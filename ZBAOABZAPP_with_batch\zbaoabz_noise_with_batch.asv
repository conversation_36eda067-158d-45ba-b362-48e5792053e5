
% run_and_plot_zbaoabz.m
clear; clc; close all;

% --- COMPILE INSTRUCTIONS ---
% Before running, make sure you have compiled the C++ code by running
% this command in your MATLAB command window:
%
% >> mex zbaoabz_with_batch_core.cpp zbaoabz_with_batch_mex.cpp
%
% This will create a file named 'zbaoabz_with_batch_core.mex...' which this script calls.

% 1) Simulation Parameters
% --- System and Integrator ---
pot       = 0;       % 0=Funnel, 1=Channel, 2=Beale
kern      = 1;       % 0=K1, 1=K2 (from paper)
grad_type = 1;       % 0=Standard, 1=Perturbed
nmax      = 1e7;
nmeas     = 1;
dtau      = 1;
gamma     = 5;
eps       = 0.1;
T         = 1;
x0        = [0, 5];
v0        = [0, 0];

% --- SamAdams Step-Size (dt) Control ---
alpha_dt  = 0.1;
scaleG_dt = 1;
m_dt      = 0.0001;  % min dt factor
M_dt      = 0.6;     % max dt factor
r_dt      = 0.5;
z0_dt     = 0;

% --- NEW: Adaptive Noise (sigma) Control ---
burn_in_noise   = 10000; % Start adapting sigma after this many steps
initial_sigma   = 4.0;   % Starting noise level
sigma_min       = 0.1;   % Min allowed noise (like a large batch)
sigma_max       = 4.0;   % Max allowed noise (like a small batch)
alpha_sigma     = 0.015;  % Memory for noise control (smaller is more memory)
threshold_sigma = 0.05;  % 5% acceptable temperature gap
steepness_sigma = 0.01;   % Steepness of the sigmoid transition

% 2) Run MEX with the new, expanded signature
%    CORRECTED: Calling the function that matches the compiled file name.
[x, y, dt_batch, psi, Umean, Tkin, Tconf, sigmas, sigma_zetas] = zbaoabz_with_batch_core( ...
    pot, kern, grad_type, nmax, nmeas, dtau, gamma, eps, T, x0, v0, ...
    alpha_dt, scaleG_dt, m_dt, M_dt, r_dt, z0_dt, ...
    burn_in_noise, initial_sigma, sigma_min, sigma_max, alpha_sigma, threshold_sigma, steepness_sigma);

% 3) Discard burn-in for plotting
burn_traj = 1e5;
idx_traj  = (burn_traj+1):numel(x);

burn_meas = burn_traj / nmeas;
if burn_meas < 1; burn_meas = 1; end % handle cases where burn_traj < nmeas
idx_meas = floor(burn_meas):numel(Umean);
meas_iters = (1:numel(Umean)) * nmeas;


% 4) Make the main figure (now with 6 panels)
f1 = figure('Position',[100 100 1500 850]);

% Trajectory
subplot(2,3,1)
plot(x(idx_traj), y(idx_traj), '.', 'MarkerSize',1)
title('Trajectory (x vs y)'); xlabel('x'); ylabel('y');
xlim([-10,10]); ylim([-10,10]); grid on;

% Δt histogram
subplot(2,3,2)
histogram(dt_batch, 100, 'Normalization','pdf')
hold on;
m_dt_val = mean(dt_batch);
% xline(m_dt_val, 'r--', 'LineWidth', 1.5, 'Label', sprintf('mean \\Delta t = %.4f', m_dt_val));
vt = xline(m_dt_val, 'r--', 'LineWidth',1.5, ...
    'Label', sprintf('mean \\Delta t = %.4f', m_dt_val), ...
    'LabelOrientation', 'horizontal', ...
    'LabelHorizontalAlignment','center', ...
    'LabelVerticalAlignment','top' ...
);
hold off;
title('Adaptive Step-size (\Delta t)'); xlabel('\Delta t'); ylabel('pdf'); grid on;

% T_conf
subplot(2,3,3)
plot(meas_iters(idx_meas), Tconf(idx_meas))
hold on; yline(T,'r--'); hold off
title('Configurational Temperature'); xlabel('Iteration'); ylabel('\langle T_{conf}\rangle');
ylim([T-0.5, T+0.5]); grid on;

% --- NEW PLOTS for Noise Control ---
% Sigma Trajectory
subplot(2,3,4)
plot(meas_iters(idx_meas), sigmas(idx_meas), 'b-')
hold on;
yline(sigma_min, 'k:'); yline(sigma_max, 'k:');
m_sigma_val = mean(sigmas(idx_meas));
hsigma = yline(m_sigma_val, 'r--', 'LineWidth',1.5, ...
    'Label', sprintf('mean noise scale \\sigma = %.4f', m_sigma_val), ...
    'LabelOrientation', 'horizontal', ...
    'LabelHorizontalAlignment','center', ...
    'LabelVerticalAlignment','top' ...
);
hold off;
title('Adaptive Noise Scale (\sigma)'); xlabel('Iteration'); ylabel('\sigma');
ylim([0, sigma_max * 1.1]); grid on;

% Sigma_Zeta Trajectory (To check for dynamic behavior)
subplot(2,3,5)
plot(meas_iters(idx_meas), sigma_zetas(idx_meas), 'r-')
m_zeta_val = mean(sigma_zetas(idx_meas));
title('Auxiliary Variable (\zeta_{\sigma})'); xlabel('Iteration'); ylabel('\zeta_{\sigma}');
grid on;

% Running Mean Potential Energy
subplot(2,3,6)
plot(meas_iters(idx_meas), Umean(idx_meas))
hold on; yline(1.1,'r--'); hold off
title('Time-averaged Potential Energy'); xlabel('Iteration'); ylabel('\langle U\rangle');
ylim([0,2]); grid on;

saveas(f1, 'neal_adaptive_noise_results.png');




% % Version before 8.2
% % run_and_plot_zbaoabz.m
% % 1) Simulation parameters
% pot    = 0;    % 0=Funnel, 1=Channel, 2=Beale
% kern   = 2;    % 0=K1,     1=K2
% grad_type = 1;     % New: 0=Standard, 1=Perturbed
% nmax   = 1e7;
% nmeas  = 100;
% dtau   = 1;
% gamma  = 5;
% alpha  = 0.1;
% eps    = 0.1;
% sigma  = 3.0;
% scaleG = 1;
% T      = 1;
% m      = 0.0001;
% M      = 0.6;
% r      = 0.5;
% x0     = [0,5];
% v0     = [0,0];
% z0     = 0;
% 
% % 2) Run MEX (all data now in x,y,dt,psi,Umean)
% % [x,y,dt,psi,Umean] = zbaoabz_mex( ...
% %     pot, kern, nmax, nmeas, dtau, gamma, alpha, eps, ...
% %     scaleG, T, m, M, r, x0, v0, z0 );
% % [x,y,dt,psi,Umean,Tkin,Tconf] = zbaoabz_core(pot, kern, grad_type, nmax, nmeas, dtau, gamma, alpha, eps, sigma, scaleG, T, m, M, r, x0, v0, z0 );
% [x,y,dt,psi,Umean,Tkin,Tconf,sigmas] = zbaoabz_with_batch_core(pot, kern, grad_type, nmax, nmeas, dtau, gamma, alpha, eps, sigma, scaleG, T, m, M, r, x0, v0, z0 );
% 
% % 3) Discard burn‐in
% burn = 1e5;
% idx  = (burn+1):numel(x);
% 
% % 4) Make the 3‐panel figure
% f1 = figure('Position',[100 100 2000 350]);
% % Trajectory
% % subplot(1,3,1)
% subplot(1,5,1)
% plot(x(idx), y(idx), '.', 'MarkerSize',1)
% title('Trajectory (x vs y)')
% xlabel('x'); ylabel('y')
% xlim([-10,10]); ylim([-10,10])
% grid on
% 
% % Δt histogram
% % subplot(1,3,2)
% % histogram(dt, 100, 'Normalization','pdf')
% % hold on
% % xline(mean(dt),'r--','Label',sprintf('mean \\Delta t=%.4f',mean(dt)));
% % hold off
% % title('Adaptive step‐size')
% % xlabel('\Delta t'); ylabel('pdf')
% % grid on
% % subplot(1,3,2)
% subplot(1,5,2)
% h = histogram(dt, 100, 'Normalization','pdf');
% hold on
% 
% m = mean(dt);
% hx = xline(m, 'r--', 'LineWidth',1.5, ...
%     'Label', sprintf('mean \\Delta t = %.4f', m), ...
%     'LabelOrientation', 'horizontal', ...
%     'LabelHorizontalAlignment','center', ...
%     'LabelVerticalAlignment','top' ...
% );
% hold off
% 
% xlabel('\Delta t'); ylabel('pdf')
% title('Adaptive step‐size')
% grid on
% 
% % Running mean 
% % subplot(1,3,3)
% subplot(1,5,3)
% plot((1:numel(Umean))*nmeas, Umean)
% hold on, yline(1.1,'r--'), hold off
% title('Time-averaged potential energy')
% xlabel('Iteration'); ylabel('\langle U\rangle')
% ylim([0,2])
% grid on
% 
% % Tkin mean
% subplot(1,5,4)
% plot((1:numel(Tkin))*nmeas, Tkin)
% hold on, yline(1,'r--'), hold off
% title('Time-averaged kinetic temperature')
% xlabel('Iteration'); ylabel('\langle T_{kin}\rangle')
% ylim([0,2])
% grid on
% 
% % Tconf mean
% subplot(1,5,5)
% plot((1:numel(Tconf))*nmeas, Tconf)
% hold on, yline(1,'r--'), hold off
% title('Time-averaged configurational temperature')
% xlabel('Iteration'); ylabel('\langle T_{conf}\rangle')
% ylim([0,2])
% grid on
% 
% % 5) Save the figure only
% saveas(f1,'neal_with_batch.png')

% % 6) Optional 2D comparison, weighted histogram via histcounts2
% nbins = 100;
% % build edges from –10 to 10, with nbins bins → nbins+1 edges
% [counts, xedges, yedges] = histcounts2( ...
%     x(idx), y(idx), ...
%     'NumBins',      [nbins nbins], ...
%     'XBinLimits',   [-10 10], ...
%     'YBinLimits',   [-10 10], ...
%     'Weights',      psi(idx), ...
%     'Normalization','pdf'  ...
% );
% 
% % plot
% figure;
% pcolor(XC,YC,counts);
% shading interp; axis square;
% xlim([-10,10]); ylim([-10,10]);
% xlabel('x'); ylabel('y');
% title('Weighted 2D histogram');
% colorbar;
% 
% % (b) Canonical PDF as before
% N = 200;
% [xg,yg] = meshgrid(linspace(-10,10,N));
% V = 0.5*xg.^2.*exp(-yg) + 0.5*eps*(xg.^2+yg.^2);
% pdf = exp(-V);
% pdf = pdf / sum(pdf(:));
% 
% subplot(1,2,2);
% pcolor(xg,yg,pdf);
% shading interp; axis square;
% xlim([-10,10]); ylim([-10,10]);
% xlabel('x'); ylabel('y');
% title('2D canonical PDF');
% colorbar;
% 
% % Save
% saveas(gcf,'zbaoabz_2d_compare.png');