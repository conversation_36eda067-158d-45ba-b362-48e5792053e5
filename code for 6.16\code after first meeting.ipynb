{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## BAOAB and perturbed_BAOAB"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for BAOAB\n", "@dataclass\n", "class BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "# parameters for perturbed BAOAB\n", "@dataclass\n", "class perturbed_BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "    xcn1: Tensor # perturbation on x (parameter for gaussian noise 1)\n", "    vcn1: Tensor # perturbation on v (parameter for gaussian noise 1)\n", "    vcn2: Tensor # perturbation on v (parameter for gaussian noise 2)\n", "\n", "# constant parameters for BAOAB\n", "def BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh/2))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-gh))\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)\n", "    \n", "# constant parameters for perturbed BAOAB\n", "def perturbed_BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh/2))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-gh))\n", "\n", "        xcn1=(hh*hh/4)*(1+eta)\n", "        vcn1=eta*(hh/2)\n", "        vcn2=(hh/2)\n", "\n", "        hc=perturbed_BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float(),xcn1=xcn1.float(),vcn1=vcn1.float(),vcn2=vcn2.float())\n", "        return(hc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### basic example as paper Sampling 3.4"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["no_batches=torch.tensor(2,device=device)      # Nb in paper\n", "batch_size=1\n", "par_runs=20000\n", "X=torch.tensor([[-1, 1], [0.5, 2.0]],device=device) #X[0,:] denotes mean, X[1, :] denotes standard deviation\n", "target_mean=(X[0,:]*(X[1,:].pow(-2))).sum()/(X[1,:].pow(-2).sum())\n", "target_sd=(X[1,:].pow(-2)).sum().pow(-0.5)\n", "l2regconst=torch.tensor(1,device=device).detach()\n", "gam=torch.sqrt(l2regconst)\n", "\n", "def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "\n", "\n", "def BAOAB_step(p,hc,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "    return(grads)     \n", "\n", "def perturbed_BAOAB_step(p,hc,batch_it,last_grad,noise_sd):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        noise1=torch.randn_like(p.data,device=device)*noise_sd\n", "        noise2=torch.randn_like(p.data,device=device)*noise_sd\n", "\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1+hc.xcn1*noise1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1+hc.vcn1*noise1+hc.vcn2*noise2\n", "\n", "    return(grads) \n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "  V1s,_=torch.sort(V1.flatten())\n", "  V2s,_=torch.sort(V2.flatten())\n", "  return (V1s-V2s).abs().mean()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def SMS_BAOAB(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SMS_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "      rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "      if i==0:\n", "        grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        grads = BAOAB_step(p, hper2c, rng_perm[:,k+1], grads)\n", "\n", "        V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "      for k in range(n_bw):\n", "        grads = BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads)\n", "\n", "        V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "      # V_epoch_arr[:,i]=V_arr.mean(dim=0)\n", "      # V_epoch_arr[:,i]=p.data.clone()\n", "\n", "  return(V_arr)\n", "\n", "def perturbed_SMS_BAOAB(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SMS_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    hper2c=perturbed_BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "      rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "      if i==0:\n", "        grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,k+1], grads, noise_sd)\n", "\n", "        V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "      for k in range(n_bw):\n", "        grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads, noise_sd)\n", "\n", "        V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "  return(V_arr)\n", "\n", "\n", "def SG_BAOAB_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      grads=BAOAB_step(p,hper2c,ind,grads)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def perturbed_SG_BAOAB_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=perturbed_BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# def SMS_BAOAB(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "  \n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "#   grads=grad(p.data,ind)       # grad(x,batch_it): return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "#   for epoch in range(num_epochs):\n", "#     if(epoch%2==0):\n", "#       rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     for i in range(no_batches):\n", "#       b=i        \n", "#       it=epoch*no_batches+b\n", "#       ind=ind_create(it)\n", "#       grads=BAOAB_step(p,hper2c,ind,grads)     \n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SMS_BAOAB(num_epochs,h,gam,noise_sd):\n", "\n", "#   p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=perturbed_BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "  \n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "#   grads=grad(p.data,ind)       # grad(x,batch_it): return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "#   for epoch in range(num_epochs):\n", "#     if(epoch%2==0):\n", "#       rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     for i in range(no_batches):\n", "#       b=i        \n", "#       it=epoch*no_batches+b\n", "#       ind=ind_create(it)\n", "#       grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)     \n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def SG_BAOAB(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "    \n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     for i in range(no_batches):\n", "#       ind=torch.randint(high=no_batches,size=(par_runs,)).int()     \n", "#       grads=BAOAB_step(p,hper2c,ind,grads)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SG_BAOAB(num_epochs,h,gam,noise_sd):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=perturbed_BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "    \n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     for i in range(no_batches):\n", "#       ind=torch.randint(high=no_batches,size=(par_runs,)).int()     \n", "#       grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def SG_BAOAB_without_replacement(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     for i in range(no_batches):\n", "#       ind=rperm[:,i]\n", "#       grads=BAOAB_step(p,hper2c,ind,grads)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SG_BAOAB_without_replacement(num_epochs,h,gam,noise_sd):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=perturbed_BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     for i in range(no_batches):\n", "#       ind=rperm[:,i]\n", "#       grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=0.01"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04094741\n", "Wasserstein distance: 0.47869724\n", "Wasserstein distance: 0.08778642\n", "Wasserstein distance: 0.04087052\n", "Wasserstein distance: 0.47890872\n", "Wasserstein distance: 0.08782548\n", "Wasserstein distance: 0.00715650\n", "Wasserstein distance: 0.12266382\n", "Wasserstein distance: 0.00868595\n", "Wasserstein distance: 0.00709229\n", "Wasserstein distance: 0.12269000\n", "Wasserstein distance: 0.00874650\n", "Wasserstein distance: 0.00190644\n", "Wasserstein distance: 0.05137049\n", "Wasserstein distance: 0.00159157\n", "Wasserstein distance: 0.00192267\n", "Wasserstein distance: 0.05124856\n", "Wasserstein distance: 0.00161765\n", "Wasserstein distance: 0.00039513\n", "Wasserstein distance: 0.02398642\n", "Wasserstein distance: 0.00033591\n", "Wasserstein distance: 0.00048710\n", "Wasserstein distance: 0.02415644\n", "Wasserstein distance: 0.00042036\n"]}], "source": ["Wass_arr=torch.zeros(4,4).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for it in range(4):\n", "    for mit in range(2):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.01)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "# for it in range(4):\n", "#     for mit in range(3):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.01)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[4.0947e-02, 4.7870e-01, 8.7786e-02, 4.0871e-02, 4.7891e-01, 8.7825e-02],\n", "        [7.1565e-03, 1.2266e-01, 8.6860e-03, 7.0923e-03, 1.2269e-01, 8.7465e-03],\n", "        [1.9064e-03, 5.1370e-02, 1.5916e-03, 1.9227e-03, 5.1249e-02, 1.6177e-03],\n", "        [3.9513e-04, 2.3986e-02, 3.3591e-04, 4.8710e-04, 2.4156e-02, 4.2036e-04]])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=0.1"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04087789\n", "Wasserstein distance: 0.47578534\n", "Wasserstein distance: 0.08781095\n", "Wasserstein distance: 0.04095888\n", "Wasserstein distance: 0.47919968\n", "Wasserstein distance: 0.08818302\n", "Wasserstein distance: 0.00722635\n", "Wasserstein distance: 0.12241393\n", "Wasserstein distance: 0.00876769\n", "Wasserstein distance: 0.00709852\n", "Wasserstein distance: 0.12259451\n", "Wasserstein distance: 0.00879736\n", "Wasserstein distance: 0.00178134\n", "Wasserstein distance: 0.05126500\n", "Wasserstein distance: 0.00166113\n", "Wasserstein distance: 0.00181238\n", "Wasserstein distance: 0.05156875\n", "Wasserstein distance: 0.00170171\n", "Wasserstein distance: 0.00047509\n", "Wasserstein distance: 0.02397298\n", "Wasserstein distance: 0.00044788\n", "Wasserstein distance: 0.00045189\n", "Wasserstein distance: 0.02382976\n", "Wasserstein distance: 0.00039319\n"]}], "source": ["# Wass_arr=torch.zeros(4,4).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for it in range(4):\n", "    # for mit in range(3):\n", "    #     rat=pow(2,it)\n", "    #     num_epochs=int(2000*rat)\n", "    #     h=torch.tensor(0.25)/rat\n", "    #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "    #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "    #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    #     Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.1)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "# for it in range(4):\n", "#     for mit in range(3):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.1)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[4.0878e-02, 4.7579e-01, 8.7811e-02, 4.0959e-02, 4.7920e-01, 8.8183e-02],\n", "        [7.2263e-03, 1.2241e-01, 8.7677e-03, 7.0985e-03, 1.2259e-01, 8.7974e-03],\n", "        [1.7813e-03, 5.1265e-02, 1.6611e-03, 1.8124e-03, 5.1569e-02, 1.7017e-03],\n", "        [4.7509e-04, 2.3973e-02, 4.4788e-04, 4.5189e-04, 2.3830e-02, 3.9319e-04]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=1"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04082059\n", "Wasserstein distance: 0.47776765\n", "Wasserstein distance: 0.08756223\n", "Wasserstein distance: 0.05660217\n", "Wasserstein distance: 0.52435035\n", "Wasserstein distance: 0.11568493\n", "Wasserstein distance: 0.00725584\n", "Wasserstein distance: 0.12265508\n", "Wasserstein distance: 0.00869231\n", "Wasserstein distance: 0.00938039\n", "Wasserstein distance: 0.13736254\n", "Wasserstein distance: 0.01871093\n", "Wasserstein distance: 0.00195998\n", "Wasserstein distance: 0.05144282\n", "Wasserstein distance: 0.00160514\n", "Wasserstein distance: 0.00491720\n", "Wasserstein distance: 0.05788900\n", "Wasserstein distance: 0.00609020\n", "Wasserstein distance: 0.00040346\n", "Wasserstein distance: 0.02403933\n", "Wasserstein distance: 0.00038692\n", "Wasserstein distance: 0.00249503\n", "Wasserstein distance: 0.02703132\n", "Wasserstein distance: 0.00293148\n"]}], "source": ["# Wass_arr=torch.zeros(4,4).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for it in range(4):\n", "    # for mit in range(3):\n", "    #     rat=pow(2,it)\n", "    #     num_epochs=int(2000*rat)\n", "    #     h=torch.tensor(0.25)/rat\n", "    #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "    #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "    #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    #     Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=1)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "# for it in range(4):\n", "#     for mit in range(3):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=1)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[4.0821e-02, 4.7777e-01, 8.7562e-02, 5.6602e-02, 5.2435e-01, 1.1568e-01],\n", "        [7.2558e-03, 1.2266e-01, 8.6923e-03, 9.3804e-03, 1.3736e-01, 1.8711e-02],\n", "        [1.9600e-03, 5.1443e-02, 1.6051e-03, 4.9172e-03, 5.7889e-02, 6.0902e-03],\n", "        [4.0346e-04, 2.4039e-02, 3.8692e-04, 2.4950e-03, 2.7031e-02, 2.9315e-03]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=2"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.11906047\n", "Wasserstein distance: 0.65309310\n", "Wasserstein distance: 0.19480605\n", "Wasserstein distance: 0.04171059\n", "Wasserstein distance: 0.17794454\n", "Wasserstein distance: 0.05260875\n", "Wasserstein distance: 0.02218124\n", "Wasserstein distance: 0.07637105\n", "Wasserstein distance: 0.02367621\n", "Wasserstein distance: 0.01170039\n", "Wasserstein distance: 0.03621376\n", "Wasserstein distance: 0.01166843\n"]}], "source": ["# Wass_arr=torch.zeros(4,4).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for it in range(4):\n", "    # for mit in range(3):\n", "    #     rat=pow(2,it)\n", "    #     num_epochs=int(2000*rat)\n", "    #     h=torch.tensor(0.25)/rat\n", "    #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "    #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "    #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    #     Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=2)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# # Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_BAOAB, SG_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "# for it in range(4):\n", "#     # for mit in range(3):\n", "#     #     rat=pow(2,it)\n", "#     #     num_epochs=int(2000*rat)\n", "#     #     h=torch.tensor(0.25)/rat\n", "#     #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#     #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#     #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#     #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#     #     Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=2)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit-3]=diff"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.1191, 0.6531, 0.1948],\n", "        [0.0417, 0.1779, 0.0526],\n", "        [0.0222, 0.0764, 0.0237],\n", "        [0.0117, 0.0362, 0.0117]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## UBU and perturbed_UBU"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for UBU\n", "@dataclass\n", "class hclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    etam1g: Tensor  # eta-1/gamma\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "# constant parameters for UBU\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc,xi1,xi2):\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "\n", "\n", "\n", "def UBU_step(p,hper2c,batch_it):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "    p.v-=hper2c.h*grads\n", "\n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def perturbed_UBU_step(p,hper2c,batch_it,noise_sd):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "        noise=torch.randn_like(p.data,device=device)*noise_sd\n", "        grads=grad(p,batch_it)*no_batches \n", "        p.v-=hper2c.h*grads+hper2c.h*noise\n", "\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "  V1s,_=torch.sort(V1.flatten())\n", "  V2s,_=torch.sort(V2.flatten())\n", "  return (V1s-V2s).abs().mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def SMS_UBU(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      UBU_step(p, hper2c, rng_perm[:,k])\n", "      V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      UBU_step(p, hper2c, rng_perm[:,Nm-1-k])\n", "      V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "    # V_epoch_arr[:,i]=V_arr.mean(dim=0)\n", "    # V_epoch_arr[:,i]=p.data.clone()\n", "\n", "  return(V_arr)\n", "\n", "def perturbed_SMS_UBU(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      perturbed_UBU_step(p, hper2c, rng_perm[:,k], noise_sd)\n", "      V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      perturbed_UBU_step(p, hper2c, rng_perm[:,Nm-1-k], noise_sd)\n", "      V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "    \n", "    # V_epoch_arr[:,i]=V_arr.mean(dim=0)      \n", "    # V_epoch_arr[:,i]=p.data.clone()\n", "  return(V_arr)\n", "\n", "def SG_UBU_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      UBU_step(p,hper2c,ind)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def perturbed_SG_UBU_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      perturbed_UBU_step(p,hper2c,ind,noise_sd)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# def SMS_UBU(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=hper2const(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     if(epoch%2==0):\n", "#       # np.title transform the [0, 1, ..., no_batches-1] to shape (par_runs, no_batches)\n", "#       # rng.permuted(, axis=1) permute the columns of the array in each row\n", "#       rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "\n", "#     for i in range(no_batches):\n", "#       b=i        \n", "#       it=epoch*no_batches+b\n", "#       ind=ind_create(it)\n", "#       UBU_step(p,hper2c,rperm[:,ind])\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SMS_UBU(num_epochs,h,gam,noise_sd):\n", "#   p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=hper2const(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     if(epoch%2==0):\n", "#       # np.title transform the [0, 1, ..., no_batches-1] to shape (par_runs, no_batches)\n", "#       # rng.permuted(, axis=1) permute the columns of the array in each row\n", "#       rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "\n", "#     for i in range(no_batches):\n", "#       b=i        \n", "#       it=epoch*no_batches+b\n", "#       ind=ind_create(it)\n", "#       perturbed_UBU_step(p,hper2c,rperm[:,ind],noise_sd)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def SG_UBU(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=hper2const(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#   for epoch in range(num_epochs):\n", "#     for i in range(no_batches):\n", "\n", "#       ind=torch.randint(high=no_batches,size=(par_runs,)).int()\n", "\n", "#       UBU_step(p,hper2c,ind)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SG_UBU(num_epochs,h,gam,noise_sd):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=hper2const(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#   for epoch in range(num_epochs):\n", "#     for i in range(no_batches):\n", "\n", "#       ind=torch.randint(high=no_batches,size=(par_runs,)).int()\n", "\n", "#       perturbed_UBU_step(p,hper2c,ind,noise_sd)\n", "    \n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "\n", "# def SG_UBU_without_replacement(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=hper2const(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#   for epoch in range(num_epochs):\n", "#     rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     for i in range(no_batches):\n", "#       ind=rperm[:,i]\n", "#       UBU_step(p,hper2c,ind)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SG_UBU_without_replacement(num_epochs,h,gam,noise_sd):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=hper2const(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#   for epoch in range(num_epochs):\n", "#     rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     for i in range(no_batches):\n", "#       ind=rperm[:,i]\n", "#       perturbed_UBU_step(p,hper2c,ind,noise_sd)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=0.01"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04123150\n", "Wasserstein distance: 0.11448739\n", "Wasserstein distance: 0.02716012\n", "Wasserstein distance: 0.04133289\n", "Wasserstein distance: 0.11459332\n", "Wasserstein distance: 0.02717442\n", "Wasserstein distance: 0.00710459\n", "Wasserstein distance: 0.04810844\n", "Wasserstein distance: 0.00608907\n", "Wasserstein distance: 0.00716362\n", "Wasserstein distance: 0.04814331\n", "Wasserstein distance: 0.00599185\n", "Wasserstein distance: 0.00220026\n", "Wasserstein distance: 0.02317319\n", "Wasserstein distance: 0.00168742\n", "Wasserstein distance: 0.00224706\n", "Wasserstein distance: 0.02299691\n", "Wasserstein distance: 0.00155922\n", "Wasserstein distance: 0.00066171\n", "Wasserstein distance: 0.01134762\n", "Wasserstein distance: 0.00051624\n", "Wasserstein distance: 0.00051188\n", "Wasserstein distance: 0.01132059\n", "Wasserstein distance: 0.00039565\n"]}], "source": ["Wass_arr=torch.zeros(4,4).detach()\n", "methods_list=[SMS_UBU, SG_UBU_without_replacement, perturbed_SMS_UBU, perturbed_SG_UBU_without_replacement]\n", "for it in range(4):\n", "    for mit in range(2):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.01)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0412, 0.1145, 0.0272, 0.0413, 0.1146, 0.0272],\n", "        [0.0071, 0.0481, 0.0061, 0.0072, 0.0481, 0.0060],\n", "        [0.0022, 0.0232, 0.0017, 0.0022, 0.0230, 0.0016],\n", "        [0.0007, 0.0113, 0.0005, 0.0005, 0.0113, 0.0004]])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=0.1"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04142142\n", "Wasserstein distance: 0.11522084\n", "Wasserstein distance: 0.02762850\n", "Wasserstein distance: 0.00713010\n", "Wasserstein distance: 0.04836661\n", "Wasserstein distance: 0.00597568\n", "Wasserstein distance: 0.00206089\n", "Wasserstein distance: 0.02317930\n", "Wasserstein distance: 0.00170126\n", "Wasserstein distance: 0.00053586\n", "Wasserstein distance: 0.01132464\n", "Wasserstein distance: 0.00041646\n"]}], "source": ["for it in range(4):\n", "    # for mit in range(3):\n", "    #     rat=pow(2,it)\n", "    #     num_epochs=int(2000*rat)\n", "    #     h=torch.tensor(0.25)/rat\n", "    #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "    #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "    #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    #     Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.1)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_UBU, SG_UBU, SG_UBU_without_replacement, perturbed_SMS_UBU, perturbed_SG_UBU, perturbed_SG_UBU_without_replacement]\n", "# for it in range(4):\n", "#     # for mit in range(3):\n", "#     #     rat=pow(2,it)\n", "#     #     num_epochs=int(2000*rat)\n", "#     #     h=torch.tensor(0.25)/rat\n", "#     #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#     #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#     #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#     #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#     #     Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.1)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0000, 0.0000, 0.0000, 0.0414, 0.1152, 0.0276],\n", "        [0.0000, 0.0000, 0.0000, 0.0071, 0.0484, 0.0060],\n", "        [0.0000, 0.0000, 0.0000, 0.0021, 0.0232, 0.0017],\n", "        [0.0000, 0.0000, 0.0000, 0.0005, 0.0113, 0.0004]])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=1"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.06054647\n", "Wasserstein distance: 0.14309835\n", "Wasserstein distance: 0.04389000\n", "Wasserstein distance: 0.00955917\n", "Wasserstein distance: 0.06083183\n", "Wasserstein distance: 0.01140498\n", "Wasserstein distance: 0.00460683\n", "Wasserstein distance: 0.02903729\n", "Wasserstein distance: 0.00498417\n", "Wasserstein distance: 0.00245498\n", "Wasserstein distance: 0.01436385\n", "Wasserstein distance: 0.00261859\n"]}], "source": ["for it in range(4):\n", "    # for mit in range(3):\n", "    #     rat=pow(2,it)\n", "    #     num_epochs=int(2000*rat)\n", "    #     h=torch.tensor(0.25)/rat\n", "    #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "    #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "    #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    #     Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=1)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_UBU, SG_UBU, SG_UBU_without_replacement, perturbed_SMS_UBU, perturbed_SG_UBU, perturbed_SG_UBU_without_replacement]\n", "# for it in range(4):\n", "#     # for mit in range(3):\n", "#     #     rat=pow(2,it)\n", "#     #     num_epochs=int(2000*rat)\n", "#     #     h=torch.tensor(0.25)/rat\n", "#     #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#     #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#     #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#     #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#     #     Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=1)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0000, 0.0000, 0.0000, 0.0605, 0.1431, 0.0439],\n", "        [0.0000, 0.0000, 0.0000, 0.0096, 0.0608, 0.0114],\n", "        [0.0000, 0.0000, 0.0000, 0.0046, 0.0290, 0.0050],\n", "        [0.0000, 0.0000, 0.0000, 0.0025, 0.0144, 0.0026]])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### noise_sd=2"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.12665330\n", "Wasserstein distance: 0.22067523\n", "Wasserstein distance: 0.10791190\n", "Wasserstein distance: 0.04165700\n", "Wasserstein distance: 0.09679680\n", "Wasserstein distance: 0.04407781\n", "Wasserstein distance: 0.02192966\n", "Wasserstein distance: 0.04690108\n", "Wasserstein distance: 0.02239938\n", "Wasserstein distance: 0.01149994\n", "Wasserstein distance: 0.02321420\n", "Wasserstein distance: 0.01156760\n"]}], "source": ["for it in range(4):\n", "    # for mit in range(3):\n", "    #     rat=pow(2,it)\n", "    #     num_epochs=int(2000*rat)\n", "    #     h=torch.tensor(0.25)/rat\n", "    #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "    #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "    #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    #     Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(2,4):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=2)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "# Wass_arr=torch.zeros(4,6).detach()\n", "# methods_list=[SMS_UBU, SG_UBU, SG_UBU_without_replacement, perturbed_SMS_UBU, perturbed_SG_UBU, perturbed_SG_UBU_without_replacement]\n", "# for it in range(4):\n", "#     # for mit in range(3):\n", "#     #     rat=pow(2,it)\n", "#     #     num_epochs=int(2000*rat)\n", "#     #     h=torch.tensor(0.25)/rat\n", "#     #     V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "#     #     V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#     #     diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#     #     print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#     #     Wass_arr[it,mit]=diff\n", "\n", "#     for mit in range(3,6):\n", "#         rat=pow(2,it)\n", "#         num_epochs=int(2000*rat)\n", "#         h=torch.tensor(0.25)/rat\n", "#         V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=2)\n", "\n", "#         V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "#         diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "#         print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "#         Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0000, 0.0000, 0.0000, 0.1267, 0.2207, 0.1079],\n", "        [0.0000, 0.0000, 0.0000, 0.0417, 0.0968, 0.0441],\n", "        [0.0000, 0.0000, 0.0000, 0.0219, 0.0469, 0.0224],\n", "        [0.0000, 0.0000, 0.0000, 0.0115, 0.0232, 0.0116]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## two dimension example\n", "$U_{\\mathrm{<PERSON>}}(x,\\theta)=\\frac{x^2}{2e^\\theta}+\\frac{\\epsilon}{2}(x^2+\\theta^2)$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BAOAB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --- global settings --------------------------------------------------\n", "dim          = 2          # <— NEW\n", "epsilon      = 0.1        # funnel strength ε\n", "par_runs     = 20_000     # number of parallel chain runs\n", "device       = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# ----------------------------------------------------------------------\n", "# 1.  Hold positions *and* velocities as (par_runs, dim) tensors\n", "p = torch.zeros(par_runs, dim,  device=device)  # positions\n", "p.v = torch.randn_like(p, device=device)  # velocities\n", "\n", "# ----------------------------------------------------------------------\n", "# 2.  Replace V and grad by the funnel versions\n", "def V(p2d, epsilon=0.1):  # q shape: (N,2)\n", "    \"\"\"\n", "    Neal-like 2D potential:\n", "        U(x, theta) = x^2/(2 e^theta) + (epsilon/2)*(x^2 + theta^2).\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x, theta = p2d[:,0], p2d[:,1]\n", "    return 0.5 * x.pow(2) * torch.exp(-theta) + 0.5 * epsilon * (x.pow(2) + theta.pow(2))\n", "\n", "def grad(p2d, epsilon=0.1):\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = x * exp(-theta) + epsilon*x\n", "      dU/dtheta = -0.5 * x^2 * exp(-theta) + epsilon * theta\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x, theta = p2d[:,0], p2d[:,1]\n", "    grad_x = x * torch.exp(-theta) + epsilon * x\n", "    grad_theta = -0.5 * x.pow(2) * torch.exp(-theta) + epsilon * theta\n", "    return torch.stack((grad_x, grad_theta), dim=1)         # shape (N,2)\n", "\n", "# ----------------------------------------------------------------------\n", "# 3.  All occurrences of torch.randn_like(p.data) automatically draw 2-D\n", "#     Gaussian noise because p is now (N,2).  No other BAOAB/UBU code\n", "#     needs to change.\n", "\n", "# ----------------------------------------------------------------------\n", "# 4.  Target “ground-truth” sampler for diagnostics\n", "#     (one simple choice: direct sampling; here we take θ ~ N(0,1/ε),\n", "#      then x | θ ~ N(0, e^{θ} / (1+ε)) )\n", "def sample_funnel(n):\n", "    theta = torch.randn(n, device=device) / epsilon.sqrt()\n", "    x     = torch.randn(n, device=device) * torch.exp(0.5*theta) / (1+epsilon).sqrt()\n", "    return torch.stack((x, theta), dim=1)\n", "\n", "\n", "def BAOAB_step(p,hc,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "        grads=grad(p,batch_it)*no_batches \n", "\n", "    # with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "    return(grads)     \n", "\n", "def perturbed_BAOAB_step(p,hc,batch_it,last_grad,noise_sd):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        noise1=torch.randn_like(p.data,device=device)*noise_sd\n", "        noise2=torch.randn_like(p.data,device=device)*noise_sd\n", "\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1+hc.xcn1*noise1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1+hc.vcn1*noise1+hc.vcn2*noise2\n", "\n", "    return(grads) \n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "  V1s,_=torch.sort(V1.flatten())\n", "  V2s,_=torch.sort(V2.flatten())\n", "  return (V1s-V2s).abs().mean()\n", "\n", "def SMS_BAOAB(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  \n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "  grads=grad(p.data,ind)       # grad(x,batch_it): return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "  for epoch in range(num_epochs):\n", "    if(epoch%2==0):\n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*no_batches+b\n", "      ind=ind_create(it)\n", "      grads=BAOAB_step(p,hper2c,ind,grads)     \n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def perturbed_SMS_BAOAB(num_epochs,h,gam,noise_sd):\n", "\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=perturbed_BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  \n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "  grads=grad(p.data,ind)       # grad(x,batch_it): return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "  for epoch in range(num_epochs):\n", "    if(epoch%2==0):\n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*no_batches+b\n", "      ind=ind_create(it)\n", "      grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)     \n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "# def SG_BAOAB(num_epochs,h,gam):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "    \n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     for i in range(no_batches):\n", "#       ind=torch.randint(high=no_batches,size=(par_runs,)).int()     \n", "#       grads=BAOAB_step(p,hper2c,ind,grads)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "# def perturbed_SG_BAOAB(num_epochs,h,gam,noise_sd):\n", "\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "#     hper2c=perturbed_BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "    \n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for epoch in range(num_epochs):\n", "\n", "#     for i in range(no_batches):\n", "#       ind=torch.randint(high=no_batches,size=(par_runs,)).int()     \n", "#       grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)\n", "\n", "#     with torch.no_grad():\n", "#       V_arr[:,epoch]=p.data\n", "    \n", "#   return(V_arr)\n", "\n", "def SG_BAOAB_without_replacement(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      ind=rperm[:,i]\n", "      grads=BAOAB_step(p,hper2c,ind,grads)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def perturbed_SG_BAOAB_without_replacement(num_epochs,h,gam,noise_sd):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=perturbed_BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      ind=rperm[:,i]\n", "      grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Wass_arr=torch.zeros(4,4).detach()\n", "methods_list=[SMS_UBU, SG_UBU_without_replacement, perturbed_SMS_UBU, perturbed_SG_UBU_without_replacement]\n", "for it in range(4):\n", "    for mit in range(3):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "    for mit in range(3,6):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam,noise_sd=0.01)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from dataclasses import dataclass\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "##############################################\n", "# 1. Define the 2D Potential and Gradient\n", "##############################################\n", "\n", "def potential_2d(states, epsilon=0.1):\n", "    \"\"\"\n", "    Neal-like 2D potential:\n", "        U(x, theta) = x^2/(2 e^theta) + (epsilon/2)*(x^2 + theta^2).\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x = states[:, 0]\n", "    theta = states[:, 1]\n", "    return 0.5 * x**2 * torch.exp(-theta) + 0.5 * epsilon * (x**2 + theta**2)\n", "\n", "def grad_potential_2d(states, epsilon=0.1):\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = x * exp(-theta) + epsilon*x\n", "      dU/dtheta = -0.5 * x^2 * exp(-theta) + epsilon * theta\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x = states[:, 0]\n", "    theta = states[:, 1]\n", "    dU_dx = x * torch.exp(-theta) + epsilon * x\n", "    dU_dtheta = -0.5 * x**2 * torch.exp(-theta) + epsilon * theta\n", "    return torch.stack([dU_dx, dU_dtheta], dim=1)\n", "\n", "\n", "##############################################\n", "# 2. Define BAOAB Coefficients (Same as 1D, but used in 2D)\n", "##############################################\n", "\n", "@dataclass\n", "class BAOABCoeffs:\n", "    \"\"\"\n", "    Holds the integrator coefficients for BAOAB.\n", "    \"\"\"\n", "    h: float      # step size\n", "    eta: float\n", "    xc1: float\n", "    xc2: float\n", "    xc3: float\n", "    vc1: float\n", "    vc2: float\n", "    vc3: float\n", "\n", "def make_baoab_coeffs(h, gamma):\n", "    \"\"\"\n", "    Create BAOAB coefficients. (Same approach as your 1D code, no change needed.)\n", "    \"\"\"\n", "    # all done in double precision, then cast to float\n", "    hh = float(h)\n", "    gam = float(gamma)\n", "    gh = gam * hh\n", "    eta = np.exp(-gh/2)\n", "\n", "    xc1 = hh / 2 * (1 + eta)\n", "    xc2 = (hh**2 / 4) * (1 + eta)\n", "    xc3 = hh / 2 * np.sqrt(-np.expm1(-gh))\n", "    vc1 = eta * (hh/2)\n", "    vc2 = (hh/2)\n", "    vc3 = np.sqrt(-np.expm1(-gh))\n", "\n", "    return BAOABCoeffs(h=hh, eta=eta, xc1=xc1, xc2=xc2, xc3=xc3,\n", "                       vc1=vc1, vc2=vc2, vc3=vc3)\n", "\n", "\n", "##############################################\n", "# 3. BAOAB Step (2D)\n", "##############################################\n", "\n", "def baoab_step(p, v, coeffs, grad_prev, gamma):\n", "    \"\"\"\n", "    One BAOAB update (unperturbed).\n", "    p, v: shape (n_samples, 2).\n", "    grad_prev: shape (n_samples, 2) from the previous iteration.\n", "    returns: new grad (for next iteration).\n", "    \"\"\"\n", "    with torch.no_grad():\n", "        # noise for position update\n", "        xi1 = torch.randn_like(p)  # shape (n_samples, 2)\n", "\n", "        # position update\n", "        p = p + coeffs.xc1 * v - coeffs.xc2 * grad_prev + coeffs.xc3 * xi1\n", "\n", "        # new gradient\n", "        grad_new = grad_potential_2d(p)\n", "\n", "        # velocity update\n", "        v = coeffs.eta * v - coeffs.vc1 * grad_prev - coeffs.vc2 * grad_new + coeffs.vc3 * xi1\n", "\n", "    return p, v, grad_new\n", "\n", "\n", "##############################################\n", "# 4. Perturbed BAOAB Step (Add gradient noise)\n", "##############################################\n", "\n", "def perturbed_baoab_step(p, v, coeffs, grad_prev, gamma, noise_sd):\n", "    \"\"\"\n", "    One BAOAB update but with Gaussian noise added to the gradient.\n", "    p, v: shape (n_samples, 2).\n", "    noise_sd: scalar float\n", "    returns: new grad (for next iteration).\n", "    \"\"\"\n", "    with torch.no_grad():\n", "        # noise for position update\n", "        xi1 = torch.randn_like(p)\n", "        # noise for gradient\n", "        gnoise1 = torch.randn_like(p) * noise_sd\n", "        gnoise2 = torch.randn_like(p) * noise_sd\n", "\n", "        # position update (including gradient perturbation)\n", "        p = p + coeffs.xc1 * v - coeffs.xc2 * grad_prev + coeffs.xc3 * xi1 + coeffs.xc2 * gnoise1\n", "\n", "        # new gradient + perturbation\n", "        grad_new = grad_potential_2d(p) + gnoise2\n", "\n", "        # velocity update\n", "        v = coeffs.eta * v \\\n", "            - coeffs.vc1 * grad_prev \\\n", "            - coeffs.vc2 * grad_new \\\n", "            + coeffs.vc3 * xi1 \\\n", "            + coeffs.vc1 * gnoise1\n", "\n", "    return p, v, grad_new\n", "\n", "\n", "##############################################\n", "# 5. Simple Distribution Distance in 2D\n", "##############################################\n", "\n", "def average_l2_distance(samples1, samples2):\n", "    \"\"\"\n", "    A simple measure of how far apart two sets of points are on average.\n", "    samples1, samples2: shape (n_samples, 2)\n", "    Return scalar.\n", "    \"\"\"\n", "    return (samples1 - samples2).norm(dim=1).mean()\n", "\n", "#\n", "# For a 'proper' 2D Wasserstein distance, consider using\n", "# the Python Optimal Transport (POT) library, e.g.\n", "#\n", "#   pip install pot\n", "#\n", "# then use ot.emd2(...) or ot.sinkhorn(...) to compute 2D Wasserstein.\n", "#\n", "\n", "##############################################\n", "# 6. Example <PERSON><PERSON><PERSON> to Run a BAOAB (or Perturbed) Chain\n", "##############################################\n", "\n", "def run_baoab(num_steps, h, gamma, noise_sd=0.0, n_chains=10000):\n", "    \"\"\"\n", "    Run either unperturbed or perturbed BAOAB on the 2D Neal potential,\n", "    for `num_steps` steps, with step size h and friction gamma.\n", "\n", "    If noise_sd > 0, we use 'perturbed_baoab_step' else 'baoab_step'.\n", "    Returns samples at each step as a 3D tensor [num_steps, n_chains, 2].\n", "    \"\"\"\n", "    # Make coefficient object\n", "    coeffs = make_baoab_coeffs(h, gamma)\n", "\n", "    # Initialize (x,theta) and velocities\n", "    p = torch.zeros(n_chains, 2, device=device)\n", "    v = torch.randn(n_chains, 2, device=device)\n", "\n", "    # pre-compute gradient\n", "    grad_prev = grad_potential_2d(p)\n", "\n", "    # Save trajectory\n", "    traj = torch.zeros(num_steps, n_chains, 2, device=device)\n", "\n", "    # Choose which stepping function\n", "    # step_fn = perturbed_baoab_step if noise_sd > 0 else baoab_step\n", "    step_fn = perturbed_baoab_step\n", "\n", "    for step in range(num_steps):\n", "        p, v, grad_prev = step_fn(p, v, coeffs, grad_prev, gamma, noise_sd)\n", "        traj[step] = p\n", "\n", "    return traj\n", "\n", "\n", "##############################################\n", "# 7. Putting It All Together:\n", "#    Testing multiple h and noise_sd\n", "##############################################\n", "\n", "def main():\n", "    # Some choices\n", "    steps_list = [1000, 2000]        # how many integrator steps to run\n", "    h_list = [0.01, 0.02, 0.05]      # step sizes\n", "    noise_list = [0.0, 0.01, 0.05, 0.1, 0.5, 1, 2]   # gradient noise\n", "    gamma = 1.0\n", "    n_chains = 5000\n", "\n", "    # For reference, let's say we want to compare to \"true\" samples\n", "    # from some known distribution. If we want to compare with\n", "    # a 'target' distribution, we can e.g. do a very long run with no perturbation:\n", "    ref_long_run = run_baoab(num_steps=50000, h=0.005, gamma=gamma, noise_sd=0.0, n_chains=n_chains)\n", "    reference_samples = ref_long_run[-1].detach()  # last step of the chain\n", "\n", "    # We'll store results\n", "    results = []\n", "\n", "    for num_steps in steps_list:\n", "        for h in h_list:\n", "            for noise_sd in noise_list:\n", "                # Run BAOAB or perturbed for (num_steps, h, noise_sd)\n", "                traj = run_baoab(num_steps, h, gamma, noise_sd=noise_sd, n_chains=n_chains)\n", "                final_samples = traj[-1].detach()  # shape (n_chains, 2)\n", "\n", "                # measure difference from reference, e.g. average L2\n", "                dist = average_l2_distance(final_samples, reference_samples)\n", "                results.append((num_steps, h, noise_sd, float(dist)))\n", "                print(f\"Steps={num_steps}, h={h}, noise_sd={noise_sd}, L2 Dist={dist:.4f}\")\n", "\n", "    # Show all results\n", "    print(\"\\nSummary of runs:\")\n", "    for (num_steps, h, nsd, dist) in results:\n", "        print(f\"  steps={num_steps}, h={h}, noise_sd={nsd}, dist={dist:.4f}\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}