% % 4) Make the main figure (now with 6 panels)
% f2 = figure('Position',[100 100 1500 850]);
% 
% % Trajectory
% subplot(2,3,1)
% plot(x(idx_traj), y(idx_traj), '.', 'MarkerSize',1)
% title('Trajectory (x vs y)'); xlabel('x'); ylabel('y');
% xlim([-10,10]); ylim([-10,10]); grid on;
% 
% % Δt histogram
% subplot(2,3,2)
% histogram(dt(idx_traj), 100, 'Normalization','pdf')
% hold on;
% m_dt_val = mean(dt(idx_traj));
% % xline(m_dt_val, 'r--', 'LineWidth', 1.5, 'Label', sprintf('mean \\Delta t = %.4f', m_dt_val));
% vt = xline(m_dt_val, 'r--', 'LineWidth',1.5, ...
%     'Label', sprintf('mean \\Delta t = %.4f', m_dt_val), ...
%     'LabelOrientation', 'horizontal', ...
%     'LabelHorizontalAlignment','center', ...
%     'LabelVerticalAlignment','top' ...
% );
% hold off;
% title('Adaptive Step-size (\Delta t)'); xlabel('\Delta t'); ylabel('pdf'); grid on;
% 
% % T_conf
% subplot(2,3,3)
% plot(meas_iters(idx_meas), Tconf(idx_meas))
% hold on; yline(T,'r--'); hold off
% title('Configurational Temperature'); xlabel('Iteration'); ylabel('\langle T_{conf}\rangle');
% ylim([T-0.5, T+0.5]); grid on;
% 
% % --- NEW PLOTS for Noise Control ---
% % Sigma Trajectory
% subplot(2,3,4)
% plot(meas_iters(idx_meas), sigmas(idx_meas), 'b-')
% hold on;
% yline(sigma_min, 'k:'); yline(sigma_max, 'k:');
% m_sigma_val = mean(sigmas(idx_meas));
% % hsigma = yline(m_sigma_val, 'r--', 'LineWidth',1.5, ...
% %     'Label', sprintf('mean noise scale \\sigma = %.4f', m_sigma_val), ...
% %     'LabelOrientation', 'horizontal', ...
% %     'LabelHorizontalAlignment','center', ...
% %     'LabelVerticalAlignment','top' ...
% % );
% hold off;
% title(sprintf('Adaptive Noise Scale (\\sigma), with mean = %.4f', m_sigma_val)); xlabel('Iteration'); ylabel('\sigma');
% ylim([0, sigma_max * 1.1]); grid on;
% 
% % Sigma_Zeta Trajectory (To check for dynamic behavior)
% subplot(2,3,5)
% plot(meas_iters(idx_meas), sigma_zetas(idx_meas), 'r-')
% title('Auxiliary Variable (\zeta_{\sigma})'); xlabel('Iteration'); ylabel('\zeta_{\sigma}');
% grid on;
% 
% % Running Mean Potential Energy
% subplot(2,3,6)
% plot(meas_iters(idx_meas), Umean(idx_meas))
% hold on; yline(1.1,'r--'); hold off
% title('Time-averaged Potential Energy'); xlabel('Iteration'); ylabel('\langle U\rangle');
% ylim([0,2]); grid on;
% 
% saveas(f2, 'batch.png');

% burn_sigma = 1e5 / nmeas;
% idx_sigma  = (burn_sigma+1):numel(sigmas);
% % 4) Make the 3‐panel figure
% f2 = plot(burn_sigma:length(sigmas), sigmas(burn_sigma:end));
% xlabel('Index (from 10001)');
% ylabel('Sigma');
% title('Plot of sigmas from index 10001 onward');
% grid on;
% saveas(f2, 'batch.png')

% % 4) Make the 3‐panel figure
% histogram(sigmas(idx_meas), 100, 'Normalization','pdf');
% vsigma = xline(m_sigma_val, 'r--', 'LineWidth',1.5, ...
%     'Label', sprintf('\\langle\\sigma\\rangle = %.4f', m_sigma_val), ...
%     'LabelOrientation', 'horizontal', ...
%     'LabelHorizontalAlignment','center', ...
%     'LabelVerticalAlignment','top' ...
% );
% xlabel('Noise Scale (\sigma)');
% ylabel('pdf');
% title('Adaptive Noise Scale (\sigma)');
% grid on;
% 
% saveas(gcf, 'sigma_histogram.png');

xlim_ = [-10 10];  ylim_ = [-10 10];
nx = 800; ny = 800;

x_ = x(idx_traj); y_ = y(idx_traj); sigmas_ = sigmas(idx_meas);

% 保证列向量；修复/重建 idx
x_ = x_(:); y_ = y_(:); sigmas_ = sigmas_(:);
if ~exist('idx','var') || numel(idx) ~= numel(x_)
    warning('idx 尺寸不匹配，将使用全部点。');
    idx = true(size(x_));
else
    idx = logical(idx(:));
end
idx = idx & isfinite(x_) & isfinite(y_) & isfinite(sigmas_);

% 只取画布范围内的数据
in = idx & (x_>=xlim_(1) & x_<=xlim_(2) & y_>=ylim_(1) & y_<=ylim_(2));
x1 = x_(in); y1 = y_(in); v = sigmas_(in);

% 分箱
xe = linspace(xlim_(1), xlim_(2), nx+1);
ye = linspace(ylim_(1), ylim_(2), ny+1);
[ix,~] = discretize(x1, xe);
[iy,~] = discretize(y1, ye);
ok = ~isnan(ix) & ~isnan(iy);
ix = ix(ok); iy = iy(ok); v = v(ok);
lin = sub2ind([ny, nx], iy, ix);
Z = accumarray(lin, v, [ny*nx 1], @max, NaN);   % 或 @mean/@median
Z = reshape(Z, ny, nx);

% ===== 在 Z 之后、imagesc 之前：分段+gamma 拉伸到 [0,1] =====
v = Z(:);
v = v(isfinite(v));

% 用分位数确定 5 段边界（0,20,40,60,80,100%）
edges = prctile(v, [0 20 40 60 80 100]);

% 轻度抑制极端值（可选；如不想抑制，删掉下一行）
edges(end) = prctile(v, 99.9);   % 顶端用 99.9% 作为 vmax

% 每段内的数据占比（用于分配 colormap 份额）
counts  = histcounts(v, edges);
portion = counts / sum(counts);

% 给份额设置上下限，避免过小/过大（可调）
portion = max(portion, 0.12);
portion = min(portion, 0.30);
portion = portion / sum(portion);       % 归一到 1

% 每段的 gamma（<1 拉开段内低值；最后一段保持 1）
gamma = [0.95 0.90 0.90 0.90 1.00];

% 累计起点
startpos = [0, cumsum(portion(1:end-1))];

% —— 将 Z 映射到 [0,1] 的“颜色空间” ——
Zmap = nan(size(Z));
for k = 1:numel(portion)
    a = edges(k); b = edges(k+1);
    mask = ~isnan(Z) & Z>=a & (k==numel(portion) | Z<b);   % 前四段左闭右开
    if any(mask(:)) && b>a
        t = (Z(mask)-a)/(b-a);     % 段内归一
        t = t.^gamma(k);           % 段内 gamma
        Zmap(mask) = startpos(k) + portion(k)*t;
    end
end

% ======= 用 Zmap 画图 =======
figure;
h = imagesc([xlim_(1) xlim_(2)], [ylim_(1) ylim_(2)], Zmap);
set(gca,'YDir','normal');
axis equal; xlim(xlim_); ylim(ylim_);
xlabel('x'); ylabel('y');
title('Noise Scale \sigma as a function of (x,y)','Interpreter','tex');
colormap(flipud(turbo));

set(gca,'Color',[1 1 1]); set(h,'AlphaData', ~isnan(Zmap));
clim([0 1]);   % 颜色空间

% ======= colorbar：用分位数作为标签，顶端留一格 =======
tick_vals = edges(1:5);                 % 0–80% 的分位数
tick_pos  = zeros(size(tick_vals));
for i = 1:numel(tick_vals)
    % 找 tick 所在段并计算在颜色空间的位置
    k = find(edges(1:end-1) <= tick_vals(i) & tick_vals(i) <= edges(2:end), 1, 'last');
    a = edges(k); b = edges(k+1);
    t = (tick_vals(i)-a)/(b-a);
    t = t.^gamma(k);
    tick_pos(i) = startpos(k) + portion(k)*t;
end
cbar = colorbar;
cbar.Ticks = [tick_pos, 1];             % 顶部再放一个刻度位置
labels = arrayfun(@(x) sprintf('%.3f', x), tick_vals, 'UniformOutput', false);
labels{end+1} = '$\sigma$';             % 顶端写 σ（或留空）
set(cbar,'TickLabelInterpreter','latex');
cbar.TickLabels = labels;

saveas(gcf,'sigma_vs_xy.png');