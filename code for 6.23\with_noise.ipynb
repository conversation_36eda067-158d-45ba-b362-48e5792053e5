{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import ot\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## $U_{\\mathrm{channel}}(x,y)=\\frac{100y^2}{1+10x^4}+0.001(x^2-9)^2$"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "def plot_traj(V_arr, burn_in, ax):\n", "    if V_arr.ndim == 2:            # single chain: shape (K, 2)\n", "        xy = V_arr[burn_in:].cpu().numpy()\n", "        ax.plot(xy[:, 0], xy[:, 1])\n", "        ax.scatter(xy[0, 0],  xy[0, 1],  marker='o', label='start')\n", "        ax.scatter(xy[-1, 0], xy[-1, 1], marker='x', label='end')\n", "\n", "        ax.set_xlabel('x')\n", "        ax.set_ylabel('y')\n", "\n", "    else:                          # many chains: shape (par_runs, K, 2)\n", "        print('Please plotting chains by yourself.')\n", "        # for run in range(V_arr.shape[0]):\n", "        #     xy = V_arr[run, burn_in:].cpu().numpy()\n", "        #     ax.plot(xy[:, 0], xy[:, 1], alpha=0.3)   # semi-transparent lines\n", "    \n", "def plot_cumulative_mean_potential(U_arr, h, burn_in, ax):\n", "    K_total = U_arr.shape[-1]\n", "    if U_arr.ndim == 1:            # single chain: shape (K, 2)\n", "        # U_arr has shape (K,)  –> (K_after, )\n", "        U_after = U_arr[burn_in:]\n", "    else:\n", "        # U_arr has shape (par_runs, K)\n", "        # First average across chains (axis 0) to get a single curve in time\n", "        U_after = U_arr[:, burn_in:].mean(dim=0)        # shape (K_after,)\n", "\n", "    steps_after = U_after.shape[0]       # K_after\n", "    cum_sum     = torch.cumsum(U_after, dim=0)   # shape (K_after,)\n", "    cum_mean    = cum_sum / torch.arange(1, steps_after+1, device=device, dtype=torch.float32)   # shape (K_after,)\n", "\n", "    h_val    = float(h)      # from the sampler call\n", "    t_axis   = h_val * torch.arange(burn_in + 1, K_total + 1, device=device, dtype=torch.float32)   # shape (K_after,)\n", "\n", "    ax.plot(t_axis.cpu().numpy(), cum_mean.cpu().numpy())\n", "    ax.set_xlabel('Time')\n", "    ax.set_ylabel(r'$\\langle U \\rangle$')\n", "\n", "def plot_mean_potential(Umean_arr, h, burn_in, ax):\n", "    K_total = Umean_arr.shape[-1]\n", "    Umean_after = Umean_arr[burn_in:]\n", "    steps_after = Umean_after.shape[0]\n", "    t_axis = h * torch.arange(burn_in + 1, K_total + 1, device=device, dtype=torch.float32)\n", "    ax.plot(t_axis.cpu().numpy(), Umean_after.cpu().numpy())\n", "    ax.set_xlabel('Time')\n", "    ax.set_ylabel(r'$\\langle U \\rangle$')\n", "\n", "def plot_x_traj(V_arr, h, burn_in, ax):\n", "    h_val    = float(h)      # from the sampler call\n", "\n", "    if V_arr.ndim == 2:            # single chain: shape (K, 2)\n", "        x_vals = V_arr[burn_in:, 0].cpu().numpy()\n", "        t_axis = h_val * np.arange(burn_in, burn_in + x_vals.size)\n", "        ax.plot(t_axis, x_vals)\n", "        ax.set_xlabel('Time')\n", "        ax.set_ylabel('x')\n", "\n", "    else:                          # many chains: shape (par_runs, K, 2)\n", "        print('Please plotting chains by yourself.')\n", "\n", "def plots(V_arr, V1_arr, V2_arr, U_arr, U1_arr, U2_arr, h, burn_in):\n", "    fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "    # Plotting the trajectories\n", "    plot_traj(V_arr, burn_in, axes[0, 0])\n", "    plot_traj(V1_arr, burn_in, axes[0, 1])\n", "    plot_traj(V2_arr, burn_in, axes[0, 2])\n", "    # Plotting the cumulative mean potential\n", "    plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "    plot_cumulative_mean_potential(U1_arr, h, burn_in, axes[1, 1])\n", "    plot_cumulative_mean_potential(U2_arr, h, burn_in, axes[1, 2])\n", "    # Plotting the x-coordinate trajectory\n", "    plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "    plot_x_traj(V1_arr, h, burn_in, axes[2, 1])\n", "    plot_x_traj(V2_arr, h, burn_in, axes[2, 2])\n", "\n", "    # axes[0,0].set_title('BAOAB')\n", "    # axes[0,1].set_title('Without noise')\n", "    # axes[0,2].set_title('With noise')\n", "    # plt.tight_layout()\n", "    # plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BAOAB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for BAOAB\n", "@dataclass\n", "class BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "def BAOAB_hconst(h,gam, T=1):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        TT=copy.deepcopy(T).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh)*TT)\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh)*TT)\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --- global settings --------------------------------------------------\n", "no_batches = torch.tensor(2, device=device)  # number of batches\n", "batch_size = 1   \n", "par_runs = 1     # number of parallel chain runs\n", "\n", "# regularisation constant used in original code just to set gamma\n", "l2regconst = torch.tensor(25.0, device=device)\n", "gam        = torch.sqrt(l2regconst)              # damping parameter γ\n", "\n", "# toy example for 2 dimensions\n", "dim          = 2          # <— NEW\n", "epsilon      = 0.1        # funnel strength ε\n", "idx_x        = 0          # column 0 = x\n", "idx_y        = 1          # column 1 = θ\n", "\n", "if par_runs > 1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # two coordinates now\n", "else:\n", "    p = torch.zeros(2, device=device)\n", "\n", "p.v = torch.randn_like(p, device=device)        # same trick as before"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def V_entropy(p2d: Tensor) -> Tensor:  # p2d shape: (N,2)\n", "    \"\"\"\n", "    Entropic Barrier 2D potential:\n", "        U(x, y) = 100y^2/(1+10x^4) + 0.001(x^2-9)^2.\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    return 100 * y.pow(2) / (1 + 10*x.pow(4)) + 0.001 * (x.pow(2) - 9).pow(2)\n", "\n", "def grad_entropy(p2d: Tensor) -> Tensor:\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = - 4000*x^3*y^2/(1+10x^4)^2 + 0.004*x*(x^2-9)\n", "      dU/dy     = 200 * y / (1 + 10x^4)\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    denom = 1 + 10*x.pow(4)\n", "    grad_x = - 4000*x.pow(3)*y.pow(2) / denom.pow(2) + 0.004*x*(x.pow(2)-9)\n", "    grad_y = 200 * y / denom\n", "    return torch.stack((grad_x, grad_y), dim=-1)   # shape (N,2)\n", "\n", "\n", "def perturbed_grad_entropy(p2d: Tensor) -> Tensor:\n", "    grad_x, grad_y = grad_entropy(p2d)\n", "    \n", "    # Step 1: Generate a random 2D vector\n", "    random_vector = torch.randn(2)\n", "    \n", "    # Step 2: Normalize the vector so that its sum is 1\n", "    normalized_vector = random_vector / random_vector.sum()\n", "    \n", "    # Step 3: Use the normalized vector to scale grad_x and grad_y\n", "    perturbed_grad_x = normalized_vector[0] * grad_x\n", "    perturbed_grad_y = normalized_vector[1] * grad_y\n", "    \n", "    return torch.stack((perturbed_grad_x, perturbed_grad_y), dim=-1)  # shape (N,2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def whole_BAOAB_step(p,hc,last_grad):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad_entropy(p)\n", "\n", "    p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "  return(grads)\n", "\n", "def whole_BAOAB(K, h, gam, T=1):\n", "  \"\"\"\n", "  K: Total samples after BAOAB\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      # U_arr = torch.zeros(K, device=device).detach()\n", "      Umean_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=BAOAB_hconst(h,gam,T=T)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    grads = grad_entropy(p.data)\n", "\n", "    for i in range(K):\n", "      grads = whole_BAOAB_step(p, hper2c, grads)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        # U_arr[i] = V_entropy(p.data).detach()\n", "        Umean_arr[i] = Umean_arr[i-1]*i/(i+1) + V_entropy(p.data).detach()/(i+1)\n", "        # if i==0:\n", "        #   Umean_arr[i] = V_entropy(p.data).detach()\n", "        # else:\n", "        #   Umean_arr[i] = (Umean_arr[i-1]*i + V_entropy(p.data).detach())/(i+1)\n", "\n", "  return (V_arr, U_arr) if par_runs>1 else (V_arr, Umean_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def perturbed_BAOAB_step(p,hc,last_grad):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=perturbed_grad_entropy(p)\n", "\n", "    p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "  return(grads)\n", "\n", "def perturbed_BAOAB(K, h, gam, T=1):\n", "  \"\"\"\n", "  K: Total samples after BAOAB\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      # U_arr = torch.zeros(K, device=device).detach()\n", "      Umean_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=BAOAB_hconst(h,gam,T=T)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    grads = perturbed_grad_entropy(p.data)\n", "\n", "    for i in range(K):\n", "      grads = perturbed_BAOAB_step(p, hper2c, grads)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        # U_arr[i] = V_entropy(p.data).detach()\n", "        Umean_arr[i] = Umean_arr[i-1]*i/(i+1) + V_entropy(p.data).detach()/(i+1)\n", "        # if i==0:\n", "        #   Umean_arr[i] = V_entropy(p.data).detach()\n", "        # else:\n", "        #   Umean_arr[i] = (Umean_arr[i-1]*i + V_entropy(p.data).detach())/(i+1)\n", "\n", "  return (V_arr, U_arr) if par_runs>1 else (V_arr, Umean_arr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### UBU"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for UBU\n", "@dataclass\n", "class hclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    etam1g: Tensor  # eta-1/gamma\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "# constant parameters for UBU\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc,xi1,xi2):\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def whole_UBU_step(p,hper2c):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    xi2=torch.randn_like(p.data,device=device)\n", "    p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=grad_entropy(p)\n", "    p.v-=hper2c.h*grads\n", "\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    xi2=torch.randn_like(p.data,device=device)\n", "    p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def whole_UBU(K, h, gam):\n", "  \"\"\"\n", "  K: Total samples after UBU\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      # U_arr = torch.zeros(K, device=device).detach()\n", "      Umean_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=hper2const(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    \n", "    for i in range(K):\n", "      whole_UBU_step(p,hper2c)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        # U_arr[i] = V_entropy(p.data).detach()\n", "        Umean_arr[i] = Umean_arr[i-1]*i/(i+1) + V_entropy(p.data).detach()/(i+1)\n", "\n", "  return (V_arr, U_arr) if par_runs>1 else (V_arr, Umean_arr)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def perturbed_UBU_step(p,hper2c):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    xi2=torch.randn_like(p.data,device=device)\n", "    p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=perturbed_grad_entropy(p)\n", "    p.v-=hper2c.h*grads\n", "\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    xi2=torch.randn_like(p.data,device=device)\n", "    p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def perturbed_UBU(K, h, gam):\n", "  \"\"\"\n", "  K: Total samples after UBU\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      # U_arr = torch.zeros(K, device=device).detach()\n", "      Umean_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=hper2const(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    \n", "    for i in range(K):\n", "      perturbed_UBU_step(p,hper2c)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        # U_arr[i] = V_entropy(p.data).detach()\n", "        Umean_arr[i] = Umean_arr[i-1]*i/(i+1) + V_entropy(p.data).detach()/(i+1)\n", "\n", "  return (V_arr, U_arr) if par_runs>1 else (V_arr, Umean_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_epochs = 10**6\n", "h = torch.tensor(0.01)\n", "T = torch.tensor(1.0)\n", "burn_in = 10**5\n", "\n", "method = \"BAOAB\"\n", "if method == \"BAOAB\":\n", "    V_arr, U_arr = whole_BAOAB(num_epochs,h,gam,T)\n", "    V1_arr, U1_arr = perturbed_BAOAB(num_epochs,h,gam,T)\n", "else:\n", "    V_arr, U_arr = whole_UBU(num_epochs,h,gam)\n", "    V1_arr, U1_arr = perturbed_UBU(num_epochs,h,gam)\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(10, 6))\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_arr, burn_in, axes[1, 0])\n", "plot_mean_potential(U_arr, h, burn_in, axes[0, 1])\n", "plot_mean_potential(U1_arr, h, burn_in, axes[1, 1])\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}