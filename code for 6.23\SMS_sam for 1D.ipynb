{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## $U_{\\mathrm{Neal}}(x,\\theta)=\\frac{x^2}{2e^\\theta}+\\frac{\\epsilon}{2}(x^2+\\theta^2)$\n", "### BAOAB"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# import matplotlib.pyplot as plt\n", "# def plot_traj(V_arr, burn_in, ax):\n", "#     if V_arr.ndim == 2:            # single chain: shape (K, 2)\n", "#         xy = V_arr[burn_in:].cpu().numpy()\n", "#         ax.plot(xy[:, 0], xy[:, 1])\n", "#         ax.scatter(xy[0, 0],  xy[0, 1],  marker='o', label='start')\n", "#         ax.scatter(xy[-1, 0], xy[-1, 1], marker='x', label='end')\n", "\n", "#         ax.set_xlabel('x')\n", "#         ax.set_ylabel('y')\n", "\n", "#     else:                          # many chains: shape (par_runs, K, 2)\n", "#         print('Please plotting chains by yourself.')\n", "#         # for run in range(V_arr.shape[0]):\n", "#         #     xy = V_arr[run, burn_in:].cpu().numpy()\n", "#         #     ax.plot(xy[:, 0], xy[:, 1], alpha=0.3)   # semi-transparent lines\n", "\n", "\n", "# def plot_mean_potential(Umean_arr, h, burn_in, ax):\n", "#     K_total = Umean_arr.shape[-1]\n", "#     Umean_after = Umean_arr[burn_in:]\n", "#     steps_after = Umean_after.shape[0]\n", "#     t_axis = h * torch.arange(burn_in + 1, K_total + 1, device=device, dtype=torch.float32)\n", "#     ax.plot(t_axis.cpu().numpy(), Umean_after, label='mean potential')\n", "#     ax.axhline(y=1, label=\"true\")\n", "#     ax.set_xlabel('Time')\n", "#     ax.set_ylabel(r'$\\langle U \\rangle$')\n", "\n", "# def plot_time_hist(t_arr, ax):\n", "#     t_arr = t_arr.cpu().numpy()\n", "#     ax.hist(t_arr, bins=50, density=True)\n", "#     ax.axvline(x=np.mean(t_arr), label='mean', color='r')\n", "#     ax.set_xlabel('stepsize')\n", "#     ax.set_ylabel('distribution')\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# --- global settings --------------------------------------------------\n", "no_batches = torch.tensor(2, device=device)  # number of batches\n", "batch_size = 1   \n", "par_runs = 1     # number of parallel chain runs\n", "\n", "# regularisation constant used in original code just to set gamma\n", "l2regconst = torch.tensor(25.0, device=device)\n", "gam        = torch.sqrt(l2regconst)              # damping parameter γ\n", "\n", "# toy example for 2 dimensions\n", "dim          = 2          # <— NEW\n", "epsilon      = 0.1        # funnel strength ε\n", "idx_x        = 0          # column 0 = x\n", "idx_theta    = 1          # column 1 = θ\n", "\n", "# if par_runs > 1:\n", "#     p = torch.zeros((par_runs, 2), device=device)   # two coordinates now\n", "# else:\n", "#     p = torch.zeros(2, device=device)\n", "\n", "p = torch.zeros(2, device=device)\n", "\n", "p.v = torch.randn_like(p, device=device)        # same trick as before"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["alpha = torch.tensor(0.1, device=device)\n", "Omega = torch.tensor(1.0, device=device)\n", "m = torch.tensor(0.01, device=device)\n", "M = torch.tensor(60.0, device=device)\n", "r = torch.tensor(0.5, device=device)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# # Replace V and grad by the funnel versions\n", "def V_neal(p2d: Tensor) -> Tensor:  # p2d shape: (N,2)\n", "    \"\"\"\n", "    Neal-like 2D potential:\n", "        U(x, theta) = x^2/(2 e^theta) + (epsilon/2)*(x^2 + theta^2).\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x, theta = p2d[...,0], p2d[...,1]\n", "    return 0.5 * x.pow(2) * torch.exp(-theta) + 0.5 * epsilon * (x.pow(2) + theta.pow(2))\n", "\n", "def grad_neal(p2d: Tensor) -> Tensor:\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = x * exp(-theta) + epsilon*x\n", "      dU/dtheta = -0.5 * x^2 * exp(-theta) + epsilon * theta\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x, theta = p2d[...,0], p2d[...,1]\n", "    grad_x = x * torch.exp(-theta) + epsilon * x\n", "    grad_theta = -0.5 * x.pow(2) * torch.exp(-theta) + epsilon * theta\n", "    return torch.stack((grad_x, grad_theta), dim=-1)   # shape (N,2)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def psi_2(zeta, m, M, r):\n", "    return m * (zeta.pow(r) + M / m) / (zeta.pow(r) + 1.0)\n", "\n", "def Z(zeta, force_norm, alpha, tau, Omega):\n", "    rho = torch.exp(-alpha * tau)\n", "    coeff = (1.0 - rho) / (Omega * alpha)\n", "    return rho * zeta + coeff * force_norm\n", "\n", "def BAOAB_coeffs(h: float, gam: float, device=device):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "        \n", "    return tuple(map(lambda v: torch.as_tensor(v, device=device),\n", "                (eta, xc1, xc2, xc3, vc1, vc2, vc3)))\n", "    \n", "def whole_BAOAB_step(p,h,gam,last_grad):\n", "    with torch.no_grad():\n", "        eta, xc1, xc2, xc3, vc1, vc2, vc3 = BAOAB_coeffs(h, gam)\n", "\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+xc1*p.v-xc2*last_grad+xc3*xi1\n", "\n", "        grads=grad_neal(p)\n", "\n", "        p.v=eta*p.v-vc1*last_grad-vc2*grads+vc3*xi1\n", "\n", "    return(grads)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ZBAOABZ_step(p, zeta, grads, force_norm, tau):\n", "    zeta = Z(zeta, force_norm, alpha, tau/2, Omega)\n", "    psi = psi_2(zeta, m, M, r)\n", "    t = psi * tau\n", "    grads = whole_BAOAB_step(p,t,gam,grads)\n", "    force_norm = torch.norm(grads, 2)\n", "    zeta = Z(zeta, force_norm, alpha, tau/2, Omega)\n", "    psi = psi_2(zeta, m, M, r)\n", "\n", "    return t, zeta, psi, grads, force_norm"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def ZBAOABZ(K, tau, n_mean):\n", "    \"\"\"\n", "    K: sampling numbers\n", "    tau: d_tau\n", "    n_mean: number of samples for energy mean\n", "    \"\"\"\n", "    p = torch.zeros(2, device=device)\n", "    p[1] = 5.0\n", "    # Initialise velocities\n", "    p.v = torch.zeros_like(p, device=device).detach()\n", "    zeta = torch.tensor(0.0, device=device)\n", "\n", "    # half_tau = tau / 2.0\n", "\n", "    # num_epochs = (K + n_mean - 1) // n_mean\n", "\n", "    with torch.no_grad():\n", "        V_arr = torch.zeros((K+1, 2), device=device).detach()  # (x, theta)\n", "        t_arr = torch.zeros(K+1, device=device).detach()       # stepsize dt\n", "        Umean_arr = np.array([])\n", "\n", "        V_arr[0,:] = p.data.clone().detach()\n", "        t_arr[0] = tau * M\n", "       \n", "        mu_store = np.array([])\n", "        U_store = torch.tensor(0.0, device=device).detach()\n", "\n", "        grads = grad_neal(p.data)\n", "        force_norm = torch.norm(grads)\n", "\n", "        for i in range(K):\n", "            if i%n_mean == 0 and i!=0:\n", "                mu_sum = np.sum(mu_store)\n", "                Umean_arr = np.append(Umean_arr, U_store.item() / mu_sum)\n", "                mu_store = np.array([])\n", "                U_store = torch.tensor(0.0, device=device).detach()\n", "\n", "            t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, grads, force_norm, tau)\n", "            V_arr[i+1,:] = p.data.clone().detach()\n", "            t_arr[i+1] = t.item()\n", "            mu_store = np.append(mu_store, psi.item())\n", "            U_store += psi * V_neal(p.data).detach()\n", "\n", "        # for i in range(K):\n", "        #     if i%n_mean == 0 and i!=0:\n", "        #         mu_sum = np.sum(mu_store)\n", "        #         Umean_arr = np.append(Umean_arr, U_store.item() / mu_sum)\n", "        #         mu_store = np.array([])\n", "        #         U_store = torch.tensor(0.0, device=device).detach()\n", "\n", "        #     if i != 0:\n", "        #         # force_norm = torch.norm(grads)\n", "        #         zeta = Z(zeta, force_norm, alpha, half_tau, Omega)\n", "        #         psi = psi_2(zeta, m, M, r)\n", "        #         t = psi * tau\n", "        #     else:\n", "        #         t = tau * M\n", "        #     t_arr[i] = t.item()\n", "\n", "        #     grads = whole_BAOAB_step(p,t,gam,grads)\n", "        #     V_arr[i,:]=p.data.clone()\n", "\n", "        #     force_norm = torch.norm(grads)\n", "        #     zeta = Z(zeta, force_norm, alpha, half_tau, Omega)\n", "        #     psi = psi_2(zeta, m, M, r)\n", "\n", "        #     mu_store = np.append(mu_store, psi.item())\n", "        #     U_store += psi * V_neal(p.data).detach()\n", "\n", "    return (V_arr, t_arr, Umean_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["K = torch.tensor(10**6)\n", "tau = torch.tensor(0.01)\n", "n_mean = torch.tensor(10**2)\n", "\n", "V_arr, t_arr, Umean_arr = ZBAOABZ(K, tau, n_mean)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["burn_in = int(10**5)\n", "fig, axes = plt.subplots(1, 3, figsize=(12, 6))\n", "\n", "xy = V_arr[burn_in:].cpu().numpy()\n", "axes[0].plot(xy[:, 0], xy[:, 1])\n", "axes[0].set_xlabel('x')\n", "axes[0].set_ylabel('theta')\n", "axes[0].set_title('SamBAOAB trajectory for <PERSON>\\'s Funnel')\n", "\n", "t_arr = t_arr.cpu().numpy()\n", "axes[1].hist(t_arr, bins=50, density=True)\n", "axes[1].axvline(x=np.mean(t_arr), label='mean', color='r')\n", "axes[1].set_xlabel('stepsize')\n", "axes[1].set_ylabel('distribution')\n", "\n", "Umean_after = Umean_arr[burn_in // n_mean:]\n", "cumulative_U = np.cumsum(Umean_after)\n", "Umean_average = cumulative_U / np.arange(1, len(cumulative_U) + 1)\n", "axes[2].plot(np.arange(Umean_average.shape[0], Umean_average))\n", "axes[2].axvline(x=1, color = 'r')\n", "axes[2].set_xlabel('Time')\n", "axes[2].set_ylabel(r'$\\langle U \\rangle$')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# burn_in = int(10**5)\n", "# fig, axes = plt.subplots(3, 1, figsize=(6, 12))\n", "# plot_traj(V_arr, burn_in, axes[0])\n", "# plot_mean_potential(Umean_arr, tau, burn_in//n_mean, axes[1])\n", "# plot_time_hist(t_arr, axes[2])"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "traj = np.loadtxt('trajectory.txt')\n", "plt.plot(traj[:, 0], traj[:, 1])\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}