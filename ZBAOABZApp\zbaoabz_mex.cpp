#include "mex.h"
#include "zbaoabz_core.hpp"

void mexFunction(int nlhs, mxArray *plhs[],
                 int nrhs, const mxArray *prhs[])
{
    // Check for the correct number of input arguments.
    if (nrhs != 18) {
        mexErrMsgIdAndTxt("MyToolbox:zbaoabz_mex:nrhs", "18 inputs required.");
    }
    if (nlhs != 8) {
        mexErrMsgIdAndTxt("MyToolbox:zbaoabz_mex:nlhs", "8 outputs required.");
    }
    // if (nlhs != 7) {
    //     mexErrMsgIdAndTxt("MyToolbox:zbaoabz_mex:nlhs", "7 outputs required.");
    // }

    // 1) Extract inputs in order:
    //    New order includes grad_type and sigma
    int idx=0;
    auto pot       = static_cast<Potential>( int(mxGetScalar(prhs[idx++])) );
    auto kern      = static_cast<Kernel>(    int(mxGetScalar(prhs[idx++])) );
    auto grad_type = static_cast<GradientType>( int(mxGetScalar(prhs[idx++])) ); // New
    int    nmax    = int(mxGetScalar(prhs[idx++]));
    int    nmeas   = int(mxGetScalar(prhs[idx++]));
    double dtau    = mxGetScalar(prhs[idx++]);
    double gamma   = mxGetScalar(prhs[idx++]);
    double alpha   = mxGetScalar(prhs[idx++]);
    double eps     = mxGetScalar(prhs[idx++]);
    double sigma   = mxGetScalar(prhs[idx++]); // New
    double scaleG  = mxGetScalar(prhs[idx++]);
    double T       = mxGetScalar(prhs[idx++]);
    double m       = mxGetScalar(prhs[idx++]);
    double M       = mxGetScalar(prhs[idx++]);
    double r       = mxGetScalar(prhs[idx++]);
    double *x0     = mxGetPr(prhs[idx++]);
    double *v0     = mxGetPr(prhs[idx++]);
    double z0      = mxGetScalar(prhs[idx++]);

    // 2) Run simulation with the new parameters
    auto R = runZBAOABZ(
      pot, kern, grad_type, nmax, nmeas, dtau, gamma, alpha, eps, sigma, scaleG, T, m, M, r,
      {x0[0], x0[1]}, {v0[0], v0[1]}, z0
    );

    // 3) Create MATLAB outputs
    mwSize N = R.x.size();
    plhs[0] = mxCreateDoubleMatrix(N,1,mxREAL); // x
    plhs[1] = mxCreateDoubleMatrix(N,1,mxREAL); // y
    plhs[2] = mxCreateDoubleMatrix(N,1,mxREAL); // dt
    plhs[3] = mxCreateDoubleMatrix(N,1,mxREAL); // psi
    mwSize Mout = R.Umean.size();
    plhs[4] = mxCreateDoubleMatrix(Mout,1,mxREAL);// Umean
    plhs[5] = mxCreateDoubleMatrix(Mout,1,mxREAL);// Tkin
    plhs[6] = mxCreateDoubleMatrix(Mout,1,mxREAL);// Tconf
    plhs[7] = mxCreateDoubleMatrix(N,1,mxREAL); // Unorm

    // 4) Copy data
    double *xo = mxGetPr(plhs[0]);
    double *yo = mxGetPr(plhs[1]);
    double *dto= mxGetPr(plhs[2]);
    double *po = mxGetPr(plhs[3]);
    double *uo = mxGetPr(plhs[4]);
    double *tko= mxGetPr(plhs[5]);
    double *tco= mxGetPr(plhs[6]);
    double *no = mxGetPr(plhs[7]); // NEW norm ptr
    for(mwSize i=0;i<N;i++){
      xo[i]=R.x[i]; yo[i]=R.y[i];
      dto[i]=R.dt[i]; po[i]=R.psi[i];
      no[i] = R.Unorm[i];          // ← copy each norm
    }
    for(mwSize i=0;i<Mout;i++) {
        uo[i]=R.Umean[i];
        tko[i]=R.Tkin[i];
        tco[i]=R.Tconf[i];
    }
}


// #include "mex.h"
// #include "zbaoabz_core.hpp"

// void mexFunction(int nlhs, mxArray *plhs[],
//                  int nrhs, const mxArray *prhs[])
// {
//     // 1) Extract inputs in order:
//     //    [pot,kern,nmax,nmeas,dtau,gamma,alpha,eps,scaleG,T,m,M,r,x0(2),v0(2),z0] = prhs...
//     int idx=0;
//     auto pot  = static_cast<Potential>( int(mxGetScalar(prhs[idx++])) );
//     auto kern = static_cast<Kernel>(    int(mxGetScalar(prhs[idx++])) );
//     int  nmax  = int(mxGetScalar(prhs[idx++]));
//     int  nmeas = int(mxGetScalar(prhs[idx++]));
//     double dtau   = mxGetScalar(prhs[idx++]);
//     double gamma  = mxGetScalar(prhs[idx++]);
//     double alpha  = mxGetScalar(prhs[idx++]);
//     double eps    = mxGetScalar(prhs[idx++]);
//     double scaleG = mxGetScalar(prhs[idx++]);
//     double T      = mxGetScalar(prhs[idx++]);
//     double m      = mxGetScalar(prhs[idx++]);
//     double M      = mxGetScalar(prhs[idx++]);
//     double r      = mxGetScalar(prhs[idx++]);
//     double *x0    = mxGetPr(prhs[idx++]);
//     double *v0    = mxGetPr(prhs[idx++]);
//     double z0     = mxGetScalar(prhs[idx++]);

//     // 2) Run simulation
//     auto R = runZBAOABZ(
//       pot,kern,nmax,nmeas,dtau,gamma,alpha,eps,scaleG,T,m,M,r,
//       {x0[0], x0[1]}, {v0[0], v0[1]}, z0
//     );

//     // 3) Create MATLAB outputs
//     mwSize N = R.x.size();
//     plhs[0] = mxCreateDoubleMatrix(N,1,mxREAL); // x
//     plhs[1] = mxCreateDoubleMatrix(N,1,mxREAL); // y
//     plhs[2] = mxCreateDoubleMatrix(N,1,mxREAL); // dt
//     plhs[3] = mxCreateDoubleMatrix(N,1,mxREAL); // psi
//     mwSize Mout = R.Umean.size();
//     plhs[4] = mxCreateDoubleMatrix(Mout,1,mxREAL);// Umean

//     // 4) Copy data
//     double *xo = mxGetPr(plhs[0]);
//     double *yo = mxGetPr(plhs[1]);
//     double *dto= mxGetPr(plhs[2]);
//     double *po = mxGetPr(plhs[3]);
//     double *uo = mxGetPr(plhs[4]);
//     for(mwSize i=0;i<N;i++){
//       xo[i]=R.x[i]; yo[i]=R.y[i];
//       dto[i]=R.dt[i]; po[i]=R.psi[i];
//     }
//     for(mwSize i=0;i<Mout;i++) uo[i]=R.Umean[i];
// }
