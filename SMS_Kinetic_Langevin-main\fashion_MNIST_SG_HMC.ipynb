{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "bGU6NwlsXFSt"}, "outputs": [], "source": ["#@title Import Dependencies\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.datasets as dsets\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import itertools\n", "import pickle\n", "import numpy as np\n", "from numpy import random\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "#from matplotlib import pyplot as plt\n", "#import matplotlib\n", "from typing import TypeVar, Dict\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.optim.optimizer import Optimizer\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting scipy\n", "  Downloading scipy-1.15.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (61 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.0/62.0 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2.5,>=1.23.5 in /usr/local/lib/python3.10/dist-packages (from scipy) (1.24.1)\n", "Downloading scipy-1.15.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (37.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m37.6/37.6 MB\u001b[0m \u001b[31m153.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: scipy\n", "Successfully installed scipy-1.15.2\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpython -m pip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install scipy"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["\n", "input_size = 28*28*1 # img_size = (28,28) ---> 28*28=784 in total\n", "batch_size = 200 # the size of input data took for one iteration"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "lCsBCXMwbpH5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-images-idx3-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-images-idx3-ubyte.gz to ./data/FashionMNIST/raw/train-images-idx3-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 26421880/26421880 [00:06<00:00, 3986876.39it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./data/FashionMNIST/raw/train-images-idx3-ubyte.gz to ./data/FashionMNIST/raw\n", "\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-labels-idx1-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/train-labels-idx1-ubyte.gz to ./data/FashionMNIST/raw/train-labels-idx1-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 29515/29515 [00:00<00:00, 108566.10it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./data/FashionMNIST/raw/train-labels-idx1-ubyte.gz to ./data/FashionMNIST/raw\n", "\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-images-idx3-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-images-idx3-ubyte.gz to ./data/FashionMNIST/raw/t10k-images-idx3-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 4422102/4422102 [00:02<00:00, 1478991.00it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./data/FashionMNIST/raw/t10k-images-idx3-ubyte.gz to ./data/FashionMNIST/raw\n", "\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-labels-idx1-ubyte.gz\n", "Downloading http://fashion-mnist.s3-website.eu-central-1.amazonaws.com/t10k-labels-idx1-ubyte.gz to ./data/FashionMNIST/raw/t10k-labels-idx1-ubyte.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5148/5148 [00:00<00:00, 5351245.85it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./data/FashionMNIST/raw/t10k-labels-idx1-ubyte.gz to ./data/FashionMNIST/raw\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["transform=transforms.Compose([transforms.ToTensor()])\n", "\n", "train_data = dsets.FashionMNIST(root = './data', train=True, transform = transform, download = True)\n", "test_data = dsets.FashionMNIST(root = './data', train=False, transform = transform, download = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "rfDPBdnYgfGp"}, "outputs": [], "source": ["#@title Loading the data\n", "\n", "train_gen = torch.utils.data.DataLoader(dataset = train_data,\n", "                                             batch_size = batch_size,\n", "                                             shuffle = True)\n", "\n", "test_gen = torch.utils.data.DataLoader(dataset = test_data,\n", "                                      batch_size = batch_size,\n", "                                      shuffle = False)\n", "\n", "no_batches=len(train_gen)\n", "test_no_batches=len(test_gen)\n", "train_data_len=len(train_data)\n", "test_data_len=len(test_data)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["#cvmx=torch.zeros([3*64*64,3*64*64],device=device)\n", "images_list=[]\n", "labels_list=[]\n", "no_batches=len(train_gen)\n", "#images_mean=torch.zeros(3,64,64,device=device)\n", "for i ,(images,labels) in enumerate(train_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    # images_mean=images_mean+images.mean(0)\n", "    # im=torch.reshape(images,[images.shape[0],3*64*64])\n", "    # cvmx+=torch.matmul(torch.transpose(im,0,1),im)\n", "    if(i<(len(train_gen))):\n", "        images_list.append(images)\n", "        labels_list.append(labels)\n", "\n", "\n", "\n", "test_images_list=[]\n", "test_labels_list=[]\n", "test_no_batches=len(test_gen)\n", "for i ,(images,labels) in enumerate(test_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    if(i<(len(test_gen))):\n", "        test_images_list.append(images)\n", "        test_labels_list.append(labels)\n", "\n", "train_data_len=len(train_data)\n", "test_data_len=len(test_data)\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "fL-Y<PERSON><PERSON><PERSON><PERSON>_"}, "outputs": [], "source": ["#@title Define model class\n", "import torch.nn as nn\n", "import torch\n", "import torch.nn.functional as F\n", "\n", "from typing import TypeVar, <PERSON><PERSON>\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "class NeuralNet(torch.nn.Module):\n", "    \"\"\"\n", "    base class for all NN classifiers\n", "    \"\"\"\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def initialize_weights(self):\n", "        for m in self.modules():\n", "            if isinstance(m, torch.nn.Conv2d):\n", "                torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None:\n", "                    torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.BatchNorm2d):\n", "                torch.nn.init.constant_(m.weight, 1)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.Linear):\n", "                torch.nn.init.normal_(m.weight, 0, 0.01)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "\n", "\n", "class CNN(NeuralNet):\n", "    \"\"\"\n", "    CNN for (binary) classification for CelebA, CheXpert\n", "    \"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 3,\n", "                 num_classes: int = 2,\n", "                 flattened_size: int = 16384,\n", "                 low_rank: int = 32,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(CNN, self).__init__()\n", "\n", "        self.conv_layer = nn.Sequential(\n", "\n", "            # Conv Layer block 1\n", "            nn.Conv2d(in_channels=in_channels, out_channels=32, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(32,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "            nn.BatchNorm2d(64,momentum=batch_norm_mom),\n", "\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(64,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(128,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=128, out_channels=128, kernel_size=3, padding=1),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "            #nn.BatchNorm2d(128),\n", "\n", "            # nn.BatchNorm2d(128),\n", "            # nn.Conv2d(in_channels=128, out_channels=128, kernel_size=3, padding=1),\n", "            # nn.Softplus(beta=1.0),\n", "            # nn.MaxPool2d(kernel_size=2, stride=2),\n", "            # nn.BatchNorm2d(128),\n", "\n", "            # # Conv Layer block 2\n", "            # nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1),\n", "            # nn.BatchNorm2d(128),\n", "            # nn.Softplus(beta=1.0),\n", "            # nn.BatchNorm2d(128),\n", "            # nn.Conv2d(in_channels=128, out_channels=128, kernel_size=3, padding=1),\n", "            # nn.Softplus(beta=1.0),\n", "            # nn.MaxPool2d(kernel_size=2, stride=2),\n", "            # nn.BatchNorm2d(128),\n", "\n", "            # Conv Layer block 3\n", "            nn.BatchNorm2d(128,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=128, out_channels=256, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(256,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=256, out_channels=256, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "        )\n", "\n", "        self.fc_layer = nn.Sequential(\n", "            #nn.Dropout(p=0.1),\n", "            nn.BatchNorm1d(flattened_size,momentum=batch_norm_mom),\n", "#            nn.Linear(flattened_size, 512),\n", "            nn.Linear(flattened_size, low_rank),\n", "            nn.BatchNorm1d(low_rank,momentum=batch_norm_mom),\n", "            nn.<PERSON><PERSON>(low_rank,512),            \n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm1d(512,momentum=batch_norm_mom),\n", "            # nn.<PERSON>(2048, 512),\n", "            # nn.Softplus(beta=1.0),\n", "            # nn.BatchNorm1d(512),\n", "            # nn.<PERSON>(1024, 512),\n", "            # nn.Softplus(beta=1.0), \n", "            # nn.BatchNorm1d(512),\n", "            #nn.Dropout(p=0.1),\n", "        )\n", "\n", "        self.last_layer=nn.Sequential(\n", "            nn.Linear(512, num_classes)\n", "        )\n", "\n", "    def forward(self, x: Tensor) -> Tensor:\n", "        \"\"\"Perform forward.\"\"\"\n", "\n", "        # conv layers\n", "        x = self.conv_layer(x)\n", "\n", "        # flatten\n", "        x = x.view(x.size(0), -1)\n", "\n", "        # # # fc layer\n", "        x = self.fc_layer(x)\n", "\n", "        x=self.last_layer(x)\n", "\n", "        return x\n", "\n", "    def classify(self, x: Tensor) -> <PERSON><PERSON>[Tensor, Tensor, Tensor]:\n", "        net_out = self.forward(x)\n", "        acc = F.softmax(net_out, dim=1)\n", "        class_idx = torch.max(net_out, 1)[1]\n", "\n", "        return acc, acc[0, class_idx], class_idx\n", "    \n", "\n", "\n", "\n", "\n", "class CelebA_CNN(CNN):\n", "    \"\"\"CNN.\"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 3,\n", "                 num_classes: int = 2,\n", "                 #num_classes: int = 1,\n", "                 flattened_size: int = 16384):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(CelebA_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                         flattened_size=flattened_size)\n", "\n", "class Fashion_MNIST_CNN(CNN):\n", "    \"\"\"CNN.\"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 1,\n", "                 num_classes: int = 10,\n", "                 #flattened_size: int = 6272,\n", "                 flattened_size: int = 2304,\n", "                 low_rank: int = 64,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(Fashion_MNIST_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                         flattened_size=flattened_size)\n", "\n", "\n", "\n", "\n", "class CheXpert_CNN(CNN):\n", "    def __init__(self,\n", "                 in_channels: int = 1,\n", "                 num_classes: int = 2,\n", "                 flattened_size: int = 65536):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(Che<PERSON>pert_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                           flattened_size=flattened_size)\n", "\n", "\n", "class Net(nn.Module):\n", "  def __init__(self, input_size, hidden_size, num_classes):\n", "    super(Net,self).__init__()\n", "    self.fc1 = nn.Linear(input_size, hidden_size)\n", "    self.relu = nn.ReLU()\n", "    self.fc2 = nn.Linear(hidden_size, num_classes)\n", "\n", "  def forward(self,x):\n", "    out = self.fc1(x)\n", "    out = self.relu(out)\n", "    out = self.fc2(out)\n", "    return out"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# net=Fashion_MNIST_CNN()\n", "# n=0\n", "# for par in net.parameters():\n", "#     n+=par.numel()\n", "\n", "# n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "ePLIwvAFj2zH"}, "outputs": [], "source": ["#@title Define loss-function & optimizer\n", "loss_function = nn.CrossEntropyLoss()\n", "\n", "\n", "def images_regulariser(): \n", "    li_reg_loss = 0\n", "    penalized     = [p for name,p in net.named_parameters() if 'bias' not in name]\n", "    not_penalized = [p for name,p in net.named_parameters() if 'bias' in name]\n", "    for p in penalized:\n", "        li_reg_loss += (p**2).sum()*0.5\n", "    #for p in net.parameters():\n", "#        li_reg_loss += (p**2).sum()*0.5\n", "    reg=li_reg_loss/(train_data_len)*l2regconst\n", "    return(reg)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def addnet(net,net2):\n", "    for param1, param2 in zip(net.parameters(), net2.parameters()):\n", "     param1.data += param2.data\n", "\n", "def multiplynet(net,a):\n", "   for param1 in net.parameters():\n", "     param1.data *=a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "@dataclass\n", "class hclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    etam1g: Tensor\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc):\n", "    xi1=torch.randn(x.size(),device=device)\n", "    xi2=torch.randn(x.size(),device=device)\n", "\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n", "\n", "def bounce(x,v,xstar,width):\n", "    vsign=(((x-xstar+width)/(2*width)).floor()% 2)*(-2)+1\n", "    vn=v*vsign\n", "    xn=((x-xstar-width)% (4*width)-2*width).abs()-width+xstar\n", "    # num_outside=((xn-xstar)>width).sum()+((xstar-xn)>width).sum()\n", "    # if(num_outside>0):\n", "    #     print(num_outside)    \n", "    return([xn, vn])\n", "\n", "def bouncenet():\n", "    for p,p_star in zip(net.parameters(),net_star.parameters()):\n", "        [p.data, p.v]=bounce(p.data, p.v, p_star.data, 6/torch.sqrt(l2regconst_extra))\n", "\n", "def svrg_grad(net, batch_it):\n", "    outputsU = net(images_list[batch_it])\n", "    loss_likelihood = loss_function(outputsU, labels_list[batch_it])  \n", "\n", "\n", "    grads_reg=[torch.zeros_like(par) for par in net.parameters()]\n", "    net_pars=list(net.parameters())\n", "    with torch.no_grad():\n", "        for it in range(len_params):\n", "            if(list_no_bias[it]):\n", "                grads_reg[it]=net_pars[it].data*l2regconst\n", "\n", "    net.zero_grad()\n", "    loss_likelihood.backward()\n", "    with torch.no_grad():\n", "        grads_likelihood=[par.grad*batch_size for par in net.parameters()]\n", "\n", "        svrg_grads=[]\n", "        for p,grad,grad_reg,p_star,grad_star,star_sum_grad in zip(list(net.parameters()),grads_likelihood,grads_reg,list(net_star.parameters()),net_star_grad_list[batch_it],net_star_full_grad):              \n", "            svrg_grads.append(grad_reg+star_sum_grad+(grad-grad_star)*no_batches+l2regconst_extra*(p.data-p_star.data))\n", "    return svrg_grads,loss_likelihood.data\n", "\n", "def EM_step(net, h, gam,batch_it):   \n", "    svrg_grads,loss_likelihood_data=svrg_grad(net, batch_it)   \n", "\n", "    with torch.no_grad():\n", "        for p,gradp in zip(net.parameters(), svrg_grads):              \n", "            p.xi=torch.randn_like(p.data,device=device)\n", "            p.data+=p.v*h\n", "            p.v-=(h)*gradp+gam*(h)*p.v-torch.sqrt(2*gam*h)*p.xi\n", "\n", "    return(loss_likelihood_data)\n", "\n", "def UBU_step(hper2c,images,labels,batch_it):   \n", "    with torch.no_grad():\n", "        for p in list(net.parameters()):\n", "\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "\n", "    outputsU = net(images)\n", "    loss_likelihood = loss_function(outputsU, labels)  \n", "\n", "\n", "    grads_reg=[torch.zeros_like(par) for par in net.parameters()]\n", "    net_pars=list(net.parameters())\n", "    with torch.no_grad():\n", "        for it in range(len_params):\n", "            if(list_no_bias[it]):\n", "                grads_reg[it]=net_pars[it].data*l2regconst\n", "\n", "    net.zero_grad()\n", "    loss_likelihood.backward()\n", "    with torch.no_grad():\n", "        grads_likelihood=[par.grad*batch_size for par in net.parameters()]\n", "    \n", "        #Normal, no variance reduction\n", "        # for p,p_star in zip(net.parameters(),net_star.parameters()):      \n", "        #     p.v-=hper2c.h*(p.grad*train_data_len+l2regconst_extra*(p.data-p_star.data))\n", "\n", "        for p,grad,grad_reg,p_star,grad_star,star_sum_grad in zip(list(net.parameters()),grads_likelihood,grads_reg,list(net_star.parameters()),net_star_grad_list[batch_it],net_star_full_grad):              \n", "            #Using variance reduction\n", "            p.v-=hper2c.h*(grad_reg+star_sum_grad+(grad-grad_star)*no_batches+l2regconst_extra*(p.data-p_star.data))\n", "\n", "        for p in list(net.parameters()):\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "    #bouncenet()\n", "    return(loss_likelihood.data)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "u75Xa5VckuTH"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["par_it: 0 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 84.892 %\n", "Epoch [1], Average Loss: 0.5767\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 88.511 %\n", "Epoch [2], Average Loss: 0.3285\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.601 %\n", "Epoch [3], Average Loss: 0.2582\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.251 %\n", "Epoch [4], Average Loss: 0.2237\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.841 %\n", "Epoch [5], Average Loss: 0.1967\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.371 %\n", "Epoch [6], Average Loss: 0.1829\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.861 %\n", "Epoch [7], Average Loss: 0.1620\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 91.901 %\n", "Epoch [8], Average Loss: 0.1574\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.331 %\n", "Epoch [9], Average Loss: 0.1352\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.421 %\n", "Epoch [10], Average Loss: 0.1171\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.751 %\n", "Epoch [11], Average Loss: 0.0959\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.751 %\n", "Epoch [12], Average Loss: 0.0751\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.201 %\n", "Epoch [13], Average Loss: 0.0559\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.981 %\n", "Epoch [14], Average Loss: 0.0315\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 92.851 %\n", "Epoch [15], Average Loss: 0.0236\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.881 %\n", "Epoch [16], Average Loss: 0.0164\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.921 %\n", "Epoch [17], Average Loss: 0.0170\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.941 %\n", "Epoch [18], Average Loss: 0.0153\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 93.011 %\n", "Epoch [19], Average Loss: 0.0132\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.501 %\n", "Epoch [20], Average Loss: 0.0131\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.701 %\n", "Epoch [21], Average Loss: 0.0050\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 92.041 %\n", "Epoch [22], Average Loss: 0.0512\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.361 %\n", "Epoch [23], Average Loss: 0.1434\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.221 %\n", "Epoch [24], Average Loss: 0.2028\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 89.291 %\n", "Epoch [25], Average Loss: 0.2227\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.271 %\n", "Epoch [26], Average Loss: 0.2290\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.681 %\n", "Epoch [27], Average Loss: 0.2259\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.911 %\n", "Epoch [28], Average Loss: 0.2450\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.531 %\n", "Epoch [29], Average Loss: 0.2726\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.141 %\n", "Epoch [30], Average Loss: 0.2316\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.261 %\n", "Epoch [31], Average Loss: 0.2124\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 89.821 %\n", "Epoch [32], Average Loss: 0.2223\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.041 %\n", "Epoch [33], Average Loss: 0.2381\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.821 %\n", "Epoch [34], Average Loss: 0.2273\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 91.211 %\n", "Epoch [35], Average Loss: 0.2506\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.081 %\n", "Epoch [36], Average Loss: 0.2485\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.021 %\n", "Epoch [37], Average Loss: 0.2313\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.731 %\n", "Epoch [38], Average Loss: 0.2327\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.961 %\n", "Epoch [39], Average Loss: 0.2295\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.051 %\n", "Epoch [40], Average Loss: 0.2064\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.901 %\n", "Epoch [41], Average Loss: 0.2234\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.011 %\n", "Epoch [42], Average Loss: 0.2356\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.561 %\n", "Epoch [43], Average Loss: 0.2282\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [44], Average Loss: 0.2393\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 89.231 %\n", "Epoch [45], Average Loss: 0.2483\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.251 %\n", "Epoch [46], Average Loss: 0.2661\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.351 %\n", "Epoch [47], Average Loss: 0.2475\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.661 %\n", "Epoch [48], Average Loss: 0.2257\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.651 %\n", "Epoch [49], Average Loss: 0.2258\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 90.071 %\n", "Epoch [50], Average Loss: 0.2397\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.391 %\n", "Epoch [51], Average Loss: 0.2193\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.801 %\n", "Epoch [52], Average Loss: 0.2109\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.381 %\n", "Epoch [53], Average Loss: 0.1958\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.991 %\n", "Epoch [54], Average Loss: 0.2101\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.191 %\n", "Epoch [55], Average Loss: 0.2272\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.601 %\n", "Epoch [56], Average Loss: 0.2393\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 87.911 %\n", "Epoch [57], Average Loss: 0.2588\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.681 %\n", "Epoch [58], Average Loss: 0.2636\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.571 %\n", "Epoch [59], Average Loss: 0.2311\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 89.981 %\n", "Epoch [60], Average Loss: 0.2199\n", "par_it: 1 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 86.971 %\n", "Epoch [1], Average Loss: 0.5052\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 89.281 %\n", "Epoch [2], Average Loss: 0.2915\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.081 %\n", "Epoch [3], Average Loss: 0.2387\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.121 %\n", "Epoch [4], Average Loss: 0.2028\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.341 %\n", "Epoch [5], Average Loss: 0.1858\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.011 %\n", "Epoch [6], Average Loss: 0.1722\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.111 %\n", "Epoch [7], Average Loss: 0.1529\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.271 %\n", "Epoch [8], Average Loss: 0.1455\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 91.821 %\n", "Epoch [9], Average Loss: 0.1297\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.031 %\n", "Epoch [10], Average Loss: 0.1093\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.391 %\n", "Epoch [11], Average Loss: 0.0937\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.901 %\n", "Epoch [12], Average Loss: 0.0691\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.001 %\n", "Epoch [13], Average Loss: 0.0437\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.771 %\n", "Epoch [14], Average Loss: 0.0300\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.091 %\n", "Epoch [15], Average Loss: 0.0160\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.731 %\n", "Epoch [16], Average Loss: 0.0172\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.731 %\n", "Epoch [17], Average Loss: 0.0109\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.831 %\n", "Epoch [18], Average Loss: 0.0107\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.971 %\n", "Epoch [19], Average Loss: 0.0116\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.491 %\n", "Epoch [20], Average Loss: 0.0086\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.571 %\n", "Epoch [21], Average Loss: 0.0038\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.931 %\n", "Epoch [22], Average Loss: 0.0471\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.161 %\n", "Epoch [23], Average Loss: 0.1379\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.421 %\n", "Epoch [24], Average Loss: 0.1726\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 90.881 %\n", "Epoch [25], Average Loss: 0.2074\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.241 %\n", "Epoch [26], Average Loss: 0.2027\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.471 %\n", "Epoch [27], Average Loss: 0.2053\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.001 %\n", "Epoch [28], Average Loss: 0.2503\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.591 %\n", "Epoch [29], Average Loss: 0.2592\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.951 %\n", "Epoch [30], Average Loss: 0.2394\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.661 %\n", "Epoch [31], Average Loss: 0.2357\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.211 %\n", "Epoch [32], Average Loss: 0.2465\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.751 %\n", "Epoch [33], Average Loss: 0.2452\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.431 %\n", "Epoch [34], Average Loss: 0.2366\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 90.271 %\n", "Epoch [35], Average Loss: 0.2413\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.951 %\n", "Epoch [36], Average Loss: 0.2379\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.021 %\n", "Epoch [37], Average Loss: 0.2384\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.531 %\n", "Epoch [38], Average Loss: 0.2584\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [39], Average Loss: 0.2553\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 89.761 %\n", "Epoch [40], Average Loss: 0.2378\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.951 %\n", "Epoch [41], Average Loss: 0.2292\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.681 %\n", "Epoch [42], Average Loss: 0.2176\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.711 %\n", "Epoch [43], Average Loss: 0.2118\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.761 %\n", "Epoch [44], Average Loss: 0.2134\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 89.961 %\n", "Epoch [45], Average Loss: 0.2122\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.701 %\n", "Epoch [46], Average Loss: 0.2431\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.841 %\n", "Epoch [47], Average Loss: 0.2314\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.861 %\n", "Epoch [48], Average Loss: 0.2301\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.211 %\n", "Epoch [49], Average Loss: 0.2382\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 89.211 %\n", "Epoch [50], Average Loss: 0.2363\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.321 %\n", "Epoch [51], Average Loss: 0.2444\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.961 %\n", "Epoch [52], Average Loss: 0.2268\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.971 %\n", "Epoch [53], Average Loss: 0.2202\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.221 %\n", "Epoch [54], Average Loss: 0.2226\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.711 %\n", "Epoch [55], Average Loss: 0.1994\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.671 %\n", "Epoch [56], Average Loss: 0.2067\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.761 %\n", "Epoch [57], Average Loss: 0.2247\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.971 %\n", "Epoch [58], Average Loss: 0.2175\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.391 %\n", "Epoch [59], Average Loss: 0.2337\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 89.981 %\n", "Epoch [60], Average Loss: 0.2224\n", "par_it: 2 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 82.362 %\n", "Epoch [1], Average Loss: 0.5583\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 88.921 %\n", "Epoch [2], Average Loss: 0.3138\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 89.901 %\n", "Epoch [3], Average Loss: 0.2464\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.451 %\n", "Epoch [4], Average Loss: 0.2157\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.481 %\n", "Epoch [5], Average Loss: 0.1954\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.411 %\n", "Epoch [6], Average Loss: 0.1814\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.891 %\n", "Epoch [7], Average Loss: 0.1630\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.061 %\n", "Epoch [8], Average Loss: 0.1485\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.281 %\n", "Epoch [9], Average Loss: 0.1355\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.081 %\n", "Epoch [10], Average Loss: 0.1151\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.071 %\n", "Epoch [11], Average Loss: 0.1020\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.801 %\n", "Epoch [12], Average Loss: 0.0851\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.521 %\n", "Epoch [13], Average Loss: 0.0558\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.601 %\n", "Epoch [14], Average Loss: 0.0405\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 92.741 %\n", "Epoch [15], Average Loss: 0.0281\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.811 %\n", "Epoch [16], Average Loss: 0.0210\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.611 %\n", "Epoch [17], Average Loss: 0.0154\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.241 %\n", "Epoch [18], Average Loss: 0.0188\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.701 %\n", "Epoch [19], Average Loss: 0.0153\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.181 %\n", "Epoch [20], Average Loss: 0.0169\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.811 %\n", "Epoch [21], Average Loss: 0.0067\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.691 %\n", "Epoch [22], Average Loss: 0.0532\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.671 %\n", "Epoch [23], Average Loss: 0.1316\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 89.111 %\n", "Epoch [24], Average Loss: 0.1927\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 89.971 %\n", "Epoch [25], Average Loss: 0.2207\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.021 %\n", "Epoch [26], Average Loss: 0.2306\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.271 %\n", "Epoch [27], Average Loss: 0.2251\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.291 %\n", "Epoch [28], Average Loss: 0.2341\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 87.181 %\n", "Epoch [29], Average Loss: 0.2629\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 90.641 %\n", "Epoch [30], Average Loss: 0.2768\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.441 %\n", "Epoch [31], Average Loss: 0.2383\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 89.531 %\n", "Epoch [32], Average Loss: 0.2384\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.121 %\n", "Epoch [33], Average Loss: 0.2614\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.391 %\n", "Epoch [34], Average Loss: 0.2813\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 88.341 %\n", "Epoch [35], Average Loss: 0.2635\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.931 %\n", "Epoch [36], Average Loss: 0.2579\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.701 %\n", "Epoch [37], Average Loss: 0.2333\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.541 %\n", "Epoch [38], Average Loss: 0.2188\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.451 %\n", "Epoch [39], Average Loss: 0.2264\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 89.201 %\n", "Epoch [40], Average Loss: 0.2393\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.741 %\n", "Epoch [41], Average Loss: 0.2790\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.681 %\n", "Epoch [42], Average Loss: 0.3029\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.971 %\n", "Epoch [43], Average Loss: 0.2634\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.381 %\n", "Epoch [44], Average Loss: 0.2330\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 90.611 %\n", "Epoch [45], Average Loss: 0.2142\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.451 %\n", "Epoch [46], Average Loss: 0.2136\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.601 %\n", "Epoch [47], Average Loss: 0.1966\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.941 %\n", "Epoch [48], Average Loss: 0.2108\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.031 %\n", "Epoch [49], Average Loss: 0.2242\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 91.061 %\n", "Epoch [50], Average Loss: 0.2319\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.381 %\n", "Epoch [51], Average Loss: 0.2535\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.741 %\n", "Epoch [52], Average Loss: 0.2496\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.071 %\n", "Epoch [53], Average Loss: 0.2331\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.321 %\n", "Epoch [54], Average Loss: 0.2229\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.871 %\n", "Epoch [55], Average Loss: 0.2205\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.011 %\n", "Epoch [56], Average Loss: 0.2356\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.261 %\n", "Epoch [57], Average Loss: 0.2325\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.991 %\n", "Epoch [58], Average Loss: 0.2268\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.361 %\n", "Epoch [59], Average Loss: 0.2284\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 90.641 %\n", "Epoch [60], Average Loss: 0.2150\n", "par_it: 3 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 87.491 %\n", "Epoch [1], Average Loss: 0.5124\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 90.091 %\n", "Epoch [2], Average Loss: 0.2827\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 89.721 %\n", "Epoch [3], Average Loss: 0.2391\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.241 %\n", "Epoch [4], Average Loss: 0.2143\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.491 %\n", "Epoch [5], Average Loss: 0.1851\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.881 %\n", "Epoch [6], Average Loss: 0.1663\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.041 %\n", "Epoch [7], Average Loss: 0.1530\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.551 %\n", "Epoch [8], Average Loss: 0.1330\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 91.921 %\n", "Epoch [9], Average Loss: 0.1211\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.591 %\n", "Epoch [10], Average Loss: 0.1046\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.461 %\n", "Epoch [11], Average Loss: 0.0885\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.671 %\n", "Epoch [12], Average Loss: 0.0701\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.881 %\n", "Epoch [13], Average Loss: 0.0426\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.161 %\n", "Epoch [14], Average Loss: 0.0286\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.291 %\n", "Epoch [15], Average Loss: 0.0170\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.901 %\n", "Epoch [16], Average Loss: 0.0175\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.761 %\n", "Epoch [17], Average Loss: 0.0106\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.571 %\n", "Epoch [18], Average Loss: 0.0109\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.821 %\n", "Epoch [19], Average Loss: 0.0142\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.461 %\n", "Epoch [20], Average Loss: 0.0113\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.771 %\n", "Epoch [21], Average Loss: 0.0044\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.421 %\n", "Epoch [22], Average Loss: 0.0489\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.211 %\n", "Epoch [23], Average Loss: 0.1221\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.731 %\n", "Epoch [24], Average Loss: 0.1778\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 89.501 %\n", "Epoch [25], Average Loss: 0.2156\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.771 %\n", "Epoch [26], Average Loss: 0.2539\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [27], Average Loss: 0.2415\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.161 %\n", "Epoch [28], Average Loss: 0.2317\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.831 %\n", "Epoch [29], Average Loss: 0.2330\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.301 %\n", "Epoch [30], Average Loss: 0.2379\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 87.791 %\n", "Epoch [31], Average Loss: 0.2541\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.841 %\n", "Epoch [32], Average Loss: 0.2514\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.761 %\n", "Epoch [33], Average Loss: 0.2374\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.591 %\n", "Epoch [34], Average Loss: 0.2643\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 88.941 %\n", "Epoch [35], Average Loss: 0.2714\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.221 %\n", "Epoch [36], Average Loss: 0.3238\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.171 %\n", "Epoch [37], Average Loss: 0.2504\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.901 %\n", "Epoch [38], Average Loss: 0.2275\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.811 %\n", "Epoch [39], Average Loss: 0.2310\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.381 %\n", "Epoch [40], Average Loss: 0.2365\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.041 %\n", "Epoch [41], Average Loss: 0.2537\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.741 %\n", "Epoch [42], Average Loss: 0.2554\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.861 %\n", "Epoch [43], Average Loss: 0.2509\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.971 %\n", "Epoch [44], Average Loss: 0.2139\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 90.461 %\n", "Epoch [45], Average Loss: 0.2167\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.001 %\n", "Epoch [46], Average Loss: 0.2191\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.111 %\n", "Epoch [47], Average Loss: 0.2250\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 89.821 %\n", "Epoch [48], Average Loss: 0.2378\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.371 %\n", "Epoch [49], Average Loss: 0.2039\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 91.091 %\n", "Epoch [50], Average Loss: 0.2118\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.141 %\n", "Epoch [51], Average Loss: 0.2242\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.911 %\n", "Epoch [52], Average Loss: 0.2272\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.821 %\n", "Epoch [53], Average Loss: 0.2178\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.141 %\n", "Epoch [54], Average Loss: 0.2320\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.041 %\n", "Epoch [55], Average Loss: 0.2243\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.421 %\n", "Epoch [56], Average Loss: 0.2182\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.441 %\n", "Epoch [57], Average Loss: 0.2256\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.071 %\n", "Epoch [58], Average Loss: 0.2208\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.561 %\n", "Epoch [59], Average Loss: 0.2135\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 90.831 %\n", "Epoch [60], Average Loss: 0.2070\n", "par_it: 4 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 86.161 %\n", "Epoch [1], Average Loss: 0.5462\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 89.961 %\n", "Epoch [2], Average Loss: 0.2996\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [3], Average Loss: 0.2452\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.041 %\n", "Epoch [4], Average Loss: 0.2135\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.171 %\n", "Epoch [5], Average Loss: 0.1927\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.741 %\n", "Epoch [6], Average Loss: 0.1752\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.231 %\n", "Epoch [7], Average Loss: 0.1580\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.601 %\n", "Epoch [8], Average Loss: 0.1482\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.701 %\n", "Epoch [9], Average Loss: 0.1326\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.521 %\n", "Epoch [10], Average Loss: 0.1144\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.661 %\n", "Epoch [11], Average Loss: 0.0843\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.871 %\n", "Epoch [12], Average Loss: 0.0795\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.111 %\n", "Epoch [13], Average Loss: 0.0539\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.371 %\n", "Epoch [14], Average Loss: 0.0334\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.351 %\n", "Epoch [15], Average Loss: 0.0191\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.931 %\n", "Epoch [16], Average Loss: 0.0225\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.071 %\n", "Epoch [17], Average Loss: 0.0139\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.921 %\n", "Epoch [18], Average Loss: 0.0110\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.731 %\n", "Epoch [19], Average Loss: 0.0155\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.571 %\n", "Epoch [20], Average Loss: 0.0147\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.871 %\n", "Epoch [21], Average Loss: 0.0046\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.841 %\n", "Epoch [22], Average Loss: 0.0470\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.351 %\n", "Epoch [23], Average Loss: 0.1297\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.761 %\n", "Epoch [24], Average Loss: 0.1812\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 91.061 %\n", "Epoch [25], Average Loss: 0.1930\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.471 %\n", "Epoch [26], Average Loss: 0.2037\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.341 %\n", "Epoch [27], Average Loss: 0.2423\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.681 %\n", "Epoch [28], Average Loss: 0.2642\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.071 %\n", "Epoch [29], Average Loss: 0.2668\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.181 %\n", "Epoch [30], Average Loss: 0.2629\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.181 %\n", "Epoch [31], Average Loss: 0.2919\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 89.181 %\n", "Epoch [32], Average Loss: 0.3178\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.131 %\n", "Epoch [33], Average Loss: 0.2904\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.981 %\n", "Epoch [34], Average Loss: 0.2484\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 88.331 %\n", "Epoch [35], Average Loss: 0.2409\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.241 %\n", "Epoch [36], Average Loss: 0.2969\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.341 %\n", "Epoch [37], Average Loss: 0.3028\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.451 %\n", "Epoch [38], Average Loss: 0.2652\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.701 %\n", "Epoch [39], Average Loss: 0.2480\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 89.531 %\n", "Epoch [40], Average Loss: 0.2657\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.871 %\n", "Epoch [41], Average Loss: 0.2702\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [42], Average Loss: 0.2727\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.011 %\n", "Epoch [43], Average Loss: 0.2424\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.941 %\n", "Epoch [44], Average Loss: 0.2247\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 90.401 %\n", "Epoch [45], Average Loss: 0.2113\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.751 %\n", "Epoch [46], Average Loss: 0.2396\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.331 %\n", "Epoch [47], Average Loss: 0.2461\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.571 %\n", "Epoch [48], Average Loss: 0.2500\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.131 %\n", "Epoch [49], Average Loss: 0.2504\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 91.151 %\n", "Epoch [50], Average Loss: 0.2239\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.911 %\n", "Epoch [51], Average Loss: 0.2078\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.541 %\n", "Epoch [52], Average Loss: 0.2035\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.911 %\n", "Epoch [53], Average Loss: 0.1936\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.321 %\n", "Epoch [54], Average Loss: 0.2022\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.071 %\n", "Epoch [55], Average Loss: 0.2037\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.331 %\n", "Epoch [56], Average Loss: 0.1917\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.001 %\n", "Epoch [57], Average Loss: 0.1945\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.541 %\n", "Epoch [58], Average Loss: 0.2311\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.451 %\n", "Epoch [59], Average Loss: 0.2731\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 90.881 %\n", "Epoch [60], Average Loss: 0.2293\n", "par_it: 5 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 87.771 %\n", "Epoch [1], Average Loss: 0.5232\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [2], Average Loss: 0.2937\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.531 %\n", "Epoch [3], Average Loss: 0.2389\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.411 %\n", "Epoch [4], Average Loss: 0.2147\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.491 %\n", "Epoch [5], Average Loss: 0.1880\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.971 %\n", "Epoch [6], Average Loss: 0.1786\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.061 %\n", "Epoch [7], Average Loss: 0.1641\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.381 %\n", "Epoch [8], Average Loss: 0.1422\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 91.941 %\n", "Epoch [9], Average Loss: 0.1273\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.651 %\n", "Epoch [10], Average Loss: 0.1084\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.381 %\n", "Epoch [11], Average Loss: 0.1004\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.531 %\n", "Epoch [12], Average Loss: 0.0727\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.741 %\n", "Epoch [13], Average Loss: 0.0525\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.691 %\n", "Epoch [14], Average Loss: 0.0298\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.061 %\n", "Epoch [15], Average Loss: 0.0191\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 93.091 %\n", "Epoch [16], Average Loss: 0.0137\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.211 %\n", "Epoch [17], Average Loss: 0.0133\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.821 %\n", "Epoch [18], Average Loss: 0.0144\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.911 %\n", "Epoch [19], Average Loss: 0.0136\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.631 %\n", "Epoch [20], Average Loss: 0.0100\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.791 %\n", "Epoch [21], Average Loss: 0.0046\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.791 %\n", "Epoch [22], Average Loss: 0.0460\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.101 %\n", "Epoch [23], Average Loss: 0.1193\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 91.191 %\n", "Epoch [24], Average Loss: 0.1820\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 90.521 %\n", "Epoch [25], Average Loss: 0.2195\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.361 %\n", "Epoch [26], Average Loss: 0.2487\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.471 %\n", "Epoch [27], Average Loss: 0.2427\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.321 %\n", "Epoch [28], Average Loss: 0.2343\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.111 %\n", "Epoch [29], Average Loss: 0.2326\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.221 %\n", "Epoch [30], Average Loss: 0.2565\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.491 %\n", "Epoch [31], Average Loss: 0.2599\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.771 %\n", "Epoch [32], Average Loss: 0.2589\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.901 %\n", "Epoch [33], Average Loss: 0.2767\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.221 %\n", "Epoch [34], Average Loss: 0.2695\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 89.101 %\n", "Epoch [35], Average Loss: 0.2712\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.061 %\n", "Epoch [36], Average Loss: 0.2829\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.141 %\n", "Epoch [37], Average Loss: 0.2392\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.851 %\n", "Epoch [38], Average Loss: 0.2246\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.971 %\n", "Epoch [39], Average Loss: 0.2403\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.021 %\n", "Epoch [40], Average Loss: 0.2357\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.471 %\n", "Epoch [41], Average Loss: 0.2226\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.831 %\n", "Epoch [42], Average Loss: 0.2228\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.011 %\n", "Epoch [43], Average Loss: 0.2207\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.051 %\n", "Epoch [44], Average Loss: 0.2273\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 90.111 %\n", "Epoch [45], Average Loss: 0.2413\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.231 %\n", "Epoch [46], Average Loss: 0.2383\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.511 %\n", "Epoch [47], Average Loss: 0.2171\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.361 %\n", "Epoch [48], Average Loss: 0.1933\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.621 %\n", "Epoch [49], Average Loss: 0.2053\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 90.091 %\n", "Epoch [50], Average Loss: 0.2320\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.571 %\n", "Epoch [51], Average Loss: 0.2204\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.051 %\n", "Epoch [52], Average Loss: 0.2242\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.211 %\n", "Epoch [53], Average Loss: 0.2345\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.161 %\n", "Epoch [54], Average Loss: 0.2253\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.341 %\n", "Epoch [55], Average Loss: 0.2164\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.141 %\n", "Epoch [56], Average Loss: 0.2289\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.181 %\n", "Epoch [57], Average Loss: 0.2214\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.611 %\n", "Epoch [58], Average Loss: 0.2257\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.091 %\n", "Epoch [59], Average Loss: 0.2027\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 91.121 %\n", "Epoch [60], Average Loss: 0.2039\n", "par_it: 6 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 86.981 %\n", "Epoch [1], Average Loss: 0.5372\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 89.811 %\n", "Epoch [2], Average Loss: 0.2908\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.551 %\n", "Epoch [3], Average Loss: 0.2376\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.261 %\n", "Epoch [4], Average Loss: 0.2114\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.381 %\n", "Epoch [5], Average Loss: 0.1910\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.081 %\n", "Epoch [6], Average Loss: 0.1718\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.951 %\n", "Epoch [7], Average Loss: 0.1571\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.341 %\n", "Epoch [8], Average Loss: 0.1358\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.031 %\n", "Epoch [9], Average Loss: 0.1215\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.301 %\n", "Epoch [10], Average Loss: 0.1060\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.501 %\n", "Epoch [11], Average Loss: 0.0931\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.761 %\n", "Epoch [12], Average Loss: 0.0665\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.821 %\n", "Epoch [13], Average Loss: 0.0515\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.201 %\n", "Epoch [14], Average Loss: 0.0285\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 92.971 %\n", "Epoch [15], Average Loss: 0.0159\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.711 %\n", "Epoch [16], Average Loss: 0.0185\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.331 %\n", "Epoch [17], Average Loss: 0.0130\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 93.251 %\n", "Epoch [18], Average Loss: 0.0071\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 93.311 %\n", "Epoch [19], Average Loss: 0.0193\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.841 %\n", "Epoch [20], Average Loss: 0.0112\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.981 %\n", "Epoch [21], Average Loss: 0.0043\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.741 %\n", "Epoch [22], Average Loss: 0.0485\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.751 %\n", "Epoch [23], Average Loss: 0.1256\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 89.561 %\n", "Epoch [24], Average Loss: 0.1878\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 89.181 %\n", "Epoch [25], Average Loss: 0.2264\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.471 %\n", "Epoch [26], Average Loss: 0.2486\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.401 %\n", "Epoch [27], Average Loss: 0.2458\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.521 %\n", "Epoch [28], Average Loss: 0.2542\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.331 %\n", "Epoch [29], Average Loss: 0.2613\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.341 %\n", "Epoch [30], Average Loss: 0.2470\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.581 %\n", "Epoch [31], Average Loss: 0.2493\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 89.141 %\n", "Epoch [32], Average Loss: 0.2403\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.141 %\n", "Epoch [33], Average Loss: 0.2508\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.991 %\n", "Epoch [34], Average Loss: 0.2468\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 91.241 %\n", "Epoch [35], Average Loss: 0.2237\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.041 %\n", "Epoch [36], Average Loss: 0.2302\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.151 %\n", "Epoch [37], Average Loss: 0.2258\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.581 %\n", "Epoch [38], Average Loss: 0.2110\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.651 %\n", "Epoch [39], Average Loss: 0.2126\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.851 %\n", "Epoch [40], Average Loss: 0.1994\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.341 %\n", "Epoch [41], Average Loss: 0.2047\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.481 %\n", "Epoch [42], Average Loss: 0.2210\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.481 %\n", "Epoch [43], Average Loss: 0.2033\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.991 %\n", "Epoch [44], Average Loss: 0.2200\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 91.081 %\n", "Epoch [45], Average Loss: 0.2319\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.121 %\n", "Epoch [46], Average Loss: 0.2378\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.871 %\n", "Epoch [47], Average Loss: 0.2439\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 88.831 %\n", "Epoch [48], Average Loss: 0.2454\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.601 %\n", "Epoch [49], Average Loss: 0.2405\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 91.131 %\n", "Epoch [50], Average Loss: 0.2390\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.381 %\n", "Epoch [51], Average Loss: 0.2556\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.551 %\n", "Epoch [52], Average Loss: 0.2607\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.941 %\n", "Epoch [53], Average Loss: 0.2415\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.831 %\n", "Epoch [54], Average Loss: 0.2499\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.411 %\n", "Epoch [55], Average Loss: 0.2512\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.331 %\n", "Epoch [56], Average Loss: 0.2457\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.921 %\n", "Epoch [57], Average Loss: 0.2529\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.841 %\n", "Epoch [58], Average Loss: 0.2267\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.851 %\n", "Epoch [59], Average Loss: 0.2096\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 91.321 %\n", "Epoch [60], Average Loss: 0.2009\n", "par_it: 7 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 83.942 %\n", "Epoch [1], Average Loss: 0.5378\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 89.731 %\n", "Epoch [2], Average Loss: 0.2957\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.481 %\n", "Epoch [3], Average Loss: 0.2462\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.121 %\n", "Epoch [4], Average Loss: 0.2113\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.701 %\n", "Epoch [5], Average Loss: 0.1938\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.711 %\n", "Epoch [6], Average Loss: 0.1681\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.331 %\n", "Epoch [7], Average Loss: 0.1600\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.511 %\n", "Epoch [8], Average Loss: 0.1448\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.031 %\n", "Epoch [9], Average Loss: 0.1229\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.381 %\n", "Epoch [10], Average Loss: 0.1098\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.681 %\n", "Epoch [11], Average Loss: 0.0885\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.791 %\n", "Epoch [12], Average Loss: 0.0763\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.901 %\n", "Epoch [13], Average Loss: 0.0485\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.971 %\n", "Epoch [14], Average Loss: 0.0259\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 92.991 %\n", "Epoch [15], Average Loss: 0.0205\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 93.041 %\n", "Epoch [16], Average Loss: 0.0149\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.051 %\n", "Epoch [17], Average Loss: 0.0116\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 93.121 %\n", "Epoch [18], Average Loss: 0.0144\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 93.151 %\n", "Epoch [19], Average Loss: 0.0110\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.681 %\n", "Epoch [20], Average Loss: 0.0120\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.001 %\n", "Epoch [21], Average Loss: 0.0043\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.811 %\n", "Epoch [22], Average Loss: 0.0477\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.261 %\n", "Epoch [23], Average Loss: 0.1389\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.621 %\n", "Epoch [24], Average Loss: 0.2086\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 90.121 %\n", "Epoch [25], Average Loss: 0.2123\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.941 %\n", "Epoch [26], Average Loss: 0.2203\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.951 %\n", "Epoch [27], Average Loss: 0.2120\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.911 %\n", "Epoch [28], Average Loss: 0.2127\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.801 %\n", "Epoch [29], Average Loss: 0.2196\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 90.711 %\n", "Epoch [30], Average Loss: 0.2351\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.201 %\n", "Epoch [31], Average Loss: 0.2255\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.591 %\n", "Epoch [32], Average Loss: 0.2139\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.241 %\n", "Epoch [33], Average Loss: 0.2152\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.861 %\n", "Epoch [34], Average Loss: 0.2347\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 86.171 %\n", "Epoch [35], Average Loss: 0.2856\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.531 %\n", "Epoch [36], Average Loss: 0.2782\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.191 %\n", "Epoch [37], Average Loss: 0.2579\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.511 %\n", "Epoch [38], Average Loss: 0.2349\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.381 %\n", "Epoch [39], Average Loss: 0.2240\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.001 %\n", "Epoch [40], Average Loss: 0.2420\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.161 %\n", "Epoch [41], Average Loss: 0.2586\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.711 %\n", "Epoch [42], Average Loss: 0.2577\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.611 %\n", "Epoch [43], Average Loss: 0.2460\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.461 %\n", "Epoch [44], Average Loss: 0.2661\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 91.411 %\n", "Epoch [45], Average Loss: 0.2469\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.241 %\n", "Epoch [46], Average Loss: 0.2311\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.591 %\n", "Epoch [47], Average Loss: 0.2323\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 89.701 %\n", "Epoch [48], Average Loss: 0.2406\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.461 %\n", "Epoch [49], Average Loss: 0.2407\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 91.111 %\n", "Epoch [50], Average Loss: 0.2233\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.561 %\n", "Epoch [51], Average Loss: 0.2149\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.561 %\n", "Epoch [52], Average Loss: 0.2318\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.911 %\n", "Epoch [53], Average Loss: 0.2124\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.181 %\n", "Epoch [54], Average Loss: 0.1905\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.651 %\n", "Epoch [55], Average Loss: 0.2061\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.041 %\n", "Epoch [56], Average Loss: 0.2436\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.771 %\n", "Epoch [57], Average Loss: 0.2517\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.531 %\n", "Epoch [58], Average Loss: 0.2487\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.181 %\n", "Epoch [59], Average Loss: 0.2449\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 89.561 %\n", "Epoch [60], Average Loss: 0.2331\n", "par_it: 8 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 86.171 %\n", "Epoch [1], Average Loss: 0.5638\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 89.021 %\n", "Epoch [2], Average Loss: 0.3048\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.211 %\n", "Epoch [3], Average Loss: 0.2425\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.841 %\n", "Epoch [4], Average Loss: 0.2103\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.141 %\n", "Epoch [5], Average Loss: 0.1909\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.691 %\n", "Epoch [6], Average Loss: 0.1779\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.211 %\n", "Epoch [7], Average Loss: 0.1637\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.231 %\n", "Epoch [8], Average Loss: 0.1486\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.421 %\n", "Epoch [9], Average Loss: 0.1322\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.181 %\n", "Epoch [10], Average Loss: 0.1202\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.491 %\n", "Epoch [11], Average Loss: 0.1005\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.941 %\n", "Epoch [12], Average Loss: 0.0793\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.771 %\n", "Epoch [13], Average Loss: 0.0564\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.011 %\n", "Epoch [14], Average Loss: 0.0380\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 92.971 %\n", "Epoch [15], Average Loss: 0.0279\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 93.111 %\n", "Epoch [16], Average Loss: 0.0186\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.631 %\n", "Epoch [17], Average Loss: 0.0180\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 93.001 %\n", "Epoch [18], Average Loss: 0.0163\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 93.041 %\n", "Epoch [19], Average Loss: 0.0116\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.541 %\n", "Epoch [20], Average Loss: 0.0175\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.791 %\n", "Epoch [21], Average Loss: 0.0057\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.351 %\n", "Epoch [22], Average Loss: 0.0566\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.971 %\n", "Epoch [23], Average Loss: 0.1311\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 89.601 %\n", "Epoch [24], Average Loss: 0.2070\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 90.391 %\n", "Epoch [25], Average Loss: 0.2432\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.821 %\n", "Epoch [26], Average Loss: 0.2405\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.501 %\n", "Epoch [27], Average Loss: 0.2330\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.641 %\n", "Epoch [28], Average Loss: 0.2299\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.651 %\n", "Epoch [29], Average Loss: 0.2359\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 90.731 %\n", "Epoch [30], Average Loss: 0.2404\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.021 %\n", "Epoch [31], Average Loss: 0.2335\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.111 %\n", "Epoch [32], Average Loss: 0.2381\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.591 %\n", "Epoch [33], Average Loss: 0.2607\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.431 %\n", "Epoch [34], Average Loss: 0.2296\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 89.691 %\n", "Epoch [35], Average Loss: 0.2280\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.671 %\n", "Epoch [36], Average Loss: 0.2393\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.221 %\n", "Epoch [37], Average Loss: 0.2413\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.311 %\n", "Epoch [38], Average Loss: 0.2191\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.051 %\n", "Epoch [39], Average Loss: 0.2040\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.161 %\n", "Epoch [40], Average Loss: 0.2393\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.381 %\n", "Epoch [41], Average Loss: 0.2347\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.911 %\n", "Epoch [42], Average Loss: 0.2521\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.221 %\n", "Epoch [43], Average Loss: 0.2757\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.191 %\n", "Epoch [44], Average Loss: 0.2486\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 88.781 %\n", "Epoch [45], Average Loss: 0.2524\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.481 %\n", "Epoch [46], Average Loss: 0.2564\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.401 %\n", "Epoch [47], Average Loss: 0.2579\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.311 %\n", "Epoch [48], Average Loss: 0.2538\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.831 %\n", "Epoch [49], Average Loss: 0.2416\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 89.731 %\n", "Epoch [50], Average Loss: 0.2559\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.711 %\n", "Epoch [51], Average Loss: 0.2458\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.321 %\n", "Epoch [52], Average Loss: 0.2161\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.241 %\n", "Epoch [53], Average Loss: 0.2126\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.131 %\n", "Epoch [54], Average Loss: 0.2193\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.721 %\n", "Epoch [55], Average Loss: 0.2108\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.641 %\n", "Epoch [56], Average Loss: 0.2109\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.401 %\n", "Epoch [57], Average Loss: 0.2255\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.451 %\n", "Epoch [58], Average Loss: 0.2104\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.121 %\n", "Epoch [59], Average Loss: 0.2032\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 91.311 %\n", "Epoch [60], Average Loss: 0.1918\n", "par_it: 9 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 84.502 %\n", "Epoch [1], Average Loss: 0.5784\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 88.341 %\n", "Epoch [2], Average Loss: 0.3257\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.781 %\n", "Epoch [3], Average Loss: 0.2587\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 90.761 %\n", "Epoch [4], Average Loss: 0.2205\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.331 %\n", "Epoch [5], Average Loss: 0.1982\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.871 %\n", "Epoch [6], Average Loss: 0.1803\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.071 %\n", "Epoch [7], Average Loss: 0.1666\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.141 %\n", "Epoch [8], Average Loss: 0.1490\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.321 %\n", "Epoch [9], Average Loss: 0.1333\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.881 %\n", "Epoch [10], Average Loss: 0.1178\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.121 %\n", "Epoch [11], Average Loss: 0.0925\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.481 %\n", "Epoch [12], Average Loss: 0.0744\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.631 %\n", "Epoch [13], Average Loss: 0.0521\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.831 %\n", "Epoch [14], Average Loss: 0.0300\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.201 %\n", "Epoch [15], Average Loss: 0.0210\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 93.011 %\n", "Epoch [16], Average Loss: 0.0137\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.861 %\n", "Epoch [17], Average Loss: 0.0150\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.561 %\n", "Epoch [18], Average Loss: 0.0143\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.961 %\n", "Epoch [19], Average Loss: 0.0112\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.461 %\n", "Epoch [20], Average Loss: 0.0071\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.551 %\n", "Epoch [21], Average Loss: 0.0049\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.351 %\n", "Epoch [22], Average Loss: 0.0509\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.961 %\n", "Epoch [23], Average Loss: 0.1231\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 89.621 %\n", "Epoch [24], Average Loss: 0.1906\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 88.671 %\n", "Epoch [25], Average Loss: 0.2448\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.111 %\n", "Epoch [26], Average Loss: 0.2628\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.661 %\n", "Epoch [27], Average Loss: 0.2744\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.461 %\n", "Epoch [28], Average Loss: 0.2595\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.421 %\n", "Epoch [29], Average Loss: 0.2691\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.511 %\n", "Epoch [30], Average Loss: 0.2782\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.261 %\n", "Epoch [31], Average Loss: 0.2522\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 89.571 %\n", "Epoch [32], Average Loss: 0.2440\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.781 %\n", "Epoch [33], Average Loss: 0.2426\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.461 %\n", "Epoch [34], Average Loss: 0.2364\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 89.881 %\n", "Epoch [35], Average Loss: 0.2387\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.541 %\n", "Epoch [36], Average Loss: 0.2224\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.691 %\n", "Epoch [37], Average Loss: 0.2442\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.951 %\n", "Epoch [38], Average Loss: 0.2202\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.291 %\n", "Epoch [39], Average Loss: 0.2244\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.541 %\n", "Epoch [40], Average Loss: 0.2183\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.651 %\n", "Epoch [41], Average Loss: 0.2073\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.611 %\n", "Epoch [42], Average Loss: 0.2428\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.501 %\n", "Epoch [43], Average Loss: 0.2634\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.791 %\n", "Epoch [44], Average Loss: 0.2490\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 89.931 %\n", "Epoch [45], Average Loss: 0.2341\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.281 %\n", "Epoch [46], Average Loss: 0.2512\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.891 %\n", "Epoch [47], Average Loss: 0.2367\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 90.671 %\n", "Epoch [48], Average Loss: 0.2413\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.291 %\n", "Epoch [49], Average Loss: 0.2179\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 90.981 %\n", "Epoch [50], Average Loss: 0.2075\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.911 %\n", "Epoch [51], Average Loss: 0.2117\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.901 %\n", "Epoch [52], Average Loss: 0.2415\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.021 %\n", "Epoch [53], Average Loss: 0.2529\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.791 %\n", "Epoch [54], Average Loss: 0.2658\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.311 %\n", "Epoch [55], Average Loss: 0.2528\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.451 %\n", "Epoch [56], Average Loss: 0.2366\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.751 %\n", "Epoch [57], Average Loss: 0.1945\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.641 %\n", "Epoch [58], Average Loss: 0.1970\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.791 %\n", "Epoch [59], Average Loss: 0.2143\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 89.481 %\n", "Epoch [60], Average Loss: 0.2304\n", "par_it: 10 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 88.461 %\n", "Epoch [1], Average Loss: 0.4830\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 90.611 %\n", "Epoch [2], Average Loss: 0.2893\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 91.031 %\n", "Epoch [3], Average Loss: 0.2443\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 91.451 %\n", "Epoch [4], Average Loss: 0.2051\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 92.021 %\n", "Epoch [5], Average Loss: 0.1924\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.961 %\n", "Epoch [6], Average Loss: 0.1726\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.481 %\n", "Epoch [7], Average Loss: 0.1577\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.071 %\n", "Epoch [8], Average Loss: 0.1414\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.191 %\n", "Epoch [9], Average Loss: 0.1259\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 93.061 %\n", "Epoch [10], Average Loss: 0.1061\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.811 %\n", "Epoch [11], Average Loss: 0.0920\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.891 %\n", "Epoch [12], Average Loss: 0.0716\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.141 %\n", "Epoch [13], Average Loss: 0.0447\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.271 %\n", "Epoch [14], Average Loss: 0.0227\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.171 %\n", "Epoch [15], Average Loss: 0.0186\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 93.331 %\n", "Epoch [16], Average Loss: 0.0155\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.321 %\n", "Epoch [17], Average Loss: 0.0133\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 92.931 %\n", "Epoch [18], Average Loss: 0.0094\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 93.191 %\n", "Epoch [19], Average Loss: 0.0111\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.811 %\n", "Epoch [20], Average Loss: 0.0114\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.161 %\n", "Epoch [21], Average Loss: 0.0042\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.971 %\n", "Epoch [22], Average Loss: 0.0510\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.111 %\n", "Epoch [23], Average Loss: 0.1418\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.231 %\n", "Epoch [24], Average Loss: 0.1996\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 89.621 %\n", "Epoch [25], Average Loss: 0.2241\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.291 %\n", "Epoch [26], Average Loss: 0.2312\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.081 %\n", "Epoch [27], Average Loss: 0.2260\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.611 %\n", "Epoch [28], Average Loss: 0.2242\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.121 %\n", "Epoch [29], Average Loss: 0.2315\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 91.021 %\n", "Epoch [30], Average Loss: 0.2120\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.211 %\n", "Epoch [31], Average Loss: 0.2401\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.781 %\n", "Epoch [32], Average Loss: 0.2413\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.861 %\n", "Epoch [33], Average Loss: 0.2351\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.531 %\n", "Epoch [34], Average Loss: 0.2402\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 89.461 %\n", "Epoch [35], Average Loss: 0.2375\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.311 %\n", "Epoch [36], Average Loss: 0.2809\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.281 %\n", "Epoch [37], Average Loss: 0.2642\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.621 %\n", "Epoch [38], Average Loss: 0.2503\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.661 %\n", "Epoch [39], Average Loss: 0.2409\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.421 %\n", "Epoch [40], Average Loss: 0.2647\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.031 %\n", "Epoch [41], Average Loss: 0.2410\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.641 %\n", "Epoch [42], Average Loss: 0.2337\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.931 %\n", "Epoch [43], Average Loss: 0.2288\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.661 %\n", "Epoch [44], Average Loss: 0.2242\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 89.961 %\n", "Epoch [45], Average Loss: 0.2269\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.121 %\n", "Epoch [46], Average Loss: 0.2611\n", "Epoch [47/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.181 %\n", "Epoch [47], Average Loss: 0.2816\n", "Epoch [48/60], Step [300/300]\n", "Test accuracy of the model: 89.261 %\n", "Epoch [48], Average Loss: 0.2870\n", "Epoch [49/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.571 %\n", "Epoch [49], Average Loss: 0.2709\n", "Epoch [50/60], Step [300/300]\n", "Test accuracy of the model: 90.421 %\n", "Epoch [50], Average Loss: 0.2560\n", "Epoch [51/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.551 %\n", "Epoch [51], Average Loss: 0.2538\n", "Epoch [52/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.031 %\n", "Epoch [52], Average Loss: 0.2515\n", "Epoch [53/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.861 %\n", "Epoch [53], Average Loss: 0.2370\n", "Epoch [54/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.131 %\n", "Epoch [54], Average Loss: 0.2371\n", "Epoch [55/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.641 %\n", "Epoch [55], Average Loss: 0.2421\n", "Epoch [56/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.791 %\n", "Epoch [56], Average Loss: 0.2322\n", "Epoch [57/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.951 %\n", "Epoch [57], Average Loss: 0.2224\n", "Epoch [58/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.171 %\n", "Epoch [58], Average Loss: 0.2224\n", "Epoch [59/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.301 %\n", "Epoch [59], Average Loss: 0.2234\n", "Epoch [60/60], Step [300/300]\n", "Test accuracy of the model: 90.101 %\n", "Epoch [60], Average Loss: 0.2267\n", "par_it: 11 \n", "\n", "Epoch [1/60], Step [300/300]\n", "Test accuracy of the model: 87.501 %\n", "Epoch [1], Average Loss: 0.5194\n", "Epoch [2/60], Step [300/300]\n", "Test accuracy of the model: 89.491 %\n", "Epoch [2], Average Loss: 0.2958\n", "Epoch [3/60], Step [300/300]\n", "Test accuracy of the model: 90.611 %\n", "Epoch [3], Average Loss: 0.2403\n", "Epoch [4/60], Step [300/300]\n", "Test accuracy of the model: 90.651 %\n", "Epoch [4], Average Loss: 0.2105\n", "Epoch [5/60], Step [300/300]\n", "Test accuracy of the model: 91.351 %\n", "Epoch [5], Average Loss: 0.1878\n", "Epoch [6/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.881 %\n", "Epoch [6], Average Loss: 0.1764\n", "Epoch [7/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.221 %\n", "Epoch [7], Average Loss: 0.1518\n", "Epoch [8/60], Step [300/300]\n", "Test accuracy of the model: 92.051 %\n", "Epoch [8], Average Loss: 0.1415\n", "Epoch [9/60], Step [300/300]\n", "Test accuracy of the model: 92.181 %\n", "Epoch [9], Average Loss: 0.1227\n", "Epoch [10/60], Step [300/300]\n", "Test accuracy of the model: 92.241 %\n", "Epoch [10], Average Loss: 0.1034\n", "Epoch [11/60], Step [300/300]\n", "Test accuracy of the model: 92.571 %\n", "Epoch [11], Average Loss: 0.0877\n", "Epoch [12/60], Step [300/300]\n", "Test accuracy of the model: 92.881 %\n", "Epoch [12], Average Loss: 0.0624\n", "Epoch [13/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.881 %\n", "Epoch [13], Average Loss: 0.0466\n", "Epoch [14/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.221 %\n", "Epoch [14], Average Loss: 0.0255\n", "Epoch [15/60], Step [300/300]\n", "Test accuracy of the model: 93.081 %\n", "Epoch [15], Average Loss: 0.0146\n", "Epoch [16/60], Step [300/300]\n", "Test accuracy of the model: 92.911 %\n", "Epoch [16], Average Loss: 0.0124\n", "Epoch [17/60], <PERSON> [300/300]\n", "Test accuracy of the model: 93.091 %\n", "Epoch [17], Average Loss: 0.0135\n", "Epoch [18/60], Step [300/300]\n", "Test accuracy of the model: 93.101 %\n", "Epoch [18], Average Loss: 0.0115\n", "Epoch [19/60], Step [300/300]\n", "Test accuracy of the model: 92.841 %\n", "Epoch [19], Average Loss: 0.0094\n", "Epoch [20/60], Step [300/300]\n", "Test accuracy of the model: 93.381 %\n", "Epoch [20], Average Loss: 0.0131\n", "Epoch [21/60], <PERSON> [300/300]\n", "Test accuracy of the model: 92.851 %\n", "Epoch [21], Average Loss: 0.0042\n", "Epoch [22/60], Step [300/300]\n", "Test accuracy of the model: 91.911 %\n", "Epoch [22], Average Loss: 0.0435\n", "Epoch [23/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.881 %\n", "Epoch [23], Average Loss: 0.1188\n", "Epoch [24/60], Step [300/300]\n", "Test accuracy of the model: 90.241 %\n", "Epoch [24], Average Loss: 0.1963\n", "Epoch [25/60], Step [300/300]\n", "Test accuracy of the model: 88.941 %\n", "Epoch [25], Average Loss: 0.2337\n", "Epoch [26/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.771 %\n", "Epoch [26], Average Loss: 0.2493\n", "Epoch [27/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.901 %\n", "Epoch [27], Average Loss: 0.2468\n", "Epoch [28/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.311 %\n", "Epoch [28], Average Loss: 0.2434\n", "Epoch [29/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.621 %\n", "Epoch [29], Average Loss: 0.2490\n", "Epoch [30/60], Step [300/300]\n", "Test accuracy of the model: 89.101 %\n", "Epoch [30], Average Loss: 0.2730\n", "Epoch [31/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.781 %\n", "Epoch [31], Average Loss: 0.2917\n", "Epoch [32/60], Step [300/300]\n", "Test accuracy of the model: 90.571 %\n", "Epoch [32], Average Loss: 0.2757\n", "Epoch [33/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.091 %\n", "Epoch [33], Average Loss: 0.2727\n", "Epoch [34/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.951 %\n", "Epoch [34], Average Loss: 0.2594\n", "Epoch [35/60], Step [300/300]\n", "Test accuracy of the model: 89.551 %\n", "Epoch [35], Average Loss: 0.2488\n", "Epoch [36/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.601 %\n", "Epoch [36], Average Loss: 0.2522\n", "Epoch [37/60], <PERSON> [300/300]\n", "Test accuracy of the model: 89.921 %\n", "Epoch [37], Average Loss: 0.2224\n", "Epoch [38/60], <PERSON> [300/300]\n", "Test accuracy of the model: 91.001 %\n", "Epoch [38], Average Loss: 0.2004\n", "Epoch [39/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.741 %\n", "Epoch [39], Average Loss: 0.1914\n", "Epoch [40/60], Step [300/300]\n", "Test accuracy of the model: 90.751 %\n", "Epoch [40], Average Loss: 0.2200\n", "Epoch [41/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.481 %\n", "Epoch [41], Average Loss: 0.2262\n", "Epoch [42/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.501 %\n", "Epoch [42], Average Loss: 0.2249\n", "Epoch [43/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.331 %\n", "Epoch [43], Average Loss: 0.2261\n", "Epoch [44/60], <PERSON> [300/300]\n", "Test accuracy of the model: 90.341 %\n", "Epoch [44], Average Loss: 0.2386\n", "Epoch [45/60], Step [300/300]\n", "Test accuracy of the model: 90.001 %\n", "Epoch [45], Average Loss: 0.2578\n", "Epoch [46/60], <PERSON> [300/300]\n", "Test accuracy of the model: 88.901 %\n", "Epoch [46], Average Loss: 0.2734\n"]}], "source": ["#@title Output arrays\n", "par_runs=64\n", "num_classes=10\n", "num_epochs=60\n", "switch_to_sampling_epoch=20\n", "switch_to_swag_epoch=15\n", "\n", "num_swag_epochs=switch_to_sampling_epoch-switch_to_swag_epoch\n", "\n", "training_size=no_batches*batch_size\n", "test_size=test_data_len\n", "labels_arr=torch.zeros(training_size)\n", "test_labels_arr=torch.zeros(test_size)\n", "test_prob_arr=torch.zeros([test_size,num_classes,num_epochs,par_runs])\n", "\n", "lr = 1e-2\n", "lr_swag=1e-3\n", "h=2.5e-4\n", "l2regconst=torch.tensor(1).detach()\n", "l2regconst_extra=torch.tensor(0).detach()\n", "gam=torch.sqrt(torch.tensor(50)).detach()\n", "hper2c=hper2const(torch.tensor(h/2),gam)\n", "\n", "for par_it in range(par_runs):\n", "  print(\"par_it:\",par_it,\"\\n\")\n", "  #@title Build the model\n", "  net = Fashion_MNIST_CNN().cuda()\n", "  net.train()\n", "  optimizer = torch.optim.Adam( net.parameters(), lr=lr)\n", "  \n", "  lr_scheduler = torch.optim.lr_scheduler.PolynomialLR(optimizer=optimizer, total_iters=switch_to_swag_epoch,power=1)\n", "\n", "\n", "  #@title Training the model\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    #l2regconst=torch.min(torch.tensor(1+epoch),torch.tensor(switch_to_swag_epoch)).detach()\n", "    net.train()\n", "    if(epoch==(switch_to_swag_epoch-1)):\n", "      net_star2=copy.deepcopy(net)\n", "      net2=copy.deepcopy(net)\n", "      multiplynet(net2,0)\n", "      optimizer=torch.optim.Adam(net.parameters(),lr=lr_swag)\n", "\n", "    if(epoch>=switch_to_sampling_epoch and (epoch-switch_to_sampling_epoch)%2==0):\n", "        rperm=random.permutation(list(range(no_batches)))\n", "    \n", "    for i in range(no_batches): \n", "      b=torch.randint(high=no_batches,size=(1,1))\n", "      images=images_list[b]\n", "      labels=labels_list[b]\n", "      \n", "      # outputs = net(images)    \n", "      # loss = loss_function(outputs, labels)\n", "      # sum_loss=sum_loss+loss    \n", "      # reg=images_regulariser()\n", "      # loss=loss+reg\n", "      # optimizer.zero_grad()\n", "      # loss.backward()\n", "      # optimizer.step()\n", "      # sum_loss_tot=sum_loss_tot+loss\n", "\n", "\n", "      if(epoch<switch_to_sampling_epoch):\n", "        outputs = net(images)    \n", "        loss_likelihood = loss_function(outputs, labels)\n", "        sum_loss=sum_loss+loss_likelihood    \n", "        reg=images_regulariser()\n", "        loss=loss_likelihood+reg\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        if(epoch>=(switch_to_swag_epoch)):\n", "          addnet(net2,net)\n", "      else:\n", "        loss_likelihood=EM_step(net, h, gam,b)\n", "        sum_loss=sum_loss+loss_likelihood\n", "\n", "\n", "\n", "\n", "    #if (i+1) % (no_batches) == 0:\n", "    #print(\"Reg:\",reg)\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0\n", "    \n", "\n", "\n", "\n", "\n", "\n", "    #for imagest,labelst in eval_gen:\n", "    if epoch==(switch_to_sampling_epoch-1):\n", "      multiplynet(net2,1/(num_swag_epochs*no_batches))\n", "      multiplynet(net,0)\n", "      addnet(net,net2)\n", "      del net2\n", "\n", "      net_star=copy.deepcopy(net)\n", "      len_params=len(list(net_star.parameters()))\n", "\n", "      #Variance reduction - saving gradients at each batch at x_star\n", "      net_star_grad_list=[]\n", "      net_star_full_grad=[torch.zeros_like(par, device=device) for par in list(net_star.parameters())]\n", "      for i in range(no_batches):\n", "          images=images_list[i]\n", "          labels=labels_list[i]\n", "          outputs=net_star(images)\n", "          loss_likelihood = loss_function(outputs, labels)\n", "          reg=images_regulariser()\n", "          net_star.zero_grad()\n", "          loss_likelihood.backward()\n", "          grads=[par.grad*batch_size for par in list(net_star.parameters())]\n", "          net_star_grad_list.append(grads)\n", "          for g, gi in zip(net_star_full_grad,grads):\n", "            g+=gi          \n", "\n", "      len_params=len(list(net_star.parameters()))\n", "      list_no_bias=torch.zeros(len_params)\n", "      pit=0\n", "      for name, p in net_star.named_parameters():\n", "          if 'bias' not in name:\n", "              list_no_bias[pit]=1.0\n", "          pit+=1\n", "\n", "      #Initialise velocities\n", "      for par in list(net.parameters()):\n", "        par.v = torch.randn_like(par,device=device)      \n", "\n", "\n", "    with torch.no_grad():\n", "      net(torch.cat(images_list[0:50],dim=0).detach())        \n", "      net.eval()\n", "\n", "      # for mod in list(net.named_modules()):\n", "      #   if(hasattr(mod[1],\"momentum\")):\n", "      #     mod[1].momentum=None\n", "      # for i in range(no_batches):\n", "      #   net(images_list[i].detach())\n", "      # net.eval()\n", "\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:,epoch,par_it]=torch.softmax(outputt,dim=1)\n", "    \n", "\n", "\n", "    \n", "\n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "\n", "    if(epoch<=switch_to_swag_epoch):\n", "      lr_scheduler.step()\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "    \n", "    # for mod in list(net.named_modules()):\n", "    #   if(hasattr(mod[1],\"momentum\")):\n", "    #     mod[1].momentum=1.0\n", "  \n", "  filepath=\"output_fashion_SG_HMC.pickle\"\n", "  with open(filepath,\"wb\") as file:\n", "    pickle.dump([labels_arr.numpy(),test_labels_arr.numpy(),test_prob_arr.numpy()],file)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["1265258"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["n=0\n", "for par in net.parameters():\n", "    n+=par.numel()\n", "n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net_star_backup=copy.deepcopy(net_star)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#filepath=\"output_fashion.pickle\"\n", "#with open(filepath,\"rb\") as file:\n", "#    [labels_arr,test_labels_arr,test_prob_arr,_,_]=pickle.load(file)\n", "#labels_arr=torch.tensor(labels_arr).detach()\n", "#test_labels_arr=torch.tensor(test_labels_arr).detach()\n", "#test_prob_arr=torch.tensor(test_prob_arr).detach()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def rps_single(probs, true_label,num_classes):\n", "    outcome=torch.zeros(num_classes)\n", "    outcome[true_label.int()]=1.0\n", "    cum_probs = torch.cumsum(probs,0)\n", "    cum_outcomes = torch.cumsum(outcome,0)\n", "    \n", "    sum_rps = 0\n", "    for i in range(len(outcome)):         \n", "        sum_rps+= (cum_probs[i] - cum_outcomes[i])**2\n", "    \n", "    return sum_rps/(num_classes-1)\n", "\n", "def rps_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    rps_vec=torch.zeros(test_data_len)\n", "    for it in range(test_data_len):\n", "        rps_vec[it]=rps_single(test_probs[it,:].reshape(num_classes),true_labels[it],num_classes)\n", "    return rps_vec\n", "\n", "def nll_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    res=0\n", "    for it in range(test_data_len):\n", "        res-=torch.max(torch.tensor([torch.log(test_probs[it,true_labels[it].int()]),-100]))\n", "    return res/test_data_len\n", "\n", "\n", "def adaptive_calibration_error(test_probs,true_labels, test_data_len, num_classes,num_bins=20):\n", "    o=torch.tensor(0.0).detach()\n", "    for k in range(num_classes):\n", "        ind=torch.argsort(test_probs[:,k],stable=True)        \n", "        testprobsk=test_probs[:,k]\n", "        sorted_probs=testprobsk[ind]\n", "        sorted_true_labels=true_labels[ind]\n", "\n", "        true_label_is_k = (sorted_true_labels==k).clone().detach().float()\n", "        bins=(torch.tensor(range(test_data_len))/torch.tensor(test_data_len/num_bins)).floor()\n", "\n", "        for b in range(num_bins):\n", "            mask = (bins == b)\n", "            if torch.any(mask):\n", "                o += (true_label_is_k[mask] - sorted_probs[mask]).mean().abs()\n", "\n", "    return o / (num_bins*num_classes)\n", "\n", "\n", "def compute_acc_ace_rps_no_bayes(es,par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_swa_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Non-Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "def compute_acc_ace_rps_swa(es,par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_sampling_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"SWA, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "#Bayesian\n", "def compute_acc_ace_rps_bayes(es,par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr): \n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,(switch_to_sampling_epoch+burnin_epochs):num_epochs,it*es:(it+1)*es]).mean(-1).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9316) std: tensor(0.0016)\n", "mean ace: tensor(0.0037) std: tensor(0.0003)\n", "mean nll: tensor(0.1950) std: tensor(0.0021)\n", "mean rps: tensor(0.0188) std: tensor(0.0002)\n", "Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.9346) std: tensor(0.0012)\n", "mean ace: tensor(0.0044) std: tensor(0.0002)\n", "mean nll: tensor(0.1879) std: tensor(0.0011)\n", "mean rps: tensor(0.0182) std: tensor(0.0001)\n", "Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9362) std: tensor(0.0009)\n", "mean ace: tensor(0.0049) std: tensor(0.0002)\n", "mean nll: tensor(0.1844) std: tensor(0.0008)\n", "mean rps: tensor(0.0179) std: tensor(8.0132e-05)\n", "Bayesian, ensemble size: 8\n", "mean accuracy: tensor(0.9371) std: tensor(0.0010)\n", "mean ace: tensor(0.0051) std: tensor(0.0002)\n", "mean nll: tensor(0.1827) std: tensor(0.0006)\n", "mean rps: tensor(0.0177) std: tensor(6.3395e-05)\n", "Bayesian, ensemble size: 16\n", "mean accuracy: tensor(0.9376) std: tensor(0.0008)\n", "mean ace: tensor(0.0052) std: tensor(0.0001)\n", "mean nll: tensor(0.1818) std: tensor(0.0006)\n", "mean rps: tensor(0.0177) std: tensor(1.0524e-05)\n"]}], "source": ["#Bayesian\n", "burnin_epochs=10\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_bayes(pow(2,it),par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_bayes_SG_HMC.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chain:  0 /epoch: 0\n", "NLL: tensor(2.2595)\n", "chain:  0 /epoch: 1\n", "NLL: tensor(3.2714)\n", "chain:  0 /epoch: 2\n", "NLL: tensor(3.9557)\n", "chain:  0 /epoch: 3\n", "NLL: tensor(4.4849)\n", "chain:  0 /epoch: 4\n", "NLL: tensor(4.3363)\n", "chain:  0 /epoch: 5\n", "NLL: tensor(3.9835)\n", "chain:  0 /epoch: 6\n", "NLL: tensor(5.3242)\n", "chain:  0 /epoch: 7\n", "NLL: tensor(3.3267)\n", "chain:  0 /epoch: 8\n", "NLL: tensor(3.2231)\n", "chain:  0 /epoch: 9\n", "NLL: tensor(3.9681)\n", "chain:  0 /epoch: 10\n", "NLL: tensor(4.1702)\n", "chain:  0 /epoch: 11\n", "NLL: tensor(3.9891)\n", "chain:  0 /epoch: 12\n", "NLL: tensor(3.8212)\n", "chain:  0 /epoch: 13\n", "NLL: tensor(3.0601)\n", "chain:  0 /epoch: 14\n", "NLL: tensor(3.1426)\n", "chain:  0 /epoch: 15\n", "NLL: tensor(3.3896)\n", "chain:  0 /epoch: 16\n", "NLL: tensor(2.3290)\n", "chain:  0 /epoch: 17\n", "NLL: tensor(2.2624)\n", "chain:  0 /epoch: 18\n", "NLL: tensor(2.2708)\n", "chain:  0 /epoch: 19\n", "NLL: tensor(2.3081)\n", "chain:  0 /epoch: 20\n", "NLL: tensor(2.4802)\n", "chain:  0 /epoch: 21\n", "NLL: tensor(2.0481)\n", "chain:  0 /epoch: 22\n", "NLL: tensor(2.8352)\n", "chain:  0 /epoch: 23\n", "NLL: tensor(1.2631)\n", "chain:  0 /epoch: 24\n", "NLL: tensor(1.7498)\n", "chain:  0 /epoch: 25\n", "NLL: tensor(1.8773)\n", "chain:  0 /epoch: 26\n", "NLL: tensor(2.0787)\n", "chain:  0 /epoch: 27\n", "NLL: tensor(1.3866)\n", "chain:  0 /epoch: 28\n", "NLL: tensor(1.7951)\n", "chain:  0 /epoch: 29\n", "NLL: tensor(1.9715)\n", "chain:  0 /epoch: 30\n", "NLL: tensor(1.4960)\n", "chain:  0 /epoch: 31\n", "NLL: tensor(1.4432)\n", "chain:  0 /epoch: 32\n", "NLL: tensor(1.3559)\n", "chain:  0 /epoch: 33\n", "NLL: tensor(1.0706)\n", "chain:  0 /epoch: 34\n", "NLL: tensor(1.2767)\n", "chain:  0 /epoch: 35\n", "NLL: tensor(0.9627)\n", "chain:  0 /epoch: 36\n", "NLL: tensor(0.8846)\n", "chain:  0 /epoch: 37\n", "NLL: tensor(0.7295)\n", "chain:  0 /epoch: 38\n", "NLL: tensor(0.9705)\n", "chain:  0 /epoch: 39\n", "NLL: tensor(0.8341)\n", "chain:  1 /epoch: 0\n", "NLL: tensor(3.0244)\n", "chain:  1 /epoch: 1\n", "NLL: tensor(2.6602)\n", "chain:  1 /epoch: 2\n", "NLL: tensor(2.5847)\n", "chain:  1 /epoch: 3\n", "NLL: tensor(4.3128)\n", "chain:  1 /epoch: 4\n", "NLL: tensor(2.5851)\n", "chain:  1 /epoch: 5\n", "NLL: tensor(3.4841)\n", "chain:  1 /epoch: 6\n", "NLL: tensor(3.8051)\n", "chain:  1 /epoch: 7\n", "NLL: tensor(2.5049)\n", "chain:  1 /epoch: 8\n", "NLL: tensor(3.5613)\n", "chain:  1 /epoch: 9\n", "NLL: tensor(2.1949)\n", "chain:  1 /epoch: 10\n", "NLL: tensor(3.3139)\n", "chain:  1 /epoch: 11\n", "NLL: tensor(2.5876)\n", "chain:  1 /epoch: 12\n", "NLL: tensor(3.2394)\n", "chain:  1 /epoch: 13\n", "NLL: tensor(4.5318)\n", "chain:  1 /epoch: 14\n", "NLL: tensor(3.5766)\n", "chain:  1 /epoch: 15\n", "NLL: tensor(3.1888)\n", "chain:  1 /epoch: 16\n", "NLL: tensor(4.3713)\n", "chain:  1 /epoch: 17\n", "NLL: tensor(2.8663)\n", "chain:  1 /epoch: 18\n", "NLL: tensor(2.2237)\n", "chain:  1 /epoch: 19\n", "NLL: tensor(2.5123)\n", "chain:  1 /epoch: 20\n", "NLL: tensor(3.8485)\n", "chain:  1 /epoch: 21\n", "NLL: tensor(2.2496)\n", "chain:  1 /epoch: 22\n", "NLL: tensor(2.7039)\n", "chain:  1 /epoch: 23\n", "NLL: tensor(2.3614)\n", "chain:  1 /epoch: 24\n", "NLL: tensor(2.4608)\n", "chain:  1 /epoch: 25\n", "NLL: tensor(2.2187)\n", "chain:  1 /epoch: 26\n", "NLL: tensor(3.0769)\n", "chain:  1 /epoch: 27\n", "NLL: tensor(2.3321)\n", "chain:  1 /epoch: 28\n", "NLL: tensor(1.4790)\n", "chain:  1 /epoch: 29\n", "NLL: tensor(1.6183)\n", "chain:  1 /epoch: 30\n", "NLL: tensor(1.3305)\n", "chain:  1 /epoch: 31\n", "NLL: tensor(1.5968)\n", "chain:  1 /epoch: 32\n", "NLL: tensor(1.6335)\n", "chain:  1 /epoch: 33\n", "NLL: tensor(1.4992)\n", "chain:  1 /epoch: 34\n", "NLL: tensor(1.1181)\n", "chain:  1 /epoch: 35\n", "NLL: tensor(1.3941)\n", "chain:  1 /epoch: 36\n", "NLL: tensor(1.2567)\n", "chain:  1 /epoch: 37\n", "NLL: tensor(0.9019)\n", "chain:  1 /epoch: 38\n", "NLL: tensor(1.2231)\n", "chain:  1 /epoch: 39\n", "NLL: tensor(1.1333)\n", "chain:  2 /epoch: 0\n", "NLL: tensor(3.7972)\n", "chain:  2 /epoch: 1\n", "NLL: tensor(3.1783)\n", "chain:  2 /epoch: 2\n", "NLL: tensor(2.5586)\n", "chain:  2 /epoch: 3\n", "NLL: tensor(3.0296)\n", "chain:  2 /epoch: 4\n", "NLL: tensor(3.9712)\n", "chain:  2 /epoch: 5\n", "NLL: tensor(4.6029)\n", "chain:  2 /epoch: 6\n", "NLL: tensor(3.8362)\n", "chain:  2 /epoch: 7\n", "NLL: tensor(3.1221)\n", "chain:  2 /epoch: 8\n", "NLL: tensor(2.8825)\n", "chain:  2 /epoch: 9\n", "NLL: tensor(2.1752)\n", "chain:  2 /epoch: 10\n", "NLL: tensor(2.6916)\n", "chain:  2 /epoch: 11\n", "NLL: tensor(3.1300)\n", "chain:  2 /epoch: 12\n", "NLL: tensor(3.4627)\n", "chain:  2 /epoch: 13\n", "NLL: tensor(3.1782)\n", "chain:  2 /epoch: 14\n", "NLL: tensor(2.7097)\n", "chain:  2 /epoch: 15\n", "NLL: tensor(3.1702)\n", "chain:  2 /epoch: 16\n", "NLL: tensor(3.2982)\n", "chain:  2 /epoch: 17\n", "NLL: tensor(2.3495)\n", "chain:  2 /epoch: 18\n", "NLL: tensor(2.5310)\n", "chain:  2 /epoch: 19\n", "NLL: tensor(1.6033)\n", "chain:  2 /epoch: 20\n", "NLL: tensor(1.5633)\n", "chain:  2 /epoch: 21\n", "NLL: tensor(2.0903)\n", "chain:  2 /epoch: 22\n", "NLL: tensor(1.3033)\n", "chain:  2 /epoch: 23\n", "NLL: tensor(1.7152)\n", "chain:  2 /epoch: 24\n", "NLL: tensor(1.8731)\n", "chain:  2 /epoch: 25\n", "NLL: tensor(1.5995)\n", "chain:  2 /epoch: 26\n", "NLL: tensor(0.9769)\n", "chain:  2 /epoch: 27\n", "NLL: tensor(1.1010)\n", "chain:  2 /epoch: 28\n", "NLL: tensor(0.9465)\n", "chain:  2 /epoch: 29\n", "NLL: tensor(1.2614)\n", "chain:  2 /epoch: 30\n", "NLL: tensor(1.2520)\n", "chain:  2 /epoch: 31\n", "NLL: tensor(0.9305)\n", "chain:  2 /epoch: 32\n", "NLL: tensor(1.0409)\n", "chain:  2 /epoch: 33\n", "NLL: tensor(1.0862)\n", "chain:  2 /epoch: 34\n", "NLL: tensor(0.8888)\n", "chain:  2 /epoch: 35\n", "NLL: tensor(1.3237)\n", "chain:  2 /epoch: 36\n", "NLL: tensor(1.2156)\n", "chain:  2 /epoch: 37\n", "NLL: tensor(0.7908)\n", "chain:  2 /epoch: 38\n", "NLL: tensor(0.9998)\n", "chain:  2 /epoch: 39\n", "NLL: tensor(1.0794)\n", "chain:  3 /epoch: 0\n", "NLL: tensor(2.7038)\n", "chain:  3 /epoch: 1\n", "NLL: tensor(3.3194)\n", "chain:  3 /epoch: 2\n", "NLL: tensor(4.7407)\n", "chain:  3 /epoch: 3\n", "NLL: tensor(2.6679)\n", "chain:  3 /epoch: 4\n", "NLL: tensor(3.4489)\n", "chain:  3 /epoch: 5\n", "NLL: tensor(2.9107)\n", "chain:  3 /epoch: 6\n", "NLL: tensor(4.9856)\n", "chain:  3 /epoch: 7\n", "NLL: tensor(5.8802)\n", "chain:  3 /epoch: 8\n", "NLL: tensor(4.0264)\n", "chain:  3 /epoch: 9\n", "NLL: tensor(6.0857)\n", "chain:  3 /epoch: 10\n", "NLL: tensor(6.4822)\n", "chain:  3 /epoch: 11\n", "NLL: tensor(5.3467)\n", "chain:  3 /epoch: 12\n", "NLL: tensor(5.8792)\n", "chain:  3 /epoch: 13\n", "NLL: tensor(5.2028)\n", "chain:  3 /epoch: 14\n", "NLL: tensor(4.1737)\n", "chain:  3 /epoch: 15\n", "NLL: tensor(5.3439)\n", "chain:  3 /epoch: 16\n", "NLL: tensor(6.6933)\n", "chain:  3 /epoch: 17\n", "NLL: tensor(4.3260)\n", "chain:  3 /epoch: 18\n", "NLL: tensor(4.1736)\n", "chain:  3 /epoch: 19\n", "NLL: tensor(3.7093)\n", "chain:  3 /epoch: 20\n", "NLL: tensor(4.6511)\n", "chain:  3 /epoch: 21\n", "NLL: tensor(2.4176)\n", "chain:  3 /epoch: 22\n", "NLL: tensor(3.2772)\n", "chain:  3 /epoch: 23\n", "NLL: tensor(3.1944)\n", "chain:  3 /epoch: 24\n", "NLL: tensor(7.4010)\n", "chain:  3 /epoch: 25\n", "NLL: tensor(5.0834)\n", "chain:  3 /epoch: 26\n", "NLL: tensor(4.3290)\n", "chain:  3 /epoch: 27\n", "NLL: tensor(3.6636)\n", "chain:  3 /epoch: 28\n", "NLL: tensor(2.5372)\n", "chain:  3 /epoch: 29\n", "NLL: tensor(4.4767)\n", "chain:  3 /epoch: 30\n", "NLL: tensor(5.3153)\n", "chain:  3 /epoch: 31\n", "NLL: tensor(5.4003)\n", "chain:  3 /epoch: 32\n", "NLL: tensor(6.3988)\n", "chain:  3 /epoch: 33\n", "NLL: tensor(4.2790)\n", "chain:  3 /epoch: 34\n", "NLL: tensor(3.3705)\n", "chain:  3 /epoch: 35\n", "NLL: tensor(3.6141)\n", "chain:  3 /epoch: 36\n", "NLL: tensor(2.6335)\n", "chain:  3 /epoch: 37\n", "NLL: tensor(2.3689)\n", "chain:  3 /epoch: 38\n", "NLL: tensor(3.0333)\n", "chain:  3 /epoch: 39\n", "NLL: tensor(1.9777)\n", "tensor(1.6629)\n"]}], "source": ["def GRdiagnostics(res):\n", "  J=res.shape[0] #Number of chains\n", "  L=res.shape[1] #Number of samples after burnin\n", "  res_means=res.mean(dim=1)\n", "  res_mean=res_means.mean()\n", "  B=(res_means-res_mean).pow(2).sum()*L/(J-1)\n", "  W=(res_means.reshape([J,1])@torch.ones([1,L])-res).pow(2).sum()/(J*(L-1))\n", "  R=(W*(L-1)/L+B/L)/W\n", "  return R\n", "\n", "\n", "par_chains=4\n", "no_GR_epochs=40\n", "test_prob_GR_arr=torch.zeros([test_size,num_classes])\n", "nll_GR_arr=torch.zeros([par_chains,no_GR_epochs])\n", "for chain in range(par_chains):\n", "    net=copy.deepcopy(net_star)\n", "    with torch.no_grad():\n", "      for par in net.parameters():\n", "        par+=(torch.randn_like(par))/torch.sqrt(torch.tensor(4))\n", "        par.v = torch.randn_like(par,device=device)\n", "    for par in list(net.parameters()):\n", "      par.v = torch.randn_like(par,device=device)          \n", "    for epoch in range(no_GR_epochs):\n", "      print(\"chain: \",chain, \"/epoch:\",epoch)\n", "      for it in range(batch_size):\n", "        b=torch.randint(high=no_batches,size=(1,1))        \n", "        EM_step(net, h, gam,b)\n", "\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        outputt = net(imagest).detach()\n", "        test_prob_GR_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:]=torch.softmax(outputt,dim=1)\n", "      \n", "      nll_GR_arr[chain,epoch]=nll_calc(test_prob_GR_arr,test_labels_arr)\n", "      print(\"NLL:\", nll_GR_arr[chain,epoch])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor(2.1597)\n"]}], "source": ["print(GRdiagnostics(nll_GR_arr[:,10:40]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#test_prob=torch.Tensor(test_prob_arr[:,:,29,0]).reshape(test_size,num_classes)\n", "#torch.cumsum(test_prob[1,:].reshape(num_classes),0)\n", "#rps_single(test_prob[1,:].reshape(num_classes),test_labels_arr[1])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Non-Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9304) std: tensor(0.0022)\n", "mean ace: tensor(0.0468) std: tensor(0.0023)\n", "mean nll: tensor(0.3279) std: tensor(0.0180)\n", "mean rps: tensor(0.0217) std: tensor(0.0006)\n", "Non-Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.9392) std: tensor(0.0017)\n", "mean ace: tensor(0.0223) std: tensor(0.0023)\n", "mean nll: tensor(0.2338) std: tensor(0.0086)\n", "mean rps: tensor(0.0180) std: tensor(0.0004)\n", "Non-Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9438) std: tensor(0.0014)\n", "mean ace: tensor(0.0125) std: tensor(0.0014)\n", "mean nll: tensor(0.1914) std: tensor(0.0061)\n", "mean rps: tensor(0.0162) std: tensor(0.0004)\n", "Non-Bayesian, ensemble size: 8\n", "mean accuracy: tensor(0.9462) std: tensor(0.0012)\n", "mean ace: tensor(0.0087) std: tensor(0.0006)\n", "mean nll: tensor(0.1706) std: tensor(0.0044)\n", "mean rps: tensor(0.0153) std: tensor(0.0003)\n", "Non-Bayesian, ensemble size: 16\n", "mean accuracy: tensor(0.9478) std: tensor(0.0007)\n", "mean ace: tensor(0.0063) std: tensor(0.0008)\n", "mean nll: tensor(0.1602) std: tensor(0.0028)\n", "mean rps: tensor(0.0148) std: tensor(0.0002)\n"]}], "source": ["#no bayesian\n", "par_runs=64\n", "def compute_acc_ace_rps_no_bayes(es):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,14,it*es:(it+1)*es]).mean(-1).reshape(test_size,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "    print(\"Non-Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "acc=torch.zeros(5)\n", "acc_std=torch.zeros(5)\n", "ace=torch.zeros(5)\n", "ace_std=torch.zeros(5)\n", "rps=torch.zeros(5)\n", "rps_std=torch.zeros(5)\n", "nll=torch.zeros(5)\n", "nll_std=torch.zeros(5)\n", "[acc[0],acc_std[0],ace[0],ace_std[0],rps[0],rps_std[0],nll[0],nll_std[0]]=compute_acc_ace_rps_no_bayes(1)\n", "[acc[1],acc_std[1],ace[1],ace_std[1],rps[1],rps_std[1],nll[1],nll_std[1]]=compute_acc_ace_rps_no_bayes(2)\n", "[acc[2],acc_std[2],ace[2],ace_std[2],rps[2],rps_std[2],nll[2],nll_std[2]]=compute_acc_ace_rps_no_bayes(4)\n", "[acc[3],acc_std[3],ace[3],ace_std[3],rps[3],rps_std[3],nll[3],nll_std[3]]=compute_acc_ace_rps_no_bayes(8)\n", "[acc[4],acc_std[4],ace[4],ace_std[4],rps[4],rps_std[4],nll[4],nll_std[4]]=compute_acc_ace_rps_no_bayes(16)\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_no_bayes_SG_HMC.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Non-Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9296) std: tensor(0.0020)\n", "mean ace: tensor(0.0555) std: tensor(0.0018)\n", "mean nll: tensor(0.4709) std: tensor(0.0251)\n", "mean rps: tensor(0.0230) std: tensor(0.0008)\n", "Non-Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.9383) std: tensor(0.0015)\n", "mean ace: tensor(0.0294) std: tensor(0.0016)\n", "mean nll: tensor(0.3109) std: tensor(0.0127)\n", "mean rps: tensor(0.0188) std: tensor(0.0005)\n", "Non-Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9422) std: tensor(0.0010)\n", "mean ace: tensor(0.0177) std: tensor(0.0011)\n", "mean nll: tensor(0.2385) std: tensor(0.0066)\n", "mean rps: tensor(0.0168) std: tensor(0.0003)\n", "Non-Bayesian, ensemble size: 8\n", "mean accuracy: tensor(0.9443) std: tensor(0.0006)\n", "mean ace: tensor(0.0126) std: tensor(0.0007)\n", "mean nll: tensor(0.2025) std: tensor(0.0042)\n", "mean rps: tensor(0.0157) std: tensor(0.0002)\n", "Non-Bayesian, ensemble size: 16\n", "mean accuracy: tensor(0.9459) std: tensor(0.0009)\n", "mean ace: tensor(0.0098) std: tensor(0.0007)\n", "mean nll: tensor(0.1832) std: tensor(0.0029)\n", "mean rps: tensor(0.0152) std: tensor(8.2395e-05)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SWA, ensemble size: 1\n", "mean accuracy: tensor(0.9356) std: tensor(0.0015)\n", "mean ace: tensor(0.0459) std: tensor(0.0016)\n", "mean nll: tensor(0.3383) std: tensor(0.0126)\n", "mean rps: tensor(0.0207) std: tensor(0.0005)\n", "SWA, ensemble size: 2\n", "mean accuracy: tensor(0.9420) std: tensor(0.0013)\n", "mean ace: tensor(0.0256) std: tensor(0.0013)\n", "mean nll: tensor(0.2467) std: tensor(0.0063)\n", "mean rps: tensor(0.0176) std: tensor(0.0003)\n", "SWA, ensemble size: 4\n", "mean accuracy: tensor(0.9453) std: tensor(0.0011)\n", "mean ace: tensor(0.0174) std: tensor(0.0010)\n", "mean nll: tensor(0.2038) std: tensor(0.0049)\n", "mean rps: tensor(0.0160) std: tensor(0.0002)\n", "SWA, ensemble size: 8\n", "mean accuracy: tensor(0.9474) std: tensor(0.0009)\n", "mean ace: tensor(0.0132) std: tensor(0.0008)\n", "mean nll: tensor(0.1816) std: tensor(0.0026)\n", "mean rps: tensor(0.0152) std: tensor(0.0001)\n", "SWA, ensemble size: 16\n", "mean accuracy: tensor(0.9489) std: tensor(0.0005)\n", "mean ace: tensor(0.0107) std: tensor(0.0004)\n", "mean nll: tensor(0.1700) std: tensor(0.0020)\n", "mean rps: tensor(0.0148) std: tensor(0.0001)\n"]}], "source": ["#swa\n", "def compute_acc_ace_rps_swa(es):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,19,it*es:(it+1)*es]).mean(-1).reshape(test_size,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "    print(\"SWA, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "acc=torch.zeros(5)\n", "acc_std=torch.zeros(5)\n", "ace=torch.zeros(5)\n", "ace_std=torch.zeros(5)\n", "rps=torch.zeros(5)\n", "rps_std=torch.zeros(5)\n", "nll=torch.zeros(5)\n", "nll_std=torch.zeros(5)\n", "[acc[0],acc_std[0],ace[0],ace_std[0],rps[0],rps_std[0],nll[0],nll_std[0]]=compute_acc_ace_rps_swa(1)\n", "[acc[1],acc_std[1],ace[1],ace_std[1],rps[1],rps_std[1],nll[1],nll_std[1]]=compute_acc_ace_rps_swa(2)\n", "[acc[2],acc_std[2],ace[2],ace_std[2],rps[2],rps_std[2],nll[2],nll_std[2]]=compute_acc_ace_rps_swa(4)\n", "[acc[3],acc_std[3],ace[3],ace_std[3],rps[3],rps_std[3],nll[3],nll_std[3]]=compute_acc_ace_rps_swa(8)\n", "[acc[4],acc_std[4],ace[4],ace_std[4],rps[4],rps_std[4],nll[4],nll_std[4]]=compute_acc_ace_rps_swa(16)\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_swa_SG_HMC.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["SWA, ensemble size: 1\n", "mean accuracy: tensor(0.9337) std: tensor(0.0016)\n", "mean ace: tensor(0.0537) std: tensor(0.0016)\n", "mean nll: tensor(0.4826) std: tensor(0.0191)\n", "mean rps: tensor(0.0220) std: tensor(0.0006)\n", "SWA, ensemble size: 2\n", "mean accuracy: tensor(0.9403) std: tensor(0.0012)\n", "mean ace: tensor(0.0309) std: tensor(0.0012)\n", "mean nll: tensor(0.3312) std: tensor(0.0111)\n", "mean rps: tensor(0.0184) std: tensor(0.0004)\n", "SWA, ensemble size: 4\n", "mean accuracy: tensor(0.9435) std: tensor(0.0011)\n", "mean ace: tensor(0.0223) std: tensor(0.0010)\n", "mean nll: tensor(0.2579) std: tensor(0.0078)\n", "mean rps: tensor(0.0166) std: tensor(0.0003)\n", "SWA, ensemble size: 8\n", "mean accuracy: tensor(0.9453) std: tensor(0.0008)\n", "mean ace: tensor(0.0180) std: tensor(0.0011)\n", "mean nll: tensor(0.2186) std: tensor(0.0036)\n", "mean rps: tensor(0.0157) std: tensor(0.0002)\n", "SWA, ensemble size: 16\n", "mean accuracy: tensor(0.9462) std: tensor(0.0008)\n", "mean ace: tensor(0.0158) std: tensor(0.0009)\n", "mean nll: tensor(0.1964) std: tensor(0.0034)\n", "mean rps: tensor(0.0153) std: tensor(5.5335e-05)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9312) std: tensor(0.0015)\n", "mean ace: tensor(0.0166) std: tensor(0.0021)\n", "mean nll: tensor(0.1955) std: tensor(0.0025)\n", "mean rps: tensor(0.0189) std: tensor(0.0002)\n", "Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.9345) std: tensor(0.0010)\n", "mean ace: tensor(0.0217) std: tensor(0.0017)\n", "mean nll: tensor(0.1883) std: tensor(0.0014)\n", "mean rps: tensor(0.0183) std: tensor(0.0001)\n", "Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9360) std: tensor(0.0008)\n", "mean ace: tensor(0.0242) std: tensor(0.0012)\n", "mean nll: tensor(0.1850) std: tensor(0.0009)\n", "mean rps: tensor(0.0180) std: tensor(9.6171e-05)\n", "Bayesian, ensemble size: 8\n", "mean accuracy: tensor(0.9365) std: tensor(0.0007)\n", "mean ace: tensor(0.0252) std: tensor(0.0009)\n", "mean nll: tensor(0.1833) std: tensor(0.0006)\n", "mean rps: tensor(0.0178) std: tensor(6.9177e-05)\n", "Bayesian, ensemble size: 16\n", "mean accuracy: tensor(0.9373) std: tensor(0.0010)\n", "mean ace: tensor(0.0262) std: tensor(0.0013)\n", "mean nll: tensor(0.1825) std: tensor(0.0005)\n", "mean rps: tensor(0.0177) std: tensor(6.2021e-05)\n"]}], "source": ["#Bayesian\n", "def compute_acc_ace_rps_bayes(es):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,30:60,it*es:(it+1)*es]).mean(-1).mean(-1).reshape(test_size,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "    print(\"Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "\n", "\n", "acc=torch.zeros(5)\n", "acc_std=torch.zeros(5)\n", "ace=torch.zeros(5)\n", "ace_std=torch.zeros(5)\n", "rps=torch.zeros(5)\n", "rps_std=torch.zeros(5)\n", "nll=torch.zeros(5)\n", "nll_std=torch.zeros(5)\n", "[acc[0],acc_std[0],ace[0],ace_std[0],rps[0],rps_std[0],nll[0],nll_std[0]]=compute_acc_ace_rps_bayes(1)\n", "[acc[1],acc_std[1],ace[1],ace_std[1],rps[1],rps_std[1],nll[1],nll_std[1]]=compute_acc_ace_rps_bayes(2)\n", "[acc[2],acc_std[2],ace[2],ace_std[2],rps[2],rps_std[2],nll[2],nll_std[2]]=compute_acc_ace_rps_bayes(4)\n", "[acc[3],acc_std[3],ace[3],ace_std[3],rps[3],rps_std[3],nll[3],nll_std[3]]=compute_acc_ace_rps_bayes(8)\n", "[acc[4],acc_std[4],ace[4],ace_std[4],rps[4],rps_std[4],nll[4],nll_std[4]]=compute_acc_ace_rps_bayes(16)\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_bayes_SG_HMC.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9358) std: tensor(nan)\n", "mean ace: tensor(0.0123) std: tensor(nan)\n", "mean nll: tensor(0.2039) std: tensor(nan)\n", "mean rps: tensor(0.0177) std: tensor(nan)\n", "\n", "Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9400) std: tensor(nan)\n", "mean ace: tensor(0.0072) std: tensor(nan)\n", "mean nll: tensor(0.1876) std: tensor(nan)\n", "mean rps: tensor(0.0169) std: tensor(nan)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test_prob=torch.Tensor(test_prob_arr[:,7,0]).reshape(test_size,1)\n", "# ace=adaptive_calibration_error(test_labels_arr.reshape(test_size,1).numpy(),test_prob.numpy(),20)\n", "# ace"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test_prob=torch.Tensor(test_prob_arr[:,10:16,48:64]).mean(-1).mean(-1).reshape(test_size,1)\n", "# ace=adaptive_calibration_error(test_labels_arr.reshape(test_size,1).numpy(),test_prob.numpy(),20)\n", "# ace"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning: Could not load \"C:\\Users\\<USER>\\.conda\\envs\\torchenv39\\Library\\bin\\gvplugin_pango.dll\" - It was found, so perhaps one of its dependents was not.  Try ldd.\n", "Warning: no hard-coded metrics for 'Linux libertine'.  Falling back to 'Times' metrics\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 8.1.0 (0)\n", " -->\n", "<!-- Title: model Pages: 1 -->\n", "<svg width=\"135pt\" height=\"1555pt\"\n", " viewBox=\"0.00 0.00 134.52 1555.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(0.611434 0.611434) rotate(0) translate(4 2539.2)\">\n", "<title>model</title>\n", "<polygon fill=\"white\" stroke=\"none\" points=\"-4,4 -4,-2539.2 216,-2539.2 216,4 -4,4\"/>\n", "<g id=\"clust1\" class=\"cluster\">\n", "<title>cluster_2</title>\n", "<polygon fill=\"none\" stroke=\"black\" stroke-dasharray=\"5,2\" points=\"8,-730.8 8,-2493.2 204,-2493.2 204,-730.8 8,-730.8\"/>\n", "<text text-anchor=\"middle\" x=\"41.33\" y=\"-2478.4\" font-family=\"Times New Roman,serif\" font-size=\"12.00\">Sequential</text>\n", "</g>\n", "<g id=\"clust3\" class=\"cluster\">\n", "<title>cluster_4</title>\n", "<polygon fill=\"none\" stroke=\"black\" stroke-dasharray=\"5,2\" points=\"36,-8 36,-160.4 176,-160.4 176,-8 36,-8\"/>\n", "<text text-anchor=\"middle\" x=\"69.33\" y=\"-145.6\" font-family=\"Times New Roman,serif\" font-size=\"12.00\">Sequential</text>\n", "</g>\n", "<g id=\"clust2\" class=\"cluster\">\n", "<title>cluster_3</title>\n", "<polygon fill=\"none\" stroke=\"black\" stroke-dasharray=\"5,2\" points=\"20,-168.4 20,-650.8 192,-650.8 192,-168.4 20,-168.4\"/>\n", "<text text-anchor=\"middle\" x=\"53.33\" y=\"-636\" font-family=\"Times New Roman,serif\" font-size=\"12.00\">Sequential</text>\n", "</g>\n", "<!-- 0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>0</title>\n", "<polygon fill=\"lightyellow\" stroke=\"none\" points=\"165.99,-2535.2 46.01,-2535.2 46.01,-2501.2 165.99,-2501.2 165.99,-2535.2\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"46.01,-2501.2 46.01,-2535.2 104.33,-2535.2 104.33,-2501.2 46.01,-2501.2\"/>\n", "<text text-anchor=\"start\" x=\"51.01\" y=\"-2521.2\" font-family=\"Linux libertine\" font-size=\"10.00\">input&#45;tensor</text>\n", "<text text-anchor=\"start\" x=\"60.17\" y=\"-2509.2\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:0</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"104.33,-2501.2 104.33,-2535.2 165.99,-2535.2 165.99,-2501.2 104.33,-2501.2\"/>\n", "<text text-anchor=\"start\" x=\"109.33\" y=\"-2515.2\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1, 28, 28)</text>\n", "</g>\n", "<!-- 1 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>1</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-2462.8 31,-2462.8 31,-2418.8 181,-2418.8 181,-2462.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-2418.8 31,-2462.8 72,-2462.8 72,-2418.8 31,-2418.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-2443.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2431.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2440.8 72,-2462.8 112,-2462.8 112,-2440.8 72,-2440.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-2448.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2440.8 112,-2462.8 181,-2462.8 181,-2440.8 112,-2440.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-2448.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1, 28, 28) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2418.8 72,-2440.8 112,-2440.8 112,-2418.8 72,-2418.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-2426.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2418.8 112,-2440.8 181,-2440.8 181,-2418.8 112,-2418.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-2426.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32, 28, 28) </text>\n", "</g>\n", "<!-- 0&#45;&gt;1 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>0&#45;&gt;1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2501.21C106,-2493.13 106,-2483.04 106,-2473.47\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2473.64 106,-2463.64 102.5,-2473.64 109.5,-2473.64\"/>\n", "</g>\n", "<!-- 2 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>2</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-2382.8 30,-2382.8 30,-2338.8 182,-2338.8 182,-2382.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-2338.8 30,-2382.8 73,-2382.8 73,-2338.8 30,-2338.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-2363.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2351.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2360.8 73,-2382.8 113,-2382.8 113,-2360.8 73,-2360.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-2368.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2360.8 113,-2382.8 182,-2382.8 182,-2360.8 113,-2360.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2368.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32, 28, 28) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2338.8 73,-2360.8 113,-2360.8 113,-2338.8 73,-2338.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-2346.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2338.8 113,-2360.8 182,-2360.8 182,-2338.8 113,-2338.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2346.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32, 28, 28) </text>\n", "</g>\n", "<!-- 1&#45;&gt;2 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>1&#45;&gt;2</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2418.9C106,-2411.12 106,-2402.1 106,-2393.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2393.76 106,-2383.76 102.5,-2393.76 109.5,-2393.76\"/>\n", "</g>\n", "<!-- 3 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>3</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-2302.8 18.5,-2302.8 18.5,-2258.8 193.5,-2258.8 193.5,-2302.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-2258.8 18.5,-2302.8 84.5,-2302.8 84.5,-2258.8 18.5,-2258.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-2283.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2271.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-2280.8 84.5,-2302.8 124.5,-2302.8 124.5,-2280.8 84.5,-2280.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-2288.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-2280.8 124.5,-2302.8 193.5,-2302.8 193.5,-2280.8 124.5,-2280.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-2288.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32, 28, 28) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-2258.8 84.5,-2280.8 124.5,-2280.8 124.5,-2258.8 84.5,-2258.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-2266.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-2258.8 124.5,-2280.8 193.5,-2280.8 193.5,-2258.8 124.5,-2258.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-2266.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32, 28, 28) </text>\n", "</g>\n", "<!-- 2&#45;&gt;3 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>2&#45;&gt;3</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2338.9C106,-2331.12 106,-2322.1 106,-2313.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2313.76 106,-2303.76 102.5,-2313.76 109.5,-2313.76\"/>\n", "</g>\n", "<!-- 4 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>4</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-2222.8 31,-2222.8 31,-2178.8 181,-2178.8 181,-2222.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-2178.8 31,-2222.8 72,-2222.8 72,-2178.8 31,-2178.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-2203.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2191.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2200.8 72,-2222.8 112,-2222.8 112,-2200.8 72,-2200.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-2208.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2200.8 112,-2222.8 181,-2222.8 181,-2200.8 112,-2200.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-2208.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32, 28, 28) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2178.8 72,-2200.8 112,-2200.8 112,-2178.8 72,-2178.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-2186.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2178.8 112,-2200.8 181,-2200.8 181,-2178.8 112,-2178.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-2186.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 28, 28) </text>\n", "</g>\n", "<!-- 3&#45;&gt;4 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>3&#45;&gt;4</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2258.9C106,-2251.12 106,-2242.1 106,-2233.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2233.76 106,-2223.76 102.5,-2233.76 109.5,-2233.76\"/>\n", "</g>\n", "<!-- 5 -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>5</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-2142.8 30,-2142.8 30,-2098.8 182,-2098.8 182,-2142.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-2098.8 30,-2142.8 73,-2142.8 73,-2098.8 30,-2098.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-2123.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2111.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2120.8 73,-2142.8 113,-2142.8 113,-2120.8 73,-2120.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-2128.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2120.8 113,-2142.8 182,-2142.8 182,-2120.8 113,-2120.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2128.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 28, 28) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2098.8 73,-2120.8 113,-2120.8 113,-2098.8 73,-2098.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-2106.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2098.8 113,-2120.8 182,-2120.8 182,-2098.8 113,-2098.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2106.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 28, 28) </text>\n", "</g>\n", "<!-- 4&#45;&gt;5 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>4&#45;&gt;5</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2178.9C106,-2171.12 106,-2162.1 106,-2153.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2153.76 106,-2143.76 102.5,-2153.76 109.5,-2153.76\"/>\n", "</g>\n", "<!-- 6 -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>6</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"188.5,-2062.8 23.5,-2062.8 23.5,-2018.8 188.5,-2018.8 188.5,-2062.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"23.5,-2018.8 23.5,-2062.8 79.5,-2062.8 79.5,-2018.8 23.5,-2018.8\"/>\n", "<text text-anchor=\"start\" x=\"28.17\" y=\"-2043.8\" font-family=\"Linux libertine\" font-size=\"10.00\">MaxPool2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2031.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"79.5,-2040.8 79.5,-2062.8 119.5,-2062.8 119.5,-2040.8 79.5,-2040.8\"/>\n", "<text text-anchor=\"start\" x=\"87.83\" y=\"-2048.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"119.5,-2040.8 119.5,-2062.8 188.5,-2062.8 188.5,-2040.8 119.5,-2040.8\"/>\n", "<text text-anchor=\"start\" x=\"124.42\" y=\"-2048.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 28, 28) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"79.5,-2018.8 79.5,-2040.8 119.5,-2040.8 119.5,-2018.8 79.5,-2018.8\"/>\n", "<text text-anchor=\"start\" x=\"84.08\" y=\"-2026.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"119.5,-2018.8 119.5,-2040.8 188.5,-2040.8 188.5,-2018.8 119.5,-2018.8\"/>\n", "<text text-anchor=\"start\" x=\"124.42\" y=\"-2026.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "</g>\n", "<!-- 5&#45;&gt;6 -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>5&#45;&gt;6</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2098.9C106,-2091.12 106,-2082.1 106,-2073.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2073.76 106,-2063.76 102.5,-2073.76 109.5,-2073.76\"/>\n", "</g>\n", "<!-- 7 -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>7</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-1982.8 18.5,-1982.8 18.5,-1938.8 193.5,-1938.8 193.5,-1982.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-1938.8 18.5,-1982.8 84.5,-1982.8 84.5,-1938.8 18.5,-1938.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-1963.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1951.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1960.8 84.5,-1982.8 124.5,-1982.8 124.5,-1960.8 84.5,-1960.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-1968.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1960.8 124.5,-1982.8 193.5,-1982.8 193.5,-1960.8 124.5,-1960.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1968.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1938.8 84.5,-1960.8 124.5,-1960.8 124.5,-1938.8 84.5,-1938.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-1946.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1938.8 124.5,-1960.8 193.5,-1960.8 193.5,-1938.8 124.5,-1938.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1946.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "</g>\n", "<!-- 6&#45;&gt;7 -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>6&#45;&gt;7</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2018.9C106,-2011.12 106,-2002.1 106,-1993.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1993.76 106,-1983.76 102.5,-1993.76 109.5,-1993.76\"/>\n", "</g>\n", "<!-- 8 -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>8</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-1902.8 31,-1902.8 31,-1858.8 181,-1858.8 181,-1902.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-1858.8 31,-1902.8 72,-1902.8 72,-1858.8 31,-1858.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-1883.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1871.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1880.8 72,-1902.8 112,-1902.8 112,-1880.8 72,-1880.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-1888.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1880.8 112,-1902.8 181,-1902.8 181,-1880.8 112,-1880.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1888.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1858.8 72,-1880.8 112,-1880.8 112,-1858.8 72,-1858.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-1866.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1858.8 112,-1880.8 181,-1880.8 181,-1858.8 112,-1858.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1866.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "</g>\n", "<!-- 7&#45;&gt;8 -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>7&#45;&gt;8</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1938.9C106,-1931.12 106,-1922.1 106,-1913.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1913.76 106,-1903.76 102.5,-1913.76 109.5,-1913.76\"/>\n", "</g>\n", "<!-- 9 -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>9</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-1822.8 30,-1822.8 30,-1778.8 182,-1778.8 182,-1822.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-1778.8 30,-1822.8 73,-1822.8 73,-1778.8 30,-1778.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-1803.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1791.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1800.8 73,-1822.8 113,-1822.8 113,-1800.8 73,-1800.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-1808.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1800.8 113,-1822.8 182,-1822.8 182,-1800.8 113,-1800.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1808.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1778.8 73,-1800.8 113,-1800.8 113,-1778.8 73,-1778.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-1786.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1778.8 113,-1800.8 182,-1800.8 182,-1778.8 113,-1778.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1786.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "</g>\n", "<!-- 8&#45;&gt;9 -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>8&#45;&gt;9</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1858.9C106,-1851.12 106,-1842.1 106,-1833.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1833.76 106,-1823.76 102.5,-1833.76 109.5,-1833.76\"/>\n", "</g>\n", "<!-- 10 -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>10</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-1742.8 18.5,-1742.8 18.5,-1698.8 193.5,-1698.8 193.5,-1742.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-1698.8 18.5,-1742.8 84.5,-1742.8 84.5,-1698.8 18.5,-1698.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-1723.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1711.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1720.8 84.5,-1742.8 124.5,-1742.8 124.5,-1720.8 84.5,-1720.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-1728.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1720.8 124.5,-1742.8 193.5,-1742.8 193.5,-1720.8 124.5,-1720.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1728.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1698.8 84.5,-1720.8 124.5,-1720.8 124.5,-1698.8 84.5,-1698.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-1706.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1698.8 124.5,-1720.8 193.5,-1720.8 193.5,-1698.8 124.5,-1698.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1706.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "</g>\n", "<!-- 9&#45;&gt;10 -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>9&#45;&gt;10</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1778.9C106,-1771.12 106,-1762.1 106,-1753.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1753.76 106,-1743.76 102.5,-1753.76 109.5,-1753.76\"/>\n", "</g>\n", "<!-- 11 -->\n", "<g id=\"node12\" class=\"node\">\n", "<title>11</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-1662.8 28.5,-1662.8 28.5,-1618.8 183.5,-1618.8 183.5,-1662.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-1618.8 28.5,-1662.8 69.5,-1662.8 69.5,-1618.8 28.5,-1618.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-1643.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-1631.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-1640.8 69.5,-1662.8 109.5,-1662.8 109.5,-1640.8 69.5,-1640.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-1648.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-1640.8 109.5,-1662.8 183.5,-1662.8 183.5,-1640.8 109.5,-1640.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1648.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 64, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-1618.8 69.5,-1640.8 109.5,-1640.8 109.5,-1618.8 69.5,-1618.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-1626.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-1618.8 109.5,-1640.8 183.5,-1640.8 183.5,-1618.8 109.5,-1618.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-1626.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "</g>\n", "<!-- 10&#45;&gt;11 -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>10&#45;&gt;11</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1698.9C106,-1691.12 106,-1682.1 106,-1673.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1673.76 106,-1663.76 102.5,-1673.76 109.5,-1673.76\"/>\n", "</g>\n", "<!-- 12 -->\n", "<g id=\"node13\" class=\"node\">\n", "<title>12</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-1582.8 27.5,-1582.8 27.5,-1538.8 184.5,-1538.8 184.5,-1582.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-1538.8 27.5,-1582.8 70.5,-1582.8 70.5,-1538.8 27.5,-1538.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-1563.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-1551.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-1560.8 70.5,-1582.8 110.5,-1582.8 110.5,-1560.8 70.5,-1560.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-1568.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-1560.8 110.5,-1582.8 184.5,-1582.8 184.5,-1560.8 110.5,-1560.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-1568.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-1538.8 70.5,-1560.8 110.5,-1560.8 110.5,-1538.8 70.5,-1538.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-1546.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-1538.8 110.5,-1560.8 184.5,-1560.8 184.5,-1538.8 110.5,-1538.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-1546.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "</g>\n", "<!-- 11&#45;&gt;12 -->\n", "<g id=\"edge12\" class=\"edge\">\n", "<title>11&#45;&gt;12</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1618.9C106,-1611.12 106,-1602.1 106,-1593.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1593.76 106,-1583.76 102.5,-1593.76 109.5,-1593.76\"/>\n", "</g>\n", "<!-- 13 -->\n", "<g id=\"node14\" class=\"node\">\n", "<title>13</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-1502.8 16,-1502.8 16,-1458.8 196,-1458.8 196,-1502.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-1458.8 16,-1502.8 82,-1502.8 82,-1458.8 16,-1458.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-1483.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-1471.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-1480.8 82,-1502.8 122,-1502.8 122,-1480.8 82,-1480.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-1488.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-1480.8 122,-1502.8 196,-1502.8 196,-1480.8 122,-1480.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-1488.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-1458.8 82,-1480.8 122,-1480.8 122,-1458.8 82,-1458.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-1466.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-1458.8 122,-1480.8 196,-1480.8 196,-1458.8 122,-1458.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-1466.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "</g>\n", "<!-- 12&#45;&gt;13 -->\n", "<g id=\"edge13\" class=\"edge\">\n", "<title>12&#45;&gt;13</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1538.9C106,-1531.12 106,-1522.1 106,-1513.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1513.76 106,-1503.76 102.5,-1513.76 109.5,-1513.76\"/>\n", "</g>\n", "<!-- 14 -->\n", "<g id=\"node15\" class=\"node\">\n", "<title>14</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-1422.8 28.5,-1422.8 28.5,-1378.8 183.5,-1378.8 183.5,-1422.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-1378.8 28.5,-1422.8 69.5,-1422.8 69.5,-1378.8 28.5,-1378.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-1403.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-1391.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-1400.8 69.5,-1422.8 109.5,-1422.8 109.5,-1400.8 69.5,-1400.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-1408.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-1400.8 109.5,-1422.8 183.5,-1422.8 183.5,-1400.8 109.5,-1400.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-1408.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-1378.8 69.5,-1400.8 109.5,-1400.8 109.5,-1378.8 69.5,-1378.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-1386.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-1378.8 109.5,-1400.8 183.5,-1400.8 183.5,-1378.8 109.5,-1378.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-1386.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "</g>\n", "<!-- 13&#45;&gt;14 -->\n", "<g id=\"edge14\" class=\"edge\">\n", "<title>13&#45;&gt;14</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1458.9C106,-1451.12 106,-1442.1 106,-1433.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1433.76 106,-1423.76 102.5,-1433.76 109.5,-1433.76\"/>\n", "</g>\n", "<!-- 15 -->\n", "<g id=\"node16\" class=\"node\">\n", "<title>15</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"191,-1342.8 21,-1342.8 21,-1298.8 191,-1298.8 191,-1342.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"21,-1298.8 21,-1342.8 77,-1342.8 77,-1298.8 21,-1298.8\"/>\n", "<text text-anchor=\"start\" x=\"25.67\" y=\"-1323.8\" font-family=\"Linux libertine\" font-size=\"10.00\">MaxPool2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-1311.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"77,-1320.8 77,-1342.8 117,-1342.8 117,-1320.8 77,-1320.8\"/>\n", "<text text-anchor=\"start\" x=\"85.33\" y=\"-1328.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"117,-1320.8 117,-1342.8 191,-1342.8 191,-1320.8 117,-1320.8\"/>\n", "<text text-anchor=\"start\" x=\"121.92\" y=\"-1328.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 14, 14) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"77,-1298.8 77,-1320.8 117,-1320.8 117,-1298.8 77,-1298.8\"/>\n", "<text text-anchor=\"start\" x=\"81.58\" y=\"-1306.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"117,-1298.8 117,-1320.8 191,-1320.8 191,-1298.8 117,-1298.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-1306.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 7, 7) </text>\n", "</g>\n", "<!-- 14&#45;&gt;15 -->\n", "<g id=\"edge15\" class=\"edge\">\n", "<title>14&#45;&gt;15</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1378.9C106,-1371.12 106,-1362.1 106,-1353.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1353.76 106,-1343.76 102.5,-1353.76 109.5,-1353.76\"/>\n", "</g>\n", "<!-- 16 -->\n", "<g id=\"node17\" class=\"node\">\n", "<title>16</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"191,-1262.8 21,-1262.8 21,-1218.8 191,-1218.8 191,-1262.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"21,-1218.8 21,-1262.8 87,-1262.8 87,-1218.8 21,-1218.8\"/>\n", "<text text-anchor=\"start\" x=\"25.67\" y=\"-1243.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-1231.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"87,-1240.8 87,-1262.8 127,-1262.8 127,-1240.8 87,-1240.8\"/>\n", "<text text-anchor=\"start\" x=\"95.33\" y=\"-1248.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"127,-1240.8 127,-1262.8 191,-1262.8 191,-1240.8 127,-1240.8\"/>\n", "<text text-anchor=\"start\" x=\"131.92\" y=\"-1248.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"87,-1218.8 87,-1240.8 127,-1240.8 127,-1218.8 87,-1218.8\"/>\n", "<text text-anchor=\"start\" x=\"91.58\" y=\"-1226.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"127,-1218.8 127,-1240.8 191,-1240.8 191,-1218.8 127,-1218.8\"/>\n", "<text text-anchor=\"start\" x=\"131.92\" y=\"-1226.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 7, 7) </text>\n", "</g>\n", "<!-- 15&#45;&gt;16 -->\n", "<g id=\"edge16\" class=\"edge\">\n", "<title>15&#45;&gt;16</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1298.9C106,-1291.12 106,-1282.1 106,-1273.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1273.76 106,-1263.76 102.5,-1273.76 109.5,-1273.76\"/>\n", "</g>\n", "<!-- 17 -->\n", "<g id=\"node18\" class=\"node\">\n", "<title>17</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"178.5,-1182.8 33.5,-1182.8 33.5,-1138.8 178.5,-1138.8 178.5,-1182.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"33.5,-1138.8 33.5,-1182.8 74.5,-1182.8 74.5,-1138.8 33.5,-1138.8\"/>\n", "<text text-anchor=\"start\" x=\"38.17\" y=\"-1163.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-1151.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"74.5,-1160.8 74.5,-1182.8 114.5,-1182.8 114.5,-1160.8 74.5,-1160.8\"/>\n", "<text text-anchor=\"start\" x=\"82.83\" y=\"-1168.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"114.5,-1160.8 114.5,-1182.8 178.5,-1182.8 178.5,-1160.8 114.5,-1160.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-1168.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 128, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"74.5,-1138.8 74.5,-1160.8 114.5,-1160.8 114.5,-1138.8 74.5,-1138.8\"/>\n", "<text text-anchor=\"start\" x=\"79.08\" y=\"-1146.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"114.5,-1138.8 114.5,-1160.8 178.5,-1160.8 178.5,-1138.8 114.5,-1138.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-1146.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "</g>\n", "<!-- 16&#45;&gt;17 -->\n", "<g id=\"edge17\" class=\"edge\">\n", "<title>16&#45;&gt;17</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1218.9C106,-1211.12 106,-1202.1 106,-1193.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1193.76 106,-1183.76 102.5,-1193.76 109.5,-1193.76\"/>\n", "</g>\n", "<!-- 18 -->\n", "<g id=\"node19\" class=\"node\">\n", "<title>18</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"179.5,-1102.8 32.5,-1102.8 32.5,-1058.8 179.5,-1058.8 179.5,-1102.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"32.5,-1058.8 32.5,-1102.8 75.5,-1102.8 75.5,-1058.8 32.5,-1058.8\"/>\n", "<text text-anchor=\"start\" x=\"37.33\" y=\"-1083.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-1071.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"75.5,-1080.8 75.5,-1102.8 115.5,-1102.8 115.5,-1080.8 75.5,-1080.8\"/>\n", "<text text-anchor=\"start\" x=\"83.83\" y=\"-1088.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"115.5,-1080.8 115.5,-1102.8 179.5,-1102.8 179.5,-1080.8 115.5,-1080.8\"/>\n", "<text text-anchor=\"start\" x=\"120.42\" y=\"-1088.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"75.5,-1058.8 75.5,-1080.8 115.5,-1080.8 115.5,-1058.8 75.5,-1058.8\"/>\n", "<text text-anchor=\"start\" x=\"80.08\" y=\"-1066.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"115.5,-1058.8 115.5,-1080.8 179.5,-1080.8 179.5,-1058.8 115.5,-1058.8\"/>\n", "<text text-anchor=\"start\" x=\"120.42\" y=\"-1066.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "</g>\n", "<!-- 17&#45;&gt;18 -->\n", "<g id=\"edge18\" class=\"edge\">\n", "<title>17&#45;&gt;18</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1138.9C106,-1131.12 106,-1122.1 106,-1113.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1113.76 106,-1103.76 102.5,-1113.76 109.5,-1113.76\"/>\n", "</g>\n", "<!-- 19 -->\n", "<g id=\"node20\" class=\"node\">\n", "<title>19</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"191,-1022.8 21,-1022.8 21,-978.8 191,-978.8 191,-1022.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"21,-978.8 21,-1022.8 87,-1022.8 87,-978.8 21,-978.8\"/>\n", "<text text-anchor=\"start\" x=\"25.67\" y=\"-1003.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-991.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"87,-1000.8 87,-1022.8 127,-1022.8 127,-1000.8 87,-1000.8\"/>\n", "<text text-anchor=\"start\" x=\"95.33\" y=\"-1008.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"127,-1000.8 127,-1022.8 191,-1022.8 191,-1000.8 127,-1000.8\"/>\n", "<text text-anchor=\"start\" x=\"131.92\" y=\"-1008.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"87,-978.8 87,-1000.8 127,-1000.8 127,-978.8 87,-978.8\"/>\n", "<text text-anchor=\"start\" x=\"91.58\" y=\"-986.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"127,-978.8 127,-1000.8 191,-1000.8 191,-978.8 127,-978.8\"/>\n", "<text text-anchor=\"start\" x=\"131.92\" y=\"-986.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "</g>\n", "<!-- 18&#45;&gt;19 -->\n", "<g id=\"edge19\" class=\"edge\">\n", "<title>18&#45;&gt;19</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1058.9C106,-1051.12 106,-1042.1 106,-1033.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1033.76 106,-1023.76 102.5,-1033.76 109.5,-1033.76\"/>\n", "</g>\n", "<!-- 20 -->\n", "<g id=\"node21\" class=\"node\">\n", "<title>20</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"178.5,-942.8 33.5,-942.8 33.5,-898.8 178.5,-898.8 178.5,-942.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"33.5,-898.8 33.5,-942.8 74.5,-942.8 74.5,-898.8 33.5,-898.8\"/>\n", "<text text-anchor=\"start\" x=\"38.17\" y=\"-923.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-911.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"74.5,-920.8 74.5,-942.8 114.5,-942.8 114.5,-920.8 74.5,-920.8\"/>\n", "<text text-anchor=\"start\" x=\"82.83\" y=\"-928.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"114.5,-920.8 114.5,-942.8 178.5,-942.8 178.5,-920.8 114.5,-920.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-928.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"74.5,-898.8 74.5,-920.8 114.5,-920.8 114.5,-898.8 74.5,-898.8\"/>\n", "<text text-anchor=\"start\" x=\"79.08\" y=\"-906.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"114.5,-898.8 114.5,-920.8 178.5,-920.8 178.5,-898.8 114.5,-898.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-906.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "</g>\n", "<!-- 19&#45;&gt;20 -->\n", "<g id=\"edge20\" class=\"edge\">\n", "<title>19&#45;&gt;20</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-978.9C106,-971.12 106,-962.1 106,-953.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-953.76 106,-943.76 102.5,-953.76 109.5,-953.76\"/>\n", "</g>\n", "<!-- 21 -->\n", "<g id=\"node22\" class=\"node\">\n", "<title>21</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"179.5,-862.8 32.5,-862.8 32.5,-818.8 179.5,-818.8 179.5,-862.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"32.5,-818.8 32.5,-862.8 75.5,-862.8 75.5,-818.8 32.5,-818.8\"/>\n", "<text text-anchor=\"start\" x=\"37.33\" y=\"-843.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-831.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"75.5,-840.8 75.5,-862.8 115.5,-862.8 115.5,-840.8 75.5,-840.8\"/>\n", "<text text-anchor=\"start\" x=\"83.83\" y=\"-848.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"115.5,-840.8 115.5,-862.8 179.5,-862.8 179.5,-840.8 115.5,-840.8\"/>\n", "<text text-anchor=\"start\" x=\"120.42\" y=\"-848.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"75.5,-818.8 75.5,-840.8 115.5,-840.8 115.5,-818.8 75.5,-818.8\"/>\n", "<text text-anchor=\"start\" x=\"80.08\" y=\"-826.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"115.5,-818.8 115.5,-840.8 179.5,-840.8 179.5,-818.8 115.5,-818.8\"/>\n", "<text text-anchor=\"start\" x=\"120.42\" y=\"-826.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "</g>\n", "<!-- 20&#45;&gt;21 -->\n", "<g id=\"edge21\" class=\"edge\">\n", "<title>20&#45;&gt;21</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-898.9C106,-891.12 106,-882.1 106,-873.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-873.76 106,-863.76 102.5,-873.76 109.5,-873.76\"/>\n", "</g>\n", "<!-- 22 -->\n", "<g id=\"node23\" class=\"node\">\n", "<title>22</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"186,-782.8 26,-782.8 26,-738.8 186,-738.8 186,-782.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"26,-738.8 26,-782.8 82,-782.8 82,-738.8 26,-738.8\"/>\n", "<text text-anchor=\"start\" x=\"30.67\" y=\"-763.8\" font-family=\"Linux libertine\" font-size=\"10.00\">MaxPool2d</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-751.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-760.8 82,-782.8 122,-782.8 122,-760.8 82,-760.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-768.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-760.8 122,-782.8 186,-782.8 186,-760.8 122,-760.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-768.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 7, 7) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-738.8 82,-760.8 122,-760.8 122,-738.8 82,-738.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-746.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-738.8 122,-760.8 186,-760.8 186,-738.8 122,-738.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-746.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 3, 3) </text>\n", "</g>\n", "<!-- 21&#45;&gt;22 -->\n", "<g id=\"edge22\" class=\"edge\">\n", "<title>21&#45;&gt;22</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-818.9C106,-811.12 106,-802.1 106,-793.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-793.76 106,-783.76 102.5,-793.76 109.5,-793.76\"/>\n", "</g>\n", "<!-- 23 -->\n", "<g id=\"node24\" class=\"node\">\n", "<title>23</title>\n", "<polygon fill=\"aliceblue\" stroke=\"none\" points=\"177.5,-702.8 34.5,-702.8 34.5,-658.8 177.5,-658.8 177.5,-702.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"34.5,-658.8 34.5,-702.8 73.5,-702.8 73.5,-658.8 34.5,-658.8\"/>\n", "<text text-anchor=\"start\" x=\"44.28\" y=\"-683.8\" font-family=\"Linux libertine\" font-size=\"10.00\">view</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-671.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:1</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73.5,-680.8 73.5,-702.8 113.5,-702.8 113.5,-680.8 73.5,-680.8\"/>\n", "<text text-anchor=\"start\" x=\"81.83\" y=\"-688.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113.5,-680.8 113.5,-702.8 177.5,-702.8 177.5,-680.8 113.5,-680.8\"/>\n", "<text text-anchor=\"start\" x=\"118.42\" y=\"-688.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 256, 3, 3) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73.5,-658.8 73.5,-680.8 113.5,-680.8 113.5,-658.8 73.5,-658.8\"/>\n", "<text text-anchor=\"start\" x=\"78.08\" y=\"-666.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113.5,-658.8 113.5,-680.8 177.5,-680.8 177.5,-658.8 113.5,-658.8\"/>\n", "<text text-anchor=\"start\" x=\"125.92\" y=\"-666.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2304) </text>\n", "</g>\n", "<!-- 22&#45;&gt;23 -->\n", "<g id=\"edge23\" class=\"edge\">\n", "<title>22&#45;&gt;23</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-738.9C106,-731.12 106,-722.1 106,-713.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-713.76 106,-703.76 102.5,-713.76 109.5,-713.76\"/>\n", "</g>\n", "<!-- 24 -->\n", "<g id=\"node25\" class=\"node\">\n", "<title>24</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-620.4 28.5,-620.4 28.5,-576.4 183.5,-576.4 183.5,-620.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-576.4 28.5,-620.4 94.5,-620.4 94.5,-576.4 28.5,-576.4\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-601.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-589.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"94.5,-598.4 94.5,-620.4 134.5,-620.4 134.5,-598.4 94.5,-598.4\"/>\n", "<text text-anchor=\"start\" x=\"102.83\" y=\"-606.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"134.5,-598.4 134.5,-620.4 183.5,-620.4 183.5,-598.4 134.5,-598.4\"/>\n", "<text text-anchor=\"start\" x=\"139.42\" y=\"-606.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2304) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"94.5,-576.4 94.5,-598.4 134.5,-598.4 134.5,-576.4 94.5,-576.4\"/>\n", "<text text-anchor=\"start\" x=\"99.08\" y=\"-584.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"134.5,-576.4 134.5,-598.4 183.5,-598.4 183.5,-576.4 134.5,-576.4\"/>\n", "<text text-anchor=\"start\" x=\"139.42\" y=\"-584.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2304) </text>\n", "</g>\n", "<!-- 23&#45;&gt;24 -->\n", "<g id=\"edge24\" class=\"edge\">\n", "<title>23&#45;&gt;24</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-659.1C106,-650.63 106,-640.64 106,-631.28\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-631.31 106,-621.31 102.5,-631.31 109.5,-631.31\"/>\n", "</g>\n", "<!-- 25 -->\n", "<g id=\"node26\" class=\"node\">\n", "<title>25</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"170,-540.4 42,-540.4 42,-496.4 170,-496.4 170,-540.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42,-496.4 42,-540.4 81,-540.4 81,-496.4 42,-496.4\"/>\n", "<text text-anchor=\"start\" x=\"48.45\" y=\"-521.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-509.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-518.4 81,-540.4 121,-540.4 121,-518.4 81,-518.4\"/>\n", "<text text-anchor=\"start\" x=\"89.33\" y=\"-526.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-518.4 121,-540.4 170,-540.4 170,-518.4 121,-518.4\"/>\n", "<text text-anchor=\"start\" x=\"125.92\" y=\"-526.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2304) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-496.4 81,-518.4 121,-518.4 121,-496.4 81,-496.4\"/>\n", "<text text-anchor=\"start\" x=\"85.58\" y=\"-504.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-496.4 121,-518.4 170,-518.4 170,-496.4 121,-496.4\"/>\n", "<text text-anchor=\"start\" x=\"130.92\" y=\"-504.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32) </text>\n", "</g>\n", "<!-- 24&#45;&gt;25 -->\n", "<g id=\"edge25\" class=\"edge\">\n", "<title>24&#45;&gt;25</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-576.5C106,-568.72 106,-559.7 106,-551.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-551.36 106,-541.36 102.5,-551.36 109.5,-551.36\"/>\n", "</g>\n", "<!-- 26 -->\n", "<g id=\"node27\" class=\"node\">\n", "<title>26</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"178.5,-460.4 33.5,-460.4 33.5,-416.4 178.5,-416.4 178.5,-460.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"33.5,-416.4 33.5,-460.4 99.5,-460.4 99.5,-416.4 33.5,-416.4\"/>\n", "<text text-anchor=\"start\" x=\"38.17\" y=\"-441.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"51.5\" y=\"-429.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"99.5,-438.4 99.5,-460.4 139.5,-460.4 139.5,-438.4 99.5,-438.4\"/>\n", "<text text-anchor=\"start\" x=\"107.83\" y=\"-446.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"139.5,-438.4 139.5,-460.4 178.5,-460.4 178.5,-438.4 139.5,-438.4\"/>\n", "<text text-anchor=\"start\" x=\"144.42\" y=\"-446.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"99.5,-416.4 99.5,-438.4 139.5,-438.4 139.5,-416.4 99.5,-416.4\"/>\n", "<text text-anchor=\"start\" x=\"104.08\" y=\"-424.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"139.5,-416.4 139.5,-438.4 178.5,-438.4 178.5,-416.4 139.5,-416.4\"/>\n", "<text text-anchor=\"start\" x=\"144.42\" y=\"-424.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32) </text>\n", "</g>\n", "<!-- 25&#45;&gt;26 -->\n", "<g id=\"edge26\" class=\"edge\">\n", "<title>25&#45;&gt;26</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-496.5C106,-488.72 106,-479.7 106,-471.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-471.36 106,-461.36 102.5,-471.36 109.5,-471.36\"/>\n", "</g>\n", "<!-- 27 -->\n", "<g id=\"node28\" class=\"node\">\n", "<title>27</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"167.5,-380.4 44.5,-380.4 44.5,-336.4 167.5,-336.4 167.5,-380.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"44.5,-336.4 44.5,-380.4 83.5,-380.4 83.5,-336.4 44.5,-336.4\"/>\n", "<text text-anchor=\"start\" x=\"50.95\" y=\"-361.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-349.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83.5,-358.4 83.5,-380.4 123.5,-380.4 123.5,-358.4 83.5,-358.4\"/>\n", "<text text-anchor=\"start\" x=\"91.83\" y=\"-366.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123.5,-358.4 123.5,-380.4 167.5,-380.4 167.5,-358.4 123.5,-358.4\"/>\n", "<text text-anchor=\"start\" x=\"130.92\" y=\"-366.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83.5,-336.4 83.5,-358.4 123.5,-358.4 123.5,-336.4 83.5,-336.4\"/>\n", "<text text-anchor=\"start\" x=\"88.08\" y=\"-344.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123.5,-336.4 123.5,-358.4 167.5,-358.4 167.5,-336.4 123.5,-336.4\"/>\n", "<text text-anchor=\"start\" x=\"128.42\" y=\"-344.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 26&#45;&gt;27 -->\n", "<g id=\"edge27\" class=\"edge\">\n", "<title>26&#45;&gt;27</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-416.5C106,-408.72 106,-399.7 106,-391.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-391.36 106,-381.36 102.5,-391.36 109.5,-391.36\"/>\n", "</g>\n", "<!-- 28 -->\n", "<g id=\"node29\" class=\"node\">\n", "<title>28</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"169.5,-300.4 42.5,-300.4 42.5,-256.4 169.5,-256.4 169.5,-300.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42.5,-256.4 42.5,-300.4 85.5,-300.4 85.5,-256.4 42.5,-256.4\"/>\n", "<text text-anchor=\"start\" x=\"47.33\" y=\"-281.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-269.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"85.5,-278.4 85.5,-300.4 125.5,-300.4 125.5,-278.4 85.5,-278.4\"/>\n", "<text text-anchor=\"start\" x=\"93.83\" y=\"-286.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"125.5,-278.4 125.5,-300.4 169.5,-300.4 169.5,-278.4 125.5,-278.4\"/>\n", "<text text-anchor=\"start\" x=\"130.42\" y=\"-286.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"85.5,-256.4 85.5,-278.4 125.5,-278.4 125.5,-256.4 85.5,-256.4\"/>\n", "<text text-anchor=\"start\" x=\"90.08\" y=\"-264.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"125.5,-256.4 125.5,-278.4 169.5,-278.4 169.5,-256.4 125.5,-256.4\"/>\n", "<text text-anchor=\"start\" x=\"130.42\" y=\"-264.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 27&#45;&gt;28 -->\n", "<g id=\"edge28\" class=\"edge\">\n", "<title>27&#45;&gt;28</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-336.5C106,-328.72 106,-319.7 106,-311.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-311.36 106,-301.36 102.5,-311.36 109.5,-311.36\"/>\n", "</g>\n", "<!-- 29 -->\n", "<g id=\"node30\" class=\"node\">\n", "<title>29</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-220.4 31,-220.4 31,-176.4 181,-176.4 181,-220.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-176.4 31,-220.4 97,-220.4 97,-176.4 31,-176.4\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-201.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-189.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"97,-198.4 97,-220.4 137,-220.4 137,-198.4 97,-198.4\"/>\n", "<text text-anchor=\"start\" x=\"105.33\" y=\"-206.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"137,-198.4 137,-220.4 181,-220.4 181,-198.4 137,-198.4\"/>\n", "<text text-anchor=\"start\" x=\"141.92\" y=\"-206.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"97,-176.4 97,-198.4 137,-198.4 137,-176.4 97,-176.4\"/>\n", "<text text-anchor=\"start\" x=\"101.58\" y=\"-184.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"137,-176.4 137,-198.4 181,-198.4 181,-176.4 137,-176.4\"/>\n", "<text text-anchor=\"start\" x=\"141.92\" y=\"-184.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 28&#45;&gt;29 -->\n", "<g id=\"edge29\" class=\"edge\">\n", "<title>28&#45;&gt;29</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-256.5C106,-248.72 106,-239.7 106,-231.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-231.36 106,-221.36 102.5,-231.36 109.5,-231.36\"/>\n", "</g>\n", "<!-- 30 -->\n", "<g id=\"node31\" class=\"node\">\n", "<title>30</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"167.5,-130 44.5,-130 44.5,-86 167.5,-86 167.5,-130\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"44.5,-86 44.5,-130 83.5,-130 83.5,-86 44.5,-86\"/>\n", "<text text-anchor=\"start\" x=\"50.95\" y=\"-111\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-99\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83.5,-108 83.5,-130 123.5,-130 123.5,-108 83.5,-108\"/>\n", "<text text-anchor=\"start\" x=\"91.83\" y=\"-116\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123.5,-108 123.5,-130 167.5,-130 167.5,-108 123.5,-108\"/>\n", "<text text-anchor=\"start\" x=\"128.42\" y=\"-116\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83.5,-86 83.5,-108 123.5,-108 123.5,-86 83.5,-86\"/>\n", "<text text-anchor=\"start\" x=\"88.08\" y=\"-94\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123.5,-86 123.5,-108 167.5,-108 167.5,-86 123.5,-86\"/>\n", "<text text-anchor=\"start\" x=\"130.92\" y=\"-94\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 10) </text>\n", "</g>\n", "<!-- 29&#45;&gt;30 -->\n", "<g id=\"edge30\" class=\"edge\">\n", "<title>29&#45;&gt;30</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-176.45C106,-165.77 106,-152.56 106,-140.67\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-140.8 106,-130.8 102.5,-140.8 109.5,-140.8\"/>\n", "</g>\n", "<!-- 31 -->\n", "<g id=\"node32\" class=\"node\">\n", "<title>31</title>\n", "<polygon fill=\"lightyellow\" stroke=\"none\" points=\"155.99,-50 56.01,-50 56.01,-16 155.99,-16 155.99,-50\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"56.01,-16 56.01,-50 119.33,-50 119.33,-16 56.01,-16\"/>\n", "<text text-anchor=\"start\" x=\"61.01\" y=\"-36\" font-family=\"Linux libertine\" font-size=\"10.00\">output&#45;tensor</text>\n", "<text text-anchor=\"start\" x=\"72.67\" y=\"-24\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:0</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"119.33,-16 119.33,-50 155.99,-50 155.99,-16 119.33,-16\"/>\n", "<text text-anchor=\"start\" x=\"124.33\" y=\"-30\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 10)</text>\n", "</g>\n", "<!-- 30&#45;&gt;31 -->\n", "<g id=\"edge31\" class=\"edge\">\n", "<title>30&#45;&gt;31</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-86.28C106,-78.28 106,-69.03 106,-60.55\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-60.67 106,-50.67 102.5,-60.67 109.5,-60.67\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x2442a09cbe0>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["import torchvision\n", "from torchview import draw_graph\n", "net2 = Fashion_MNIST_CNN()\n", "\n", "#model_graph = draw_graph(net2, input_size=(1,3,64,64), expand_nested=True)\n", "model_graph = draw_graph(net2, input_size=(1,1,28,28), expand_nested=True)\n", "model_graph.visual_graph\n"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Pytorch MNIST.ipynb", "provenance": []}, "kernelspec": {"display_name": "torchenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}