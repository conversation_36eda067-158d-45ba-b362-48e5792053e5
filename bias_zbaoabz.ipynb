{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## BAOAB and perturbed_BAOAB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_batches=torch.tensor(3,device=device)      # Nm in paper\n", "batch_size=1\n", "par_runs=20000\n", "X=torch.tensor([[-3, 1, 2], [0.5, 1.0, 2.0]],device=device) #X[0,:] denotes mean, X[1, :] denotes standard deviation\n", "target_mean=(X[0,:]*(X[1,:].pow(-2))).sum()/(X[1,:].pow(-2).sum())\n", "target_sd=(X[1,:].pow(-2)).sum().pow(-0.5)\n", "l2regconst=torch.tensor(1,device=device).detach()\n", "gam=torch.sqrt(l2regconst)\n", "\n", "def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alpha = torch.tensor(0.1, device=device)\n", "Omega = torch.tensor(1.0, device=device)\n", "m = torch.tensor(0.01, device=device)\n", "M = torch.tensor(60.0, device=device)\n", "r = torch.tensor(0.5, device=device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from dataclasses import dataclass\n", "\n", "# # parameters for BAOAB\n", "# @dataclass\n", "# class BAOABhclass:\n", "#     h: Tensor    # step size\n", "#     eta: Tensor  # exp(-gamma*h/2)\n", "#     xc1: Tensor\n", "#     xc2: Tensor\n", "#     xc3: Tensor\n", "#     vc1: Tensor\n", "#     vc2: Tensor\n", "#     vc3: Tensor\n", "\n", "# # parameters for perturbed BAOAB\n", "# @dataclass\n", "# class perturbed_BAOABhclass:\n", "#     h: Tensor    # step size\n", "#     eta: Tensor  # exp(-gamma*h/2)\n", "#     xc1: Tensor\n", "#     xc2: Tensor\n", "#     xc3: Tensor\n", "#     vc1: Tensor\n", "#     vc2: Tensor\n", "#     vc3: Tensor\n", "#     xcn1: Tensor # perturbation on x (parameter for gaussian noise 1)\n", "#     vcn1: Tensor # perturbation on v (parameter for gaussian noise 1)\n", "#     vcn2: Tensor # perturbation on v (parameter for gaussian noise 2)\n", "\n", "# # constant parameters for BAOAB\n", "# def BAOAB_hconst(h,gam):\n", "#     with torch.no_grad():\n", "#         hh=copy.deepcopy(h).detach().double()\n", "#         gamm=copy.deepcopy(gam).detach().double()\n", "#         gh=gamm*hh\n", "#         eta=(torch.exp(-gh))\n", "#         xc1=hh/2*(1+eta)\n", "#         xc2=(hh*hh/4)*(1+eta)\n", "#         xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "#         vc1=eta*(hh/2)\n", "#         vc2=(hh/2)\n", "#         vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "\n", "#         hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "#         return(hc)\n", "    \n", "# # constant parameters for perturbed BAOAB\n", "# def perturbed_BAOAB_hconst(h,gam):\n", "#     with torch.no_grad():\n", "#         hh=copy.deepcopy(h).detach().double()\n", "#         gamm=copy.deepcopy(gam).detach().double()\n", "#         gh=gamm*hh\n", "#         eta=(torch.exp(-gh))\n", "#         xc1=hh/2*(1+eta)\n", "#         xc2=(hh*hh/4)*(1+eta)\n", "#         xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "#         vc1=eta*(hh/2)\n", "#         vc2=(hh/2)\n", "#         vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "\n", "#         xcn1=(hh*hh/4)*(1+eta)\n", "#         vcn1=eta*(hh/2)\n", "#         vcn2=(hh/2)\n", "\n", "#         hc=perturbed_BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float(),xcn1=xcn1.float(),vcn1=vcn1.float(),vcn2=vcn2.float())\n", "#         return(hc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def BAOAB_coeffs(h: float, gam: float, device=device):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "        \n", "    return tuple(map(lambda v: torch.as_tensor(v, device=device),\n", "                (eta, xc1, xc2, xc3, vc1, vc2, vc3)))\n", "\n", "def BAOAB_step(p,h,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        eta, xc1, xc2, xc3, vc1, vc2, vc3 = BAOAB_coeffs(h, gam)\n", "\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+xc1*p.v-xc2*last_grad+xc3*xi1\n", "\n", "        grads=grad(p,batch_it)*no_batches \n", "\n", "        p.v=eta*p.v-vc1*last_grad-vc2*grads+vc3*xi1\n", "\n", "    return(grads)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "    V1s,_=torch.sort(V1.flatten())\n", "    V2s,_=torch.sort(V2.flatten())\n", "    return (V1s-V2s).abs().mean()     "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def SMS_BAOAB(K,h,gam):\n", "#   \"\"\"\n", "#   K: Total samples after SMS_BAOAB\n", "#   no_batches: Nm\n", "#   batch_size: Nb, here is 1\n", "#   \"\"\"\n", "\n", "#   Nm: int = int(no_batches.item())\n", "#   num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "#   p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "#     # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "\n", "#     for i in range(num_epochs):\n", "#       rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "#       rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "#       if i==0:\n", "#         grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "\n", "#       # ---------- forward sweep ----------\n", "#       n_fw = min(Nm, K - (2*i)*Nm)\n", "#       for k in range(n_fw):\n", "#         grads = BAOAB_step(p, hper2c, rng_perm[:,k+1], grads)\n", "\n", "#         V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "#       # ---------- backward sweep ----------\n", "#       n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "#       for k in range(n_bw):\n", "#         grads = BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads)\n", "\n", "#         V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "#       # V_epoch_arr[:,i]=V_arr.mean(dim=0)\n", "#       # V_epoch_arr[:,i]=p.data.clone()\n", "\n", "#   return(V_arr)\n", "\n", "# def SG_BAOAB_without_replacement(K,h,gam):\n", "#   \"\"\"\n", "#   K: Total samples after SG_BAOAB\n", "#   no_batches: Nm\n", "#   batch_size: Nb, here is 1\n", "#   \"\"\"\n", "\n", "#   Nm: int = int(no_batches.item())\n", "#   num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "#   p=torch.zeros(par_runs,device=device)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "#     # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#   #Initialise velocities\n", "#     p.v = torch.randn_like(p,device=device).detach()\n", "#   ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "#   grads=grad(p.data,ind)\n", "\n", "#   for i in range(num_epochs):\n", "\n", "#     rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "#     n_sw=min(Nm,K-i*Nm)\n", "#     for k in range(n_sw):\n", "#       ind=rperm[:,k]\n", "#       grads=BAOAB_step(p,hper2c,ind,grads)\n", "#       V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "#     # with torch.no_grad():\n", "#     #   V_epoch_arr[:,i]=p.data\n", "    \n", "#   return(V_arr)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def psi_of_zeta(z, m=0.1, M=10.0, r=0.5):\n", "#     \"\"\"\n", "#     The Sundman transform kernel, eq. (25) from paper, version (1):\n", "#        ψ(1)(ζ) = m * ( ζ^r + M ) / ( ζ^r + m ).\n", "#     \"\"\"\n", "#     # clamp z so it's never negative or zero\n", "#     if z<1e-14:\n", "#         z=1e-14\n", "#     zr = z**r\n", "#     return m*(zr + M)/(zr + m)\n", "\n", "def psi_of_zeta(z, m, M, r):\n", "    \"\"\"\n", "    The Sundman transform kernel, eq. (25) from paper, version (1):\n", "       ψ(1)(ζ) = m * ( ζ^r + M ) / ( ζ^r + m ).\n", "    \"\"\"\n", "    # clamp z so it's never negative or zero\n", "    # if z<1e-14:\n", "    #     z=1e-14\n", "    zr = z**r\n", "    return m*(zr + M/m)/(zr + 1)\n", "\n", "# def z_half_step(z_old, alpha, half_dtau, g_val):\n", "#     \"\"\"\n", "#     The half-step update for z using eq. (17) from paper:\n", "#       z_new = e^{-alpha * half_dtau} z_old + [1 - e^{-alpha * half_dtau}]/alpha * g_val\n", "#     \"\"\"\n", "#     rho = np.exp(-alpha*half_dtau)\n", "#     return rho*z_old + (1.-rho)/alpha*g_val\n", "\n", "def monitor_g(gradU, scale_g):\n", "        \"\"\"\n", "        The 'g(x,p)' from the paper: sum-of-square or norm-of gradient \n", "        => we'll do: ∥gradU∥ / scale_g or ∥gradU∥^1 or ^2. \n", "        Let's do ∥gradU∥^1 for demonstration: \n", "        \"\"\"\n", "        gn = torch.norm(gradU)  # or p=2 \n", "        return float(gn/scale_g)\n", "\n", "def Z(zeta, force_norm, alpha, tau, Omega):\n", "    rho = torch.exp(-alpha * tau)\n", "    coeff = (1.0 - rho) / (Omega * alpha)\n", "    return rho * zeta + coeff * force_norm\n", "\n", "def ZBAOABZ_step(p,zeta,batch_it,last_grad, force_norm, tau):\n", "    zeta = Z(zeta, force_norm, alpha, tau/2, Omega)\n", "    psi = psi_of_zeta(zeta, m, M, r)\n", "    t = psi * tau\n", "    grads = BAOAB_step(p,t,batch_it,last_grad)\n", "    force_norm = torch.norm(grads, 2)\n", "    zeta = Z(zeta, force_norm, alpha, tau/2, Omega)\n", "    psi = psi_of_zeta(zeta, m, M, r)\n", "\n", "    return t, zeta, psi, grads, force_norm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def SMS_ZBAOABZ(K,tau):\n", "  \"\"\"\n", "  K: Total samples after SMS_ZBAOABZ\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  # p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  p=torch.zeros(1,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    # V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # mu_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # t_arr=torch.zeros(par_runs, device=device).detach()\n", "    V_arr=torch.zeros(K,device=device).detach()\n", "    mu_arr=torch.zeros(K,device=device).detach()\n", "    t_arr=torch.tensor(0.0, device=device).detach()\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      # rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "      rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "      rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "\n", "      if i==0:\n", "        # grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "        grads = grad(p.data, rng_perm[0]) * no_batches # k = 0 gradient\n", "        force_norm = torch.norm(grads, 2)\n", "        zeta = torch.tensor(M*tau, device=device)\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        # t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[:,k+1], grads, force_norm, tau)\n", "\n", "        # V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "        # mu_arr[:,(2*i)*Nm + k]=psi.clone()\n", "        # t_arr += t.clone()\n", "        t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[k+1], grads, force_norm, tau)\n", "\n", "        V_arr[(2*i)*Nm + k]=p.data.clone()\n", "        mu_arr[(2*i)*Nm + k]=psi.clone()\n", "        t_arr += t.clone()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i+1)*Nm)  \n", "      for k in range(n_bw):\n", "        # t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[:,K-k-1], grads, force_norm, tau)\n", "\n", "        # V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "        # mu_arr[:,(2*i+1)*Nm + k]=psi.clone()\n", "        # t_arr += t.clone()\n", "        t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[Nm-1-k], grads, force_norm, tau)\n", "\n", "        V_arr[(2*i+1)*Nm + k]=p.data.clone()\n", "        mu_arr[(2*i+1)*Nm + k]=psi.clone()\n", "        t_arr += t.clone()\n", "\n", "    return V_arr, mu_arr, t_arr/num_epochs\n", "\n", " \n", "def SG_ZBAOABZ_without_replacement(K,tau):\n", "  \"\"\"\n", "  K: Total samples after SG_ZBAOABZ\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  # p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  p=torch.zeros(1,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    # V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # mu_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # t_arr=torch.zeros(par_runs, device=device).detach()\n", "    V_arr=torch.zeros(K,device=device).detach()\n", "    mu_arr=torch.zeros(K,device=device).detach()\n", "    t_arr=torch.tensor(0.0, device=device).detach()\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    # ind = torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "    ind = torch.randint(high=no_batches).int()  # random ind from [0, no_batches-1] for each par_runs\n", "    grads = grad(p.data, ind) * no_batches # k = 0 gradient\n", "    force_norm = torch.norm(grads, 2)**2\n", "    zeta = torch.tensor(M*tau, device=device)\n", "\n", "    for i in range(num_epochs):\n", "      # rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "      rperm=rng.permutation(Nm)\n", "      n_sw=min(Nm,K-i*Nm)\n", "      for k in range(n_sw):\n", "        # ind=rperm[:,k]\n", "        # t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, ind, grads, force_norm, tau)\n", "\n", "        # V_arr[:,i*Nm+k]=p.data.clone()\n", "        # mu_arr[:,i*Nm+k]=psi.clone()\n", "        # t_arr += t.clone()\n", "        ind=rperm[k]\n", "        t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, ind, grads, force_norm, tau)\n", "\n", "        V_arr[i*Nm+k]=p.data.clone()\n", "        mu_arr[i*Nm+k]=psi.clone()\n", "        t_arr += t.clone()\n", "\n", "    return V_arr, mu_arr, t_arr/num_epochs\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if __name__ == '__main__':\n", "    K = 4000\n", "    tau = torch.tensor(0.125, device=device) # Base stepsize in virtual time\n", "\n", "    print(\"Running SMS-ZBAOABZ...\")\n", "    V_arr, mu_arr = SMS_ZBAOABZ(K, tau)\n", "\n", "    # --- <PERSON><PERSON>tion with Reweighting ---\n", "    print(\"\\nCalculating <PERSON>serstein distance with reweighting...\")\n", "    burn_in = K // 5 # Discard first 20% of samples\n", "    \n", "    # Samples and weights after burn-in\n", "    samples_to_use = V_arr[:, burn_in:]\n", "    weights_to_use = mu_arr[:, burn_in:]\n", "\n", "    # Generate target distribution samples\n", "    target_samples = torch.randn_like(samples_to_use, device=device) * target_sd + target_mean\n", "\n", "    # Perform resampling for each parallel run\n", "    resampled_list = []\n", "    for i in range(par_runs):\n", "        # Normalize weights for the current run\n", "        normalized_weights = weights_to_use[i] / torch.sum(weights_to_use[i])\n", "        \n", "        # Resample indices with replacement based on normalized weights\n", "        num_samples = samples_to_use.shape[1]\n", "        resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "        \n", "        # Get the new unweighted samples\n", "        resampled_list.append(samples_to_use[i, resampled_indices])\n", "\n", "    resampled_V = torch.stack(resampled_list)\n", "    \n", "    # Now, calculate <PERSON><PERSON><PERSON> distance on the resampled (unweighted) data\n", "    diff = Wass(resampled_V, target_samples)\n", "    print(f\"<PERSON><PERSON><PERSON> distance on reweighted samples: {diff:.8f}\")"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}