{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import time\n", "import arviz as az\n", "import math\n", "\n", "from PIL import Image\n", "\n", "import torchvision\n", "from torchvision import datasets, transforms\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_auc_score\n", "\n", "#%% ZBAOABZ with Adaptive Batch Size\n", "class ZBAOABZ_with_batch(nn.Module):\n", "    \"\"\"\n", "    ZBAOABZ Sampler with two adaptive controllers:\n", "    1. <PERSON><PERSON><PERSON><PERSON> for step-size (dt) based on gradient norm.\n", "    2. Adaptive Bat<PERSON> Si<PERSON> (B) based on configurational temperature.\n", "    \"\"\"\n", "\n", "    def __init__(self, model, train_dataset, eval_loader, test_loader, ood_loader, criterion, \n", "                 # Sam<PERSON> params\n", "                 dtau, weight_decay, gamma, temperature, epochs, device, meas_freq,\n", "                 # dt control params\n", "                 alpha_dt, scale_g_dt, m_dt, M_dt,\n", "                 # --- NEW: Batch size control params ---\n", "                 burn_in_epochs, initial_batch_size, post_burn_in_initial_batch_size, \n", "                 min_batch_size, max_batch_size,\n", "                 alpha_batch, delta_temp,       # was: threshold_batch   (temperature deadzone half-width)\n", "                 k_sigma,          # was: scale_g_batch     (monitor gain)\n", "                 c_sigma,          # was: steepness_batch   (mapping gain)\n", "                 # Optional: neutral batch target and temp-error smoothing\n", "                 B_star=None,      # neutral/in-tolerance batch; default set inside to post_burn_in_initial_batch_size\n", "                 alpha_e=12.0,     # smoothing rate (1/τ) for temperature error EMA\n", "                 # threshold_batch, steepness_batch, scale_g_batch,\n", "                 # Schedules\n", "                 lr_schedule=None, T_schedule=None):\n", "        super(ZBAOABZ_with_batch, self).__init__()\n", "        \n", "        # Core components\n", "        self.model = model\n", "        self.train_dataset = train_dataset # Store dataset, not loader\n", "        self.eval_loader = eval_loader\n", "        self.test_loader = test_loader\n", "        self.ood_loader = ood_loader\n", "        self.criterion = criterion\n", "        self.device = device\n", "        self.epochs = epochs\n", "        self.meas_freq = meas_freq\n", "        self.weight_decay = weight_decay\n", "        self.gamma = gamma\n", "        self.T = temperature\n", "        \n", "        # --- State variables for dt control ---\n", "        self.dtau = dtau\n", "        self.alpha_dt = alpha_dt\n", "        self.scale_g_dt = scale_g_dt # This is Omega_inv in the paper\n", "        self.m_dt = m_dt\n", "        self.M_dt = M_dt\n", "        self.r_dt = 0.25\n", "        self.lr = None # dt\n", "        self.zeta_dt = None\n", "        self.gradnorm = None\n", "        self.exptau_half_dt = np.exp(-0.5 * self.alpha_dt * self.dtau)\n", "        self.alpha_inv_dt = 1 / self.alpha_dt if self.alpha_dt > 0 else 0\n", "\n", "        # --- NEW: State variables for batch size control ---\n", "        self.burn_in_epochs = burn_in_epochs\n", "        self.min_batch_size = min_batch_size\n", "        self.max_batch_size = max_batch_size\n", "        self.post_burn_in_initial_batch_size = post_burn_in_initial_batch_size # NEW\n", "        self.post_burn_in_reset_done = False\n", "\n", "        # self.alpha_batch = alpha_batch\n", "        self.delta_temp = delta_temp      # tolerance half-width for deadzone (e.g., 0.05–0.10)\n", "        self.k_sigma   = k_sigma          # monitor gain (replaces “×100” hacks)\n", "        self.alpha_batch = alpha_batch    # EMA rate for zeta_batch (keep slower than alpha_dt)\n", "\n", "        # Temp-error smoothing (monitor input)\n", "        self.alpha_e = alpha_e            # smoothing rate for temperature error (EMA in fictive time)\n", "        self.e_temp_ema = 0.0             # state for smoothed, dimensionless temp error\n", "\n", "        # New mapping param (logistic/tanh slope)\n", "        self.c_sigma = c_sigma            # mapping gain (controls small-signal slope in sigma(zeta))\n", "        # self.threshold_batch = threshold_batch\n", "        # self.steepness_batch = steepness_batch\n", "        # self.scale_g_batch = scale_g_batch # This is Omega_inv for monitor function of sigma\n", "        self.r_batch = 1\n", "        \n", "        self.batch_size = initial_batch_size\n", "        self.zeta_batch = 0.0 # Start auxiliary variable at neutral\n", "        # Calculate initial sigma from initial batch size\n", "        self.datasize = len(self.train_dataset)\n", "        self.sigma = (self.datasize - self.batch_size) / self.batch_size if self.batch_size > 0 else float('inf')\n", "        # Pre-calculate sigma bounds from batch size bounds\n", "        self.sigma_min = (self.datasize - self.max_batch_size) / self.max_batch_size if self.max_batch_size > 0 else float('inf')\n", "        self.sigma_max = (self.datasize - self.min_batch_size) / self.min_batch_size if self.min_batch_size > 0 else float('inf')\n", "        \n", "        # --- Pre-calculated constants ---\n", "        self.a_gamma = np.exp(-self.gamma)\n", "        self.a = None\n", "        self.sqrt_aT = None\n", "        \n", "        # --- Schedules ---\n", "        self.lr_schedule = lr_schedule\n", "        self.T_schedule = T_schedule\n", "        self.T_idx = 0\n", "        \n", "        # --- Metrics Storage ---\n", "        self.dt_raw = []\n", "        self.zeta_dt_raw = []\n", "        self.batch_size_raw = []\n", "        self.zeta_batch_raw = []\n", "        self.ess_raw = []\n", "        self.tkin_raw = []\n", "        self.tconf_raw = []\n", "        self.loss_history_for_ess = []\n", "        self.all_batch_sizes_in_training = [] # For calculating mean batch size\n", "        \n", "        self.results_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \n", "                               \"Tkin\", \"Tconf\", \"NLL\", \"Brier\", \"ECE\", \n", "                               \"OOD AUC\", \"ESS\", \"dt\", \"zeta_dt\", \"Batch Size\", \"zeta_batch\"]\n", "        \n", "        # Neutral batch when in tolerance:\n", "        if B_star is None:\n", "            B_star = self.post_burn_in_initial_batch_size  # sensible default\n", "        # Clip to feasible range\n", "        B_star = int(np.round(np.clip(B_star, self.min_batch_size, self.max_batch_size)))\n", "        self.B_star = B_star\n", "\n", "        # Convert to neutral sigma\n", "        self.sigma_star = (self.datasize / B_star) - 1.0\n", "\n", "        # Precompute logistic bias so sigma(0) = sigma_star\n", "        rng = (self.sigma_max - self.sigma_min)\n", "        if rng <= 0:\n", "            raise ValueError(\"sigma_max must be greater than sigma_min (check min/max batch settings).\")\n", "\n", "        p_star = (self.sigma_star - self.sigma_min) / rng\n", "        # avoid infinities at 0/1\n", "        p_star = float(np.clip(p_star, 1e-6, 1.0 - 1e-6))\n", "        self.bias_sigma = float(np.log(p_star / (1.0 - p_star)))  # logit(p*)\n", "\n", "    # --- NEW: Methods for Adaptive Batch Size Control ---\n", "    # def monitor_g_batch(self, T_conf):\n", "    #     \"\"\" Two-way linear monitor for temperature gap. \"\"\"\n", "    #     gap = np.abs(T_conf - self.T) / self.T if self.T > 0 else np.abs(T_conf)\n", "    #     return gap*100\n", "    def monitor_g_batch(self, T_meas):\n", "        \"\"\"\n", "        Signed temperature-error monitor with deadzone and short EMA smoothing.\n", "\n", "        Inputs:\n", "            T_meas : instantaneous measured temperature (use T_kin measured AFTER OU).\n", "        Returns:\n", "            g_sigma : control signal driving zeta_sigma (positive when 'too hot').\n", "        \"\"\"\n", "        # 1) Dimensionless signed error: hot (>T) => positive\n", "        e = (T_meas / self.T) - 1.0\n", "\n", "        # 2) Short EMA smoothing in fictive time tau\n", "        #    alpha_e is a rate (1/time); dtau is your integrator step in tau.\n", "        rho_e = math.exp(-self.alpha_e * self.dtau)\n", "        self.e_temp_ema = rho_e * self.e_temp_ema + (1.0 - rho_e) * e\n", "\n", "        # 3) Deadzone (tolerance) around zero\n", "        err = self.e_temp_ema\n", "        delta = self.delta_temp  # e.g., 0.05–0.10\n", "\n", "        if abs(err) <= delta:\n", "            return 0.0  # exactly zero drive inside tolerance\n", "\n", "        # 4) Signed drive outside the deadzone (linear beyond the band)\n", "        #    magnitude grows with distance beyond delta; sign follows err.\n", "        g_sigma = self.k_sigma * math.copysign(abs(err) - delta, err)\n", "\n", "        return g_sigma\n", "    \n", "    # def psi_batch(self):\n", "    #     \"\"\" Arctan mapping from zeta_batch to sigma. \"\"\"\n", "    #     midpoint = (self.sigma_min + self.sigma_max) / 2.0\n", "    #     sigma_range = self.sigma_max - self.sigma_min\n", "    #     atan_val = np.arctan(self.steepness_batch * (self.zeta_batch**self.r_batch) - self.threshold_batch)\n", "    #     # Use a minus sign to make it a decreasing function\n", "    #     return midpoint - (sigma_range / np.pi) * atan_val\n", "    def psi_batch(self):\n", "        \"\"\"\n", "        Map zeta_batch -> sigma via a bounded, monotone-decreasing logistic.\n", "        Ensures sigma in [sigma_min, sigma_max] and sigma(0) = sigma_star.\n", "        \"\"\"\n", "        # Argument to the logistic: bias shifts the center so that zeta=0 -> sigma_star\n", "        # c_sigma > 0 sets the small-signal slope (responsiveness)\n", "        x = self.bias_sigma - self.c_sigma * self.zeta_batch\n", "\n", "        # Numerically safe logistic\n", "        # (clip x to avoid overflow in exp for very large |x|)\n", "        x = float(np.clip(x, -60.0, 60.0))\n", "        S = 1.0 / (1.0 + math.exp(-x))   # in (0,1)\n", "\n", "        sigma = self.sigma_min + (self.sigma_max - self.sigma_min) * S\n", "        # (sigma already in [sigma_min, sigma_max] by construction)\n", "        return sigma\n", "    \n", "    def zeta_step_batch(self, g_val):\n", "        \"\"\" Update rule for the batch size auxiliary variable. \"\"\"\n", "        # The effective time step for this update is dtau, as it happens once per ZBAOABZ step\n", "        if self.alpha_batch <= 0: return\n", "        rho = np.exp(-self.alpha_batch * self.dtau)\n", "        self.zeta_batch = rho * self.zeta_batch + (1 - rho) / self.alpha_batch * g_val\n", "\n", "    def get_current_temps(self):\n", "        \"\"\" Calculates instantaneous Tkin and Tconf. \"\"\"\n", "        <PERSON><PERSON>, <PERSON><PERSON><PERSON>, param_count = 0, 0, 0\n", "        with torch.no_grad():\n", "            for param in self.model.parameters():\n", "                if hasattr(param, 'buf') and param.grad is not None:\n", "                    Tkin += (param.buf**2).sum().item()\n", "                    Tconf += (param.data * param.grad.data).sum().item()\n", "                    param_count += param.numel()\n", "\n", "        Tkin_val = Tkin / param_count if param_count > 0 else 0\n", "        Tconf_val = Tconf / param_count if param_count > 0 else 0\n", "        return Tkin_val, Tconf_val\n", "\n", "    def get_current_Tkin(self):\n", "        \"\"\" Calculates instantaneous Tkin. \"\"\"\n", "        <PERSON><PERSON>, param_count = 0, 0\n", "        with torch.no_grad():\n", "            for param in self.model.parameters():\n", "                if hasattr(param, 'buf') and param.grad is not None:\n", "                    Tkin += (param.buf**2).sum().item()\n", "                    param_count += param.numel()\n", "\n", "        Tkin_val = Tkin / param_count if param_count > 0 else 0\n", "        return Tkin_val\n", "\n", "    def adapt_batch_size(self):\n", "        \"\"\"\n", "        Orchestrates the batch size adaptation. This is called once per ZBAOABZ step.\n", "        \"\"\"\n", "        # 1. Get current T_conf (lagging indicator from previous step's gradient)\n", "        # T<PERSON>, T_conf = self.get_current_temps()\n", "        Tkin = self.get_current_Tkin()\n", "\n", "        # # 2. Get monitor value\n", "        # g_val_kin = self.monitor_g_batch(Tkin)\n", "        # g_val_conf = self.monitor_g_batch(T_conf)\n", "        # # g_val = max(g_val_kin, g_val_conf)\n", "        # g_val = g_val_kin\n", "        g_val = self.monitor_g_batch(Tkin)\n", "\n", "        g_val *= self.scale_g_batch\n", "        \n", "        # 3. Update auxiliary variable\n", "        self.zeta_step_batch(g_val)\n", "\n", "        # 4. Map to new sigma\n", "        new_sigma = self.psi_batch()\n", "        self.sigma = np.clip(new_sigma, self.sigma_min, self.sigma_max)\n", "\n", "        # 5. Convert sigma to new batch size B\n", "        if (self.sigma + 1) == 0: # Avoid division by zero\n", "            new_B_float = float('inf')\n", "        else:\n", "            new_B_float = self.datasize / (self.sigma + 1)\n", "\n", "        self.batch_size = int(np.round(np.clip(new_B_float, self.min_batch_size, self.max_batch_size)))\n", "\n", "    # --- Methods for Adaptive Step Size (dt) Control ---\n", "\n", "    def set_gradnorm(self):\n", "        self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters() if p.grad is not None)\n", "        self.gradnorm *= self.scale_g_dt\n", "\n", "    def Z_step_dt(self):\n", "        if self.alpha_dt <= 0: return\n", "        self.zeta_dt = self.exptau_half_dt * self.zeta_dt + self.alpha_inv_dt * (1 - self.exptau_half_dt) * self.gradnorm  \n", "\n", "    def Sundman_transform_dt(self):\n", "        # --- BUG FIX: Clamp zeta_dt to prevent NaN from negative numbers ---\n", "        zeta_clamped = torch.clamp(self.zeta_dt, min=1e-8)\n", "        zeta_r = zeta_clamped**self.r_dt # r=0.25\n", "        self.lr = self.dtau * self.m_dt * (zeta_r + self.M_dt / self.m_dt) / (zeta_r + 1) # K2 kernel\n", "\n", "        # --- Core Training and Integrator Logic ---\n", "\n", "    def train(self):\n", "        squeeze = isinstance(self.criterion, torch.nn.modules.loss.BCELoss)\n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\")\n", "\n", "        for p in self.model.parameters():\n", "            p.buf = torch.normal(torch.zeros_like(p), np.sqrt(0.5 * self.T)).to(self.device)\n", "\n", "        # Create a loader with the initial_batch_size for the first gradient and first evaluation\n", "        initial_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True)\n", "        (data, target) = next(iter(initial_loader))\n", "        data, target = data.to(self.device), target.to(self.device)\n", "        self.fill_gradients(data, target, squeeze)\n", "\n", "        results_eval = []\n", "        # Use the same loader for a consistent initial evaluation\n", "        initial_metrics = evaluate(self.model, initial_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "        results_eval.append(initial_metrics)\n", "\n", "        # consistent_eval_train_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.initial_batch_size, shuffle=False)\n", "\n", "        start_time = time.time()\n", "        print(\"Starting ZBAOABZ sampling with adaptive batch size...\")\n", "\n", "        self.set_gradnorm()\n", "        self.zeta_dt = self.gradnorm\n", "        self.<PERSON><PERSON>_transform_dt()\n", "\n", "        # Record initial dynamic state\n", "        self.dt_raw.append(self.lr)\n", "        self.zeta_dt_raw.append(self.zeta_dt)\n", "        self.batch_size_raw.append(self.batch_size)\n", "        self.zeta_batch_raw.append(self.zeta_batch)\n", "        self.ess_raw.append(np.nan)\n", "        tkin_init, tconf_init = self.get_current_temps()\n", "        self.tkin_raw.append(tkin_init)\n", "        self.tconf_raw.append(tconf_init)\n", "\n", "        # step_counter = 0\n", "        for epoch in range(1, self.epochs + 1):\n", "            self.model.train()\n", "\n", "            # adjust stepsize and temperature (optional)\n", "            if self.lr_schedule is not None and epoch % self.lr_schedule[0]==0:\n", "                self.dtau *= self.lr_schedule[1]\n", "\n", "            if self.T_schedule is not None and epoch % self.T_schedule[self.T_idx][0]==0:\n", "                new_T = self.T_schedule[self.T_idx][1]\n", "                print(\"Adjusting temperature to \", new_T)\n", "                self.change_temperature(new_T)\n", "                self.T_idx += 1\n", "                if self.T_idx == len(self.T_schedule):\n", "                    self.T_schedule = None\n", "\n", "            # --- NEW: One-time batch size reset after burn-in ---\n", "            if epoch == self.burn_in_epochs + 1 and not self.post_burn_in_reset_done:\n", "                print(f\"--- Burn-in complete. Resetting batch size to {self.post_burn_in_initial_batch_size} ---\")\n", "                self.batch_size = self.post_burn_in_initial_batch_size\n", "                self.post_burn_in_reset_done = True\n", "\n", "            shuffled_indices = torch.randperm(self.datasize)\n", "            current_index = 0\n", "\n", "            while current_index < self.datasize:\n", "                # step_counter += 1\n", "                \n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "                self.a = self.a_gamma**(self.lr)\n", "                self.sqrt_aT = torch.sqrt((1 - self.a**2) * self.T)\n", "\n", "                self.update_params_BAOA()\n", "\n", "                # self.update_params_AOA()\n", "\n", "                if epoch > self.burn_in_epochs:\n", "                    self.adapt_batch_size()\n", "\n", "                # Store every batch size used for final analysis\n", "                self.all_batch_sizes_in_training.append(self.batch_size)\n", "\n", "                batch_end_index = min(current_index + self.batch_size, self.datasize)\n", "                batch_indices = shuffled_indices[current_index:batch_end_index]\n", "\n", "                data = self.train_dataset.data[batch_indices]\n", "                target = self.train_dataset.targets[batch_indices]\n", "\n", "                # Manually apply the transform to the batch of images\n", "                # The transform expects a PIL image, so we convert each tensor image\n", "                if self.train_dataset.transform:\n", "                    data_transformed = []\n", "                    for img_tensor in data:\n", "                        # Convert tensor to PIL Image\n", "                        pil_img = Image.fromarray(img_tensor.numpy())\n", "                        # Apply the transform\n", "                        data_transformed.append(self.train_dataset.transform(pil_img))\n", "                    # Stack the transformed images back into a single tensor\n", "                    data = torch.stack(data_transformed)\n", "\n", "                current_index = batch_end_index\n", "\n", "                data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)\n", "                self.fill_gradients(data, target, squeeze)\n", "\n", "                self.update_params_B()\n", "\n", "                self.set_gradnorm()\n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "\n", "                self.loss_history_for_ess.append(self.running_loss.item() / self.datasize)\n", "\n", "            if epoch % self.meas_freq == 0:\n", "                # For subsequent evaluations, create a loader with the same consistent initial batch size\n", "                # consistent_eval_train_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=False)\n", "                eval_metrics = evaluate(self.model, self.eval_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "                results_eval.append(eval_metrics)\n", "\n", "                tkin_now, tconf_now = self.get_current_temps()\n", "                self.tkin_raw.append(tkin_now)\n", "                self.tconf_raw.append(tconf_now)\n", "\n", "                self.dt_raw.append(self.lr)\n", "                self.zeta_dt_raw.append(self.zeta_dt)\n", "                self.batch_size_raw.append(self.batch_size)\n", "                self.zeta_batch_raw.append(self.zeta_batch)\n", "                ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "                self.ess_raw.append(ess_value)\n", "\n", "                print(f\"ZBAOABZ EPOCH {epoch} DONE! | Test Accu: {eval_metrics[2]:.2f}% | dt: {self.lr.item():.6f} | Batch: {self.batch_size}\")\n", "\n", "        end_time = time.time()\n", "        print(f\"Training took {end_time-start_time:.2f} seconds\")\n", "\n", "        # # --- NEW: Calculate and print mean batch sizes ---\n", "        # all_batches = np.array(self.all_batch_sizes_in_training)\n", "        # if len(all_batches) > 0:\n", "        #     steps_per_epoch = len(all_batches) / self.epochs\n", "        #     burn_in_idx = int(self.burn_in_epochs * steps_per_epoch)\n", "\n", "        #     mean_batch_overall = np.mean(all_batches)\n", "        #     mean_batch_post_burn = np.mean(all_batches[burn_in_idx:]) if burn_in_idx < len(all_batches) else np.nan\n", "\n", "        #     print(f\"\\n--- Adaptive Batch Size Stats ---\")\n", "        #     print(f\"Mean batch size (overall): {mean_batch_overall:.2f}\")\n", "        #     print(f\"Mean batch size (post-burn-in): {mean_batch_post_burn:.2f}\")\n", "        #     print(f\"---------------------------------\")\n", "\n", "        results_eval = np.array(results_eval)\n", "        dt_np = np.array([i.cpu().item() for i in self.dt_raw])\n", "        zeta_dt_np = np.array([i.cpu().item() for i in self.zeta_dt_raw])\n", "        batch_size_np = np.array(self.batch_size_raw)\n", "        zeta_batch_np = np.array(self.zeta_batch_raw)\n", "        ess_np = np.array(self.ess_raw)\n", "        tkin_np = np.array(self.tkin_raw)\n", "        tconf_np = np.array(self.tconf_raw)\n", "\n", "        epoch_axis = np.arange(0, len(results_eval[:,0])) * self.meas_freq\n", "\n", "        results = np.column_stack((epoch_axis,\n", "                                   results_eval[:,:3],\n", "                                   tkin_np, tconf_np,\n", "                                   results_eval[:,3:],\n", "                                   ess_np, dt_np, zeta_dt_np, batch_size_np, zeta_batch_np))\n", "\n", "        return results, self.results_header\n", "\n", "    def update_params_BAOA(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "            eps = torch.randn_like(p.buf)\n", "            p.buf.mul_(self.a)\n", "            p.buf.add_(eps, alpha=self.sqrt_aT)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "\n", "    def update_params_AOA(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "            eps = torch.randn_like(p.buf)\n", "            p.buf.mul_(self.a)\n", "            p.buf.add_(eps, alpha=self.sqrt_aT)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "\n", "\n", "    def update_params_B(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "\n", "    def fill_gradients(self, data, target, squeeze):\n", "        self.model.zero_grad()\n", "        output = self.model(data)\n", "        if squeeze: output = output.squeeze()\n", "        self.running_loss = self.criterion(output, target) * self.datasize\n", "        self.running_loss.backward()\n", "        for p in self.model.parameters():\n", "            if p.grad is not None:\n", "                p.grad.data.add_(p.data, alpha=self.weight_decay)\n", "\n", "    def change_temperature(self, T):\n", "        \"\"\"\n", "        Changes the temperature of the sampler.\n", "        \"\"\"\n", "        self.T = T\n", "\n", "def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "    \"\"\"\n", "    Returns a list containing:\n", "    [train loss, train accuracy, test accuracy, NLL, Brier Score, ECE, OOD AUC]\n", "    \"\"\"\n", "    model.eval()\n", "    all_test_preds, all_test_labels, all_ood_preds = [], [], []\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            probs = torch.exp(output)\n", "            all_test_preds.append(probs)\n", "            all_test_labels.append(target)\n", "        all_test_preds = torch.cat(all_test_preds, dim=0)\n", "        all_test_labels = torch.cat(all_test_labels, dim=0)\n", "        for data, _ in ood_loader:\n", "            data = data.to(device, non_blocking=True)\n", "            output = model(data)\n", "            all_ood_preds.append(torch.exp(output))\n", "        all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "    \n", "    nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "    brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "    ece = calculate_ece(all_test_preds, all_test_labels)\n", "    test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "    entropy_id = get_predictive_entropy(all_test_preds)\n", "    entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "    entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "    ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "    correct, loss = 0, 0\n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "    loss /= len(train_loader)\n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "    \n", "    return [loss, train_accu, test_accu, nll, brier, ece, ood_auc]\n", "\n", "# ... (The rest of your code: helper functions, model definition, parameters, and training script can remain the same) ...\n", "\n", "def calculate_ess(chain):\n", "    if len(chain) < 10: return np.nan\n", "    try:\n", "        return az.ess(chain).item()\n", "    except:\n", "        return np.nan\n", "\n", "def calculate_ece(preds, labels, n_bins=15):\n", "    bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "    bin_lowers = bin_boundaries[:-1]\n", "    bin_uppers = bin_boundaries[1:]\n", "    confidences, predictions = torch.max(preds, 1)\n", "    accuracies = predictions.eq(labels)\n", "    ece = torch.zeros(1, device=preds.device)\n", "    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "        in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "        prop_in_bin = in_bin.float().mean()\n", "        if prop_in_bin.item() > 0:\n", "            accuracy_in_bin = accuracies[in_bin].float().mean()\n", "            avg_confidence_in_bin = confidences[in_bin].mean()\n", "            ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "    return ece.item()\n", "\n", "def calculate_brier_score(preds, labels):\n", "    one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "    return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "def get_predictive_entropy(preds):\n", "    return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "        self.fc_input_size = 128 * 3 * 3\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = F.log_softmax(x, dim=1)\n", "        return x\n", "\n", "#%% MAIN SCRIPT\n", "if __name__ == '__main__':\n", "    epochs = 60\n", "    initial_B_train = 10000\n", "\n", "    min_B_train = 1000\n", "    max_B_train = 30000\n", "    # --- NEW: Parameter for post-burn-in batch size ---\n", "    post_burn_in_initial_B_train = 30000 # e.g., set to max_batch_size\n", "\n", "    seed = 2\n", "    meas_freq = 5\n", "    gamma = 1\n", "    temperature = 1\n", "    weight_decay = 1e-5\n", "\n", "    alpha_dt = 50\n", "    scale_g_dt = 1/60000\n", "    dtau = 4e-4\n", "    m_dt = 0.1\n", "    M_dt = 10\n", "\n", "    burn_in_epochs = 5\n", "    alpha_batch = 10.0  # 8–12 (keep slower than alpha_dt=50)\n", "    threshold_batch = 5.0\n", "    steepness_batch = 25.0\n", "    scale_g_batch = 1\n", "\n", "    delta_temp = 0.05 # tolerance ±5%; try 0.10 if you want a wider “do-nothing” band\n", "    k_sigma = 1.0 # monitor gain; increase if σ responds too weakly; decrease if chattery\n", "    c_sigma = 3.0  # 2–4 is a good start. “hot,” increase c_sigma; jumps to bounds or oscillates, reduce c_sigma\n", "    alpha_e = 12.0 # temp-error EMA rate; smooths over a few inner steps\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "\n", "    transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.5,), (0.5,))])\n", "    train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "    test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "    ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "\n", "    torch.manual_seed(seed)\n", "    model = SimpleCNN().to(device)\n", "\n", "    # CORRECTED: Create test/ood loaders with the consistent initial_B_train\n", "    eval_loader = torch.utils.data.DataLoader(train_dataset, batch_size=initial_B_train, shuffle=False)\n", "    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=initial_B_train, shuffle=False)\n", "    ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=initial_B_train, shuffle=False)\n", "\n", "    sampler1 = ZBAOABZ_with_batch(\n", "        model, train_dataset, eval_loader, test_loader, ood_loader, nn.NLLLoss(reduction=\"mean\"),\n", "        dtau=dtau, weight_decay=weight_decay, gamma=gamma, temperature=temperature, \n", "        epochs=epochs, device=device, meas_freq=meas_freq,\n", "        alpha_dt=alpha_dt, scale_g_dt=scale_g_dt, m_dt=m_dt, M_dt=M_dt,\n", "        burn_in_epochs=burn_in_epochs, \n", "        initial_batch_size=initial_B_train,\n", "        post_burn_in_initial_batch_size=post_burn_in_initial_B_train, # NEW\n", "        min_batch_size=min_B_train, max_batch_size=max_B_train,\n", "        alpha_batch=alpha_batch, delta_temp=delta_temp, k_sigma=k_sigma, \n", "        c_sigma=c_sigma, scale_g_batch=scale_g_batch\n", "    )\n", "\n", "    results1, header1 = sampler1.train()\n", "    results_dict1 = {h: results1[:, i] for i, h in enumerate(header1)}\n", "\n", "    # --- NEW: Plotting for batch size distribution ---\n", "    all_batches = np.array(sampler1.all_batch_sizes_in_training)\n", "    if len(all_batches) > 0:\n", "        steps_per_epoch = len(all_batches) / epochs\n", "        burn_in_idx = int(burn_in_epochs * steps_per_epoch)\n", "        post_burn_batches = all_batches[burn_in_idx:]\n", "        \n", "        mean_batch_overall = np.mean(all_batches)\n", "        mean_batch_post_burn = np.mean(all_batches[burn_in_idx:]) if burn_in_idx < len(all_batches) else np.nan\n", "\n", "        print(f\"\\n--- Adaptive Batch Size Stats ---\")\n", "        print(f\"Mean batch size (overall): {mean_batch_overall:.2f}\")\n", "        print(f\"Mean batch size (post-burn-in): {mean_batch_post_burn:.2f}\")\n", "        print(f\"---------------------------------\")\n", "\n", "        plt.figure(figsize=(8, 6))\n", "        plt.hist(post_burn_batches, bins=50, density=True, label='Batch Size Distribution')\n", "        plt.axvline(np.mean(post_burn_batches), color='r', linestyle='--', label=f'Mean: {np.mean(post_burn_batches):.2f}')\n", "        plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "        plt.xlabel('<PERSON><PERSON> Size')\n", "        plt.ylabel('Density')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        plt.show()\n", "\n", "    fig, ax = plt.subplots(3, 2, figsize=(14, 12))\n", "    ax = ax.ravel()\n", "    ax[0].plot(results_dict1[\"Epoch\"], results_dict1[\"Train Loss\"], marker='o')\n", "    ax[0].set_title(\"Training Loss\")\n", "    ax[0].set_ylabel(\"NLL Loss\")\n", "    ax[1].plot(results_dict1[\"Epoch\"], results_dict1[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "    ax[1].plot(results_dict1[\"Epoch\"], results_dict1[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "    ax[1].set_title(\"Train and Test Accuracy\")\n", "    ax[1].set_ylabel(\"Accuracy (%)\")\n", "    ax[1].legend()\n", "    ax[2].plot(results_dict1[\"Epoch\"], results_dict1[\"dt\"], marker='o', color='purple')\n", "    ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "    ax[2].set_ylabel(\"Stepsize\")\n", "    ax[3].plot(results_dict1[\"Epoch\"], results_dict1[\"Batch Size\"], marker='o', color='green')\n", "    ax[3].set_title(\"Adaptive Batch Size\")\n", "    ax[3].set_ylabel(\"Batch Size\")\n", "    ax[3].axhline(min_B_train, color='k', linestyle=':', lw=1)\n", "    ax[3].axhline(max_B_train, color='k', linestyle=':', lw=1)\n", "    ax[4].plot(results_dict1[\"Epoch\"], results_dict1[\"Tconf\"], marker='o', color='red')\n", "    ax[4].plot(results_dict1[\"Epoch\"], results_dict1[\"Tkin\"], marker='o', color='blue')\n", "    ax[4].axhline(temperature, color='k', linestyle='--')\n", "    ax[4].set_title(\"Configurational Temperature\")\n", "    ax[4].set_ylabel(\"T_conf\")\n", "    ax[5].plot(results_dict1[\"Epoch\"], results_dict1[\"zeta_batch\"], marker='o', color='orange')\n", "    ax[5].axhline(0, color='k', linestyle='--')\n", "    ax[5].set_title(\"Batch Size Auxiliary Variable (zeta_batch)\")\n", "    ax[5].set_ylabel(\"zeta_batch\")\n", "    for a in ax:\n", "        a.set_xlabel(\"Epochs\")\n", "        a.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["****"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%% Imports\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import time\n", "import arviz as az\n", "import math\n", "\n", "import torchvision\n", "from torchvision import datasets, transforms\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_auc_score\n", "\n", "#%% ZBAOABZ with Adaptive Batch Size\n", "\n", "class ZBAOABZ_with_batch(nn.Module):\n", "    \"\"\"\n", "    ZBAOABZ Sampler with two adaptive controllers:\n", "    1. <PERSON><PERSON><PERSON><PERSON> for step-size (dt) based on gradient norm.\n", "    2. Adaptive Bat<PERSON> Si<PERSON> (B) based on configurational temperature.\n", "    \"\"\"\n", "\n", "    def __init__(self, model, train_dataset, eval_loader, test_loader, ood_loader, criterion, \n", "                 # Sam<PERSON> params\n", "                 dtau, weight_decay, gamma, temperature, epochs, device, meas_freq,\n", "                 # dt control params\n", "                 alpha_dt, scale_g_dt, m_dt, M_dt,\n", "                 # --- NEW: Batch size control params ---\n", "                 burn_in_epochs, initial_batch_size, min_batch_size, max_batch_size,\n", "                 alpha_batch, threshold_batch, steepness_batch, scale_g_batch,\n", "                 # Schedules\n", "                 lr_schedule=None, T_schedule=None):\n", "        super(ZBAOABZ_with_batch, self).__init__()\n", "        \n", "        # Core components\n", "        self.model = model\n", "        self.train_dataset = train_dataset # Store dataset, not loader\n", "        self.eval_loader = eval_loader\n", "        self.test_loader = test_loader\n", "        self.ood_loader = ood_loader\n", "        self.criterion = criterion\n", "        self.device = device\n", "        self.epochs = epochs\n", "        self.meas_freq = meas_freq\n", "        self.weight_decay = weight_decay\n", "        self.gamma = gamma\n", "        self.T = temperature\n", "        \n", "        # --- State variables for dt control ---\n", "        self.dtau = dtau\n", "        self.alpha_dt = alpha_dt\n", "        self.scale_g_dt = scale_g_dt # This is Omega_inv in the paper\n", "        self.m_dt = m_dt\n", "        self.M_dt = M_dt\n", "        self.r_dt = 0.25\n", "        self.lr = None # dt\n", "        self.zeta_dt = None\n", "        self.gradnorm = None\n", "        self.exptau_half_dt = np.exp(-0.5 * self.alpha_dt * self.dtau)\n", "        self.alpha_inv_dt = 1 / self.alpha_dt if self.alpha_dt > 0 else 0\n", "\n", "        # --- NEW: State variables for batch size control ---\n", "        self.burn_in_epochs = burn_in_epochs\n", "        self.min_batch_size = min_batch_size\n", "        self.max_batch_size = max_batch_size\n", "        self.alpha_batch = alpha_batch\n", "        self.threshold_batch = threshold_batch\n", "        self.steepness_batch = steepness_batch\n", "        self.scale_g_batch = scale_g_batch # This is Omega_inv for monitor function of sigma\n", "        \n", "        self.batch_size = initial_batch_size\n", "        self.zeta_batch = 0.0 # Start auxiliary variable at neutral\n", "        # Calculate initial sigma from initial batch size\n", "        self.datasize = len(self.train_dataset)\n", "        self.sigma = (self.datasize - self.batch_size) / self.batch_size if self.batch_size > 0 else float('inf')\n", "        # Pre-calculate sigma bounds from batch size bounds\n", "        self.sigma_min = (self.datasize - self.max_batch_size) / self.max_batch_size if self.max_batch_size > 0 else float('inf')\n", "        self.sigma_max = (self.datasize - self.min_batch_size) / self.min_batch_size if self.min_batch_size > 0 else float('inf')\n", "        \n", "        # --- Pre-calculated constants ---\n", "        self.a_gamma = np.exp(-self.gamma)\n", "        self.a = None\n", "        self.sqrt_aT = None\n", "        \n", "        # --- Schedules ---\n", "        self.lr_schedule = lr_schedule\n", "        self.T_schedule = T_schedule\n", "        self.T_idx = 0\n", "        \n", "        # --- Metrics Storage ---\n", "        self.dt_raw = []\n", "        self.zeta_dt_raw = []\n", "        self.batch_size_raw = []\n", "        self.zeta_batch_raw = []\n", "        self.ess_raw = []\n", "        self.tkin_raw = []\n", "        self.tconf_raw = []\n", "        self.loss_history_for_ess = []\n", "        self.all_batch_sizes_in_training = [] # For calculating mean batch size\n", "        \n", "        self.results_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \n", "                               \"Tkin\", \"Tconf\", \"NLL\", \"Brier\", \"ECE\", \n", "                               \"OOD AUC\", \"ESS\", \"dt\", \"zeta_dt\", \"Batch Size\", \"zeta_batch\"]\n", "\n", "    # --- NEW: Methods for Adaptive Batch Size Control ---\n", "\n", "    def monitor_g_batch(self, T_conf):\n", "        \"\"\" Two-way linear monitor for temperature gap. \"\"\"\n", "        gap = np.abs(T_conf - self.T) / self.T if self.T > 0 else np.abs(T_conf)\n", "        return gap - self.threshold_batch\n", "\n", "    def psi_batch(self):\n", "        \"\"\" Arctan mapping from zeta_batch to sigma. \"\"\"\n", "        midpoint = (self.sigma_min + self.sigma_max) / 2.0\n", "        sigma_range = self.sigma_max - self.sigma_min\n", "        atan_val = np.arctan(self.steepness_batch * self.zeta_batch)\n", "        # Use a minus sign to make it a decreasing function\n", "        return midpoint - (sigma_range / np.pi) * atan_val\n", "\n", "    def zeta_step_batch(self, g_val):\n", "        \"\"\" Update rule for the batch size auxiliary variable. \"\"\"\n", "        # The effective time step for this update is dtau, as it happens once per ZBAOABZ step\n", "        if self.alpha_batch <= 0: return\n", "        rho = np.exp(-self.alpha_batch * self.dtau)\n", "        self.zeta_batch = rho * self.zeta_batch + (1 - rho) / self.alpha_batch * g_val\n", "\n", "    def get_current_temps(self):\n", "        \"\"\" Calculates instantaneous Tkin and Tconf. \"\"\"\n", "        <PERSON><PERSON>, <PERSON><PERSON><PERSON>, param_count = 0, 0, 0\n", "        with torch.no_grad():\n", "            for param in self.model.parameters():\n", "                if hasattr(param, 'buf') and param.grad is not None:\n", "                    Tkin += (param.buf**2).sum().item()\n", "                    Tconf += (param.data * param.grad.data).sum().item()\n", "                    param_count += param.numel()\n", "        \n", "        Tkin_val = Tkin / param_count if param_count > 0 else 0\n", "        Tconf_val = Tconf / param_count if param_count > 0 else 0\n", "        return Tkin_val, Tconf_val\n", "        \n", "    def adapt_batch_size(self):\n", "        \"\"\"\n", "        Orchestrates the batch size adaptation. This is called once per ZBAOABZ step.\n", "        \"\"\"\n", "        # 1. Get current T_conf (lagging indicator from previous step's gradient)\n", "        <PERSON><PERSON>, T_conf = self.get_current_temps()\n", "        \n", "        # 2. Get monitor value\n", "        g_val_kin = self.monitor_g_batch(Tkin)\n", "        g_val_conf = self.monitor_g_batch(T_conf)\n", "        g_val = max(g_val_kin, g_val_conf)\n", "\n", "        g_val *= self.scale_g_batch\n", "        \n", "        # 3. Update auxiliary variable\n", "        self.zeta_step_batch(g_val)\n", "        \n", "        # 4. Map to new sigma\n", "        new_sigma = self.psi_batch()\n", "        self.sigma = np.clip(new_sigma, self.sigma_min, self.sigma_max)\n", "        \n", "        # 5. Convert sigma to new batch size B\n", "        if (self.sigma + 1) == 0: # Avoid division by zero\n", "            new_B_float = float('inf')\n", "        else:\n", "            new_B_float = self.datasize / (self.sigma + 1)\n", "        self.batch_size = int(np.round( (new_B_float, self.min_batch_size, self.max_batch_size)))\n", "\n", "    # --- Methods for Adaptive Step Size (dt) Control ---\n", "    \n", "    def set_gradnorm(self):\n", "        self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters() if p.grad is not None)\n", "        self.gradnorm *= self.scale_g_dt\n", "\n", "    def Z_step_dt(self):\n", "        if self.alpha_dt <= 0: return\n", "        self.zeta_dt = self.exptau_half_dt * self.zeta_dt + self.alpha_inv_dt * (1 - self.exptau_half_dt) * self.gradnorm\n", "\n", "    def Sundman_transform_dt(self):\n", "        # --- BUG FIX: Clamp zeta_dt to prevent NaN from negative numbers ---\n", "        zeta_r = self.zeta_dt**self.r_dt # r=0.25\n", "        self.lr = self.dtau * self.m_dt * (zeta_r + self.M_dt / self.m_dt) / (zeta_r + 1) # K2 kernel\n", "\n", "    # --- Core Training and Integrator Logic ---\n", "\n", "    def train(self):\n", "        squeeze = isinstance(self.criterion, torch.nn.modules.loss.BCELoss)\n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\")\n", "\n", "        for p in self.model.parameters():\n", "            p.buf = torch.normal(torch.zeros_like(p), np.sqrt(0.5 * self.T)).to(self.device)\n", "\n", "        # Create a loader with the initial_batch_size for the first gradient and first evaluation\n", "        initial_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True)\n", "        (data, target) = next(iter(initial_loader))\n", "        data, target = data.to(self.device), target.to(self.device)\n", "        self.fill_gradients(data, target, squeeze)\n", "        \n", "        results_eval = []\n", "        # Use the same loader for a consistent initial evaluation\n", "        initial_metrics = evaluate(self.model, initial_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "        results_eval.append(initial_metrics)\n", "        \n", "        # consistent_eval_train_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.initial_batch_size, shuffle=False)\n", "\n", "        start_time = time.time()\n", "        print(\"Starting ZBAOABZ sampling with adaptive batch size...\")\n", "        \n", "        self.set_gradnorm()\n", "        self.zeta_dt = self.gradnorm\n", "        self.<PERSON><PERSON>_transform_dt()\n", "        \n", "        # Record initial dynamic state\n", "        self.dt_raw.append(self.lr)\n", "        self.zeta_dt_raw.append(self.zeta_dt)\n", "        self.batch_size_raw.append(self.batch_size)\n", "        self.zeta_batch_raw.append(self.zeta_batch)\n", "        self.ess_raw.append(np.nan)\n", "        tkin_init, tconf_init = self.get_current_temps()\n", "        self.tkin_raw.append(tkin_init)\n", "        self.tconf_raw.append(tconf_init)\n", "\n", "        # step_counter = 0\n", "        for epoch in range(1, self.epochs + 1):\n", "            self.model.train()\n", "            \n", "            # adjust stepsize and temperature (optional)\n", "            if self.lr_schedule is not None and epoch % self.lr_schedule[0]==0:\n", "                self.dtau *= self.lr_schedule[1]\n", "\n", "            if self.T_schedule is not None and epoch % self.T_schedule[self.T_idx][0]==0:\n", "                new_T = self.T_schedule[self.T_idx][1]\n", "                print(\"Adjusting temperature to \", new_T)\n", "                self.change_temperature(new_T)\n", "                self.T_idx += 1\n", "                if self.T_idx == len(self.T_schedule):\n", "                    self.T_schedule = None\n", "\n", "            shuffled_indices = torch.randperm(self.datasize)\n", "            current_index = 0\n", "            \n", "            while current_index < self.datasize:\n", "                # step_counter += 1\n", "                \n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "                self.a = self.a_gamma**(self.lr)\n", "                self.sqrt_aT = torch.sqrt((1 - self.a**2) * self.T)\n", "                \n", "                self.update_params_BAOA()\n", "\n", "                if epoch > self.burn_in_epochs:\n", "                    self.adapt_batch_size()\n", "                \n", "                # Store every batch size used for final analysis\n", "                self.all_batch_sizes_in_training.append(self.batch_size)\n", "\n", "                batch_end_index = min(current_index + self.batch_size, self.datasize)\n", "                batch_indices = shuffled_indices[current_index:batch_end_index]\n", "                \n", "                data = self.train_dataset.data[batch_indices]\n", "                target = self.train_dataset.targets[batch_indices]\n", "\n", "                # Manually apply the transform to the batch of images\n", "                # The transform expects a PIL image, so we convert each tensor image\n", "                if self.train_dataset.transform:\n", "                    data_transformed = []\n", "                    for img_tensor in data:\n", "                        # Convert tensor to PIL Image\n", "                        pil_img = Image.fromarray(img_tensor.numpy(), mode='L')\n", "                        # Apply the transform\n", "                        data_transformed.append(self.train_dataset.transform(pil_img))\n", "                    # Stack the transformed images back into a single tensor\n", "                    data = torch.stack(data_transformed)\n", "\n", "                current_index = batch_end_index\n", "                \n", "                data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)\n", "                self.fill_gradients(data, target, squeeze)\n", "                \n", "                self.update_params_B()\n", "                \n", "                self.set_gradnorm()\n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "\n", "                self.loss_history_for_ess.append(self.running_loss.item() / self.datasize)\n", "\n", "            if epoch % self.meas_freq == 0:\n", "                # For subsequent evaluations, create a loader with the same consistent initial batch size\n", "                # consistent_eval_train_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=False)\n", "                eval_metrics = evaluate(self.model, self.eval_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "                results_eval.append(eval_metrics)\n", "                \n", "                tkin_now, tconf_now = self.get_current_temps()\n", "                self.tkin_raw.append(tkin_now)\n", "                self.tconf_raw.append(tconf_now)\n", "                \n", "                self.dt_raw.append(self.lr)\n", "                self.zeta_dt_raw.append(self.zeta_dt)\n", "                self.batch_size_raw.append(self.batch_size)\n", "                self.zeta_batch_raw.append(self.zeta_batch)\n", "                ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "                self.ess_raw.append(ess_value)\n", "\n", "                print(f\"ZBAOABZ EPOCH {epoch} DONE! | Test Accu: {eval_metrics[2]:.2f}% | dt: {self.lr.item():.6f} | Batch: {self.batch_size}\")\n", "\n", "        end_time = time.time()\n", "        print(f\"Training took {end_time-start_time:.2f} seconds\")\n", "        \n", "        # # --- NEW: Calculate and print mean batch sizes ---\n", "        # all_batches = np.array(self.all_batch_sizes_in_training)\n", "        # if len(all_batches) > 0:\n", "        #     steps_per_epoch = len(all_batches) / self.epochs\n", "        #     burn_in_idx = int(self.burn_in_epochs * steps_per_epoch)\n", "            \n", "        #     mean_batch_overall = np.mean(all_batches)\n", "        #     mean_batch_post_burn = np.mean(all_batches[burn_in_idx:]) if burn_in_idx < len(all_batches) else np.nan\n", "            \n", "        #     print(f\"\\n--- Adaptive Batch Size Stats ---\")\n", "        #     print(f\"Mean batch size (overall): {mean_batch_overall:.2f}\")\n", "        #     print(f\"Mean batch size (post-burn-in): {mean_batch_post_burn:.2f}\")\n", "        #     print(f\"---------------------------------\")\n", "\n", "        results_eval = np.array(results_eval)\n", "        dt_np = np.array([i.cpu().item() for i in self.dt_raw])\n", "        zeta_dt_np = np.array([i.cpu().item() for i in self.zeta_dt_raw])\n", "        batch_size_np = np.array(self.batch_size_raw)\n", "        zeta_batch_np = np.array(self.zeta_batch_raw)\n", "        ess_np = np.array(self.ess_raw)\n", "        tkin_np = np.array(self.tkin_raw)\n", "        tconf_np = np.array(self.tconf_raw)\n", "        \n", "        epoch_axis = np.arange(0, len(results_eval[:,0])) * self.meas_freq\n", "        \n", "        results = np.column_stack((epoch_axis, \n", "                                   results_eval[:,:3],\n", "                                   tkin_np, tconf_np,\n", "                                   results_eval[:,3:],\n", "                                   ess_np, dt_np, zeta_dt_np, batch_size_np, zeta_batch_np))\n", "\n", "        return results, self.results_header\n", "\n", "    def update_params_BAOA(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "            eps = torch.randn_like(p.buf)\n", "            p.buf.mul_(self.a)\n", "            p.buf.add_(eps, alpha=self.sqrt_aT)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "\n", "    def update_params_B(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "\n", "    def fill_gradients(self, data, target, squeeze):\n", "        self.model.zero_grad()\n", "        output = self.model(data)\n", "        if squeeze: output = output.squeeze()\n", "        self.running_loss = self.criterion(output, target) * self.datasize\n", "        self.running_loss.backward()\n", "        for p in self.model.parameters():\n", "            if p.grad is not None:\n", "                p.grad.data.add_(p.data, alpha=self.weight_decay)\n", "\n", "    def change_temperature(self, T):\n", "        \"\"\"\n", "        Changes the temperature of the sampler.\n", "        \"\"\"\n", "        self.T = T\n", "\n", "def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "    \"\"\"\n", "    Returns a list containing:\n", "    [train loss, train accuracy, test accuracy, NLL, Brier Score, ECE, OOD AUC]\n", "    \"\"\"\n", "    model.eval()\n", "    all_test_preds, all_test_labels, all_ood_preds = [], [], []\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            probs = torch.exp(output)\n", "            all_test_preds.append(probs)\n", "            all_test_labels.append(target)\n", "        all_test_preds = torch.cat(all_test_preds, dim=0)\n", "        all_test_labels = torch.cat(all_test_labels, dim=0)\n", "        for data, _ in ood_loader:\n", "            data = data.to(device, non_blocking=True)\n", "            output = model(data)\n", "            all_ood_preds.append(torch.exp(output))\n", "        all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "    \n", "    nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "    brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "    ece = calculate_ece(all_test_preds, all_test_labels)\n", "    test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "    entropy_id = get_predictive_entropy(all_test_preds)\n", "    entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "    entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "    ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "    correct, loss = 0, 0\n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "    loss /= len(train_loader)\n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "    \n", "    return [loss, train_accu, test_accu, nll, brier, ece, ood_auc]\n", "\n", "# ... (The rest of your code: helper functions, model definition, parameters, and training script can remain the same) ...\n", "\n", "def calculate_ess(chain):\n", "    if len(chain) < 10: return np.nan\n", "    try:\n", "        return az.ess(chain).item()\n", "    except:\n", "        return np.nan\n", "\n", "def calculate_ece(preds, labels, n_bins=15):\n", "    bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "    bin_lowers = bin_boundaries[:-1]\n", "    bin_uppers = bin_boundaries[1:]\n", "    confidences, predictions = torch.max(preds, 1)\n", "    accuracies = predictions.eq(labels)\n", "    ece = torch.zeros(1, device=preds.device)\n", "    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "        in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "        prop_in_bin = in_bin.float().mean()\n", "        if prop_in_bin.item() > 0:\n", "            accuracy_in_bin = accuracies[in_bin].float().mean()\n", "            avg_confidence_in_bin = confidences[in_bin].mean()\n", "            ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "    return ece.item()\n", "\n", "def calculate_brier_score(preds, labels):\n", "    one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "    return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "def get_predictive_entropy(preds):\n", "    return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "        self.fc_input_size = 128 * 3 * 3\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = F.log_softmax(x, dim=1)\n", "        return x\n", "\n", "#%% MAIN SCRIPT\n", "if __name__ == '__main__':\n", "    epochs = 60\n", "    initial_B_train = 10000\n", "    min_B_train = 512\n", "    max_B_train = 10000\n", "    seed = 2\n", "    meas_freq = 5\n", "    gamma = 1\n", "    temperature = 1\n", "    weight_decay = 1e-5\n", "    \n", "    alpha_dt = 50\n", "    scale_g = 1/60000\n", "    dtau = 4e-4\n", "    m_dt = 0.1\n", "    M_dt = 10\n", "\n", "    burn_in_epochs = 3\n", "    alpha_batch = 0.05\n", "    threshold_batch = 0.01\n", "    steepness_batch = 1.0\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "\n", "    transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.5,), (0.5,))])\n", "    train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "    test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "    ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "    \n", "    torch.manual_seed(seed)\n", "    model = SimpleCNN().to(device)\n", "\n", "    # CORRECTED: Create test/ood loaders with the consistent initial_B_train\n", "    eval_loader = torch.utils.data.DataLoader(train_dataset, batch_size=initial_B_train, shuffle=False)\n", "    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=initial_B_train, shuffle=False)\n", "    ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=initial_B_train, shuffle=False)\n", "\n", "    sampler = ZBAOABZ_with_batch(\n", "        model, train_dataset, eval_loader, test_loader, ood_loader, nn.NLLLoss(reduction=\"mean\"),\n", "        dtau=dtau, weight_decay=weight_decay, gamma=gamma, temperature=temperature, \n", "        epochs=epochs, device=device, meas_freq=meas_freq,\n", "        alpha_dt=alpha_dt, scale_g_dt=scale_g, m_dt=m_dt, M_dt=M_dt,\n", "        burn_in_epochs=burn_in_epochs, initial_batch_size=initial_B_train,\n", "        min_batch_size=min_B_train, max_batch_size=max_B_train,\n", "        alpha_batch=alpha_batch, threshold_batch=threshold_batch,\n", "        steepness_batch=steepness_batch, scale_g_batch=scale_g\n", "    )\n", "    \n", "    # --- NEW: Plotting for batch size distribution ---\n", "    all_batches = np.array(sampler.all_batch_sizes_in_training)\n", "    if len(all_batches) > 0:\n", "        steps_per_epoch = len(all_batches) / epochs\n", "        burn_in_idx = int(burn_in_epochs * steps_per_epoch)\n", "        post_burn_batches = all_batches[burn_in_idx:]\n", "\n", "        mean_batch_overall = np.mean(all_batches)\n", "        mean_batch_post_burn = np.mean(all_batches[burn_in_idx:]) if burn_in_idx < len(all_batches) else np.nan\n", "            \n", "        print(f\"\\n--- Adaptive Batch Size Stats ---\")\n", "        print(f\"Mean batch size (overall): {mean_batch_overall:.2f}\")\n", "        print(f\"Mean batch size (post-burn-in): {mean_batch_post_burn:.2f}\")\n", "        print(f\"---------------------------------\")\n", "\n", "        plt.figure(figsize=(8, 6))\n", "        plt.hist(post_burn_batches, bins=50, density=True, label='Batch Size Distribution')\n", "        plt.axvline(np.mean(post_burn_batches), color='r', linestyle='--', label=f'Mean: {np.mean(post_burn_batches):.2f}')\n", "        plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "        plt.xlabel('<PERSON><PERSON> Size')\n", "        plt.ylabel('Density')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        plt.show()\n", "\n", "    results, header = sampler.train()\n", "    results_dict = {h: results[:, i] for i, h in enumerate(header)}\n", "\n", "    fig, ax = plt.subplots(3, 2, figsize=(14, 12))\n", "    ax = ax.ravel()\n", "    ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Train Loss\"], marker='o')\n", "    ax[0].set_title(\"Training Loss\")\n", "    ax[0].set_ylabel(\"NLL Loss\")\n", "    ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "    ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "    ax[1].set_title(\"Train and Test Accuracy\")\n", "    ax[1].set_ylabel(\"Accuracy (%)\")\n", "    ax[1].legend()\n", "    ax[2].plot(results_dict[\"Epoch\"], results_dict[\"dt\"], marker='o', color='purple')\n", "    ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "    ax[2].set_ylabel(\"Stepsize\")\n", "    ax[3].plot(results_dict[\"Epoch\"], results_dict[\"Batch Size\"], marker='o', color='green')\n", "    ax[3].set_title(\"Adaptive Batch Size\")\n", "    ax[3].set_ylabel(\"Batch Size\")\n", "    ax[3].axhline(min_B_train, color='k', linestyle=':', lw=1)\n", "    ax[3].axhline(max_B_train, color='k', linestyle=':', lw=1)\n", "    ax[4].plot(results_dict[\"Epoch\"], results_dict[\"Tconf\"], marker='o', color='red')\n", "    ax[4].axhline(temperature, color='k', linestyle='--')\n", "    ax[4].set_title(\"Configurational Temperature\")\n", "    ax[4].set_ylabel(\"T_conf\")\n", "    ax[5].plot(results_dict[\"Epoch\"], results_dict[\"zeta_batch\"], marker='o', color='orange')\n", "    ax[5].axhline(0, color='k', linestyle='--')\n", "    ax[5].set_title(\"Batch Size Auxiliary Variable (zeta_batch)\")\n", "    ax[5].set_ylabel(\"zeta_batch\")\n", "    for a in ax:\n", "        a.set_xlabel(\"Epochs\")\n", "        a.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%% Imports\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import time\n", "import arviz as az\n", "import math\n", "\n", "import torchvision\n", "from torchvision import datasets, transforms\n", "from PIL import Image\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_auc_score\n", "\n", "#%% ZBAOABZ with Adaptive Batch Size\n", "\n", "class ZBAOABZ(nn.Module):\n", "    \"\"\"\n", "    ZBAOABZ Sampler with two adaptive controllers:\n", "    1. <PERSON><PERSON><PERSON><PERSON> for step-size (dt) based on gradient norm.\n", "    2. Adaptive Bat<PERSON> Si<PERSON> (B) based on configurational temperature.\n", "    \"\"\"\n", "\n", "    def __init__(self, model, train_dataset, test_loader, ood_loader, criterion, \n", "                 # Sam<PERSON> params\n", "                 dtau, weight_decay, gamma, temperature, epochs, device, meas_freq,\n", "                 # dt control params\n", "                 alpha_dt, scale_g, m_dt, M_dt,\n", "                 # --- NEW: Batch size control params ---\n", "                 burn_in_epochs, initial_batch_size, min_batch_size, max_batch_size,\n", "                 alpha_batch, threshold_batch, steepness_batch,\n", "                 # Schedules\n", "                 lr_schedule=None, T_schedule=None):\n", "        super(ZBAOABZ, self).__init__()\n", "        \n", "        # Core components\n", "        self.model = model\n", "        self.train_dataset = train_dataset # Store dataset, not loader\n", "        self.test_loader = test_loader\n", "        self.ood_loader = ood_loader\n", "        self.criterion = criterion\n", "        self.device = device\n", "        self.epochs = epochs\n", "        self.meas_freq = meas_freq\n", "        self.weight_decay = weight_decay\n", "        self.gamma = gamma\n", "        self.T = temperature\n", "        \n", "        # --- State variables for dt control ---\n", "        self.dtau = dtau\n", "        self.alpha_dt = alpha_dt\n", "        self.scale_g = scale_g # This is Omega_inv in the paper\n", "        self.m_dt = m_dt\n", "        self.M_dt = M_dt\n", "        self.lr = None # dt\n", "        self.zeta_dt = None\n", "        self.gradnorm = None\n", "        self.exptau_half_dt = np.exp(-0.5 * self.alpha_dt * self.dtau)\n", "        self.alpha_inv_dt = 1 / self.alpha_dt if self.alpha_dt > 0 else 0\n", "\n", "        # --- NEW: State variables for batch size control ---\n", "        self.burn_in_epochs = burn_in_epochs\n", "        self.initial_batch_size = initial_batch_size # Store for consistent evaluation\n", "        self.min_batch_size = min_batch_size\n", "        self.max_batch_size = max_batch_size\n", "        self.alpha_batch = alpha_batch\n", "        self.threshold_batch = threshold_batch\n", "        self.steepness_batch = steepness_batch\n", "        \n", "        self.batch_size = initial_batch_size\n", "        self.zeta_batch = 0.0 # Start auxiliary variable at neutral\n", "        # Calculate initial sigma from initial batch size\n", "        self.datasize = len(self.train_dataset)\n", "        self.sigma = (self.datasize - self.batch_size) / self.batch_size if self.batch_size > 0 else float('inf')\n", "        # Pre-calculate sigma bounds from batch size bounds\n", "        self.sigma_min = (self.datasize - self.max_batch_size) / self.max_batch_size if self.max_batch_size > 0 else float('inf')\n", "        self.sigma_max = (self.datasize - self.min_batch_size) / self.min_batch_size if self.min_batch_size > 0 else float('inf')\n", "        \n", "        # --- Pre-calculated constants ---\n", "        self.a_gamma = np.exp(-self.gamma)\n", "        self.a = None\n", "        self.sqrt_aT = None\n", "        \n", "        # --- Schedules ---\n", "        self.lr_schedule = lr_schedule\n", "        self.T_schedule = T_schedule\n", "        self.T_idx = 0\n", "        \n", "        # --- Metrics Storage ---\n", "        self.dt_raw = []\n", "        self.zeta_dt_raw = []\n", "        self.batch_size_raw = []\n", "        self.zeta_batch_raw = []\n", "        self.ess_raw = []\n", "        self.tkin_raw = []\n", "        self.tconf_raw = []\n", "        self.loss_history_for_ess = []\n", "        self.all_batch_sizes_in_training = [] # For calculating mean batch size\n", "        \n", "        self.results_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \n", "                               \"Tkin\", \"Tconf\", \"NLL\", \"Brier\", \"ECE\", \n", "                               \"OOD AUC\", \"ESS\", \"dt\", \"zeta_dt\", \"Batch Size\", \"zeta_batch\"]\n", "\n", "    # --- NEW: Methods for Adaptive Batch Size Control ---\n", "\n", "    def monitor_g_batch(self, T_conf):\n", "        \"\"\" Two-way linear monitor for temperature gap. \"\"\"\n", "        gap = np.abs(T_conf - self.T) / self.T if self.T > 0 else np.abs(T_conf)\n", "        return gap - self.threshold_batch\n", "\n", "    def psi_batch(self):\n", "        \"\"\" Arctan mapping from zeta_batch to sigma. \"\"\"\n", "        midpoint = (self.sigma_min + self.sigma_max) / 2.0\n", "        sigma_range = self.sigma_max - self.sigma_min\n", "        atan_val = np.arctan(self.steepness_batch * self.zeta_batch)\n", "        # Use a minus sign to make it a decreasing function\n", "        return midpoint - (sigma_range / np.pi) * atan_val\n", "\n", "    def zeta_step_batch(self, g_val):\n", "        \"\"\" Update rule for the batch size auxiliary variable. \"\"\"\n", "        # The effective time step for this update is dtau, as it happens once per ZBAOABZ step\n", "        if self.alpha_batch <= 0: return\n", "        rho = np.exp(-self.alpha_batch * self.dtau)\n", "        self.zeta_batch = rho * self.zeta_batch + (1 - rho) / self.alpha_batch * g_val\n", "\n", "    def get_current_temps(self):\n", "        \"\"\" Calculates instantaneous Tkin and Tconf. \"\"\"\n", "        <PERSON><PERSON>, <PERSON><PERSON><PERSON>, param_count = 0, 0, 0\n", "        with torch.no_grad():\n", "            for param in self.model.parameters():\n", "                if hasattr(param, 'buf') and param.grad is not None:\n", "                    Tkin += (param.buf**2).sum().item()\n", "                    Tconf += (param.data * param.grad.data).sum().item()\n", "                    param_count += param.numel()\n", "        \n", "        Tkin_val = Tkin / param_count if param_count > 0 else 0\n", "        Tconf_val = Tconf / param_count if param_count > 0 else 0\n", "        return Tkin_val, Tconf_val\n", "        \n", "    def adapt_batch_size(self):\n", "        \"\"\"\n", "        Orchestrates the batch size adaptation. This is called once per ZBAOABZ step.\n", "        \"\"\"\n", "        # 1. Get current T_conf (lagging indicator from previous step's gradient)\n", "        _, T_conf = self.get_current_temps()\n", "        \n", "        # 2. Get monitor value\n", "        g_val = self.monitor_g_batch(T_conf)\n", "        \n", "        # 3. Update auxiliary variable\n", "        self.zeta_step_batch(g_val)\n", "        \n", "        # 4. Map to new sigma\n", "        new_sigma = self.psi_batch()\n", "        self.sigma = np.clip(new_sigma, self.sigma_min, self.sigma_max)\n", "        \n", "        # 5. Convert sigma to new batch size B\n", "        if (self.sigma + 1) == 0: # Avoid division by zero\n", "            new_B_float = float('inf')\n", "        else:\n", "            new_B_float = self.datasize / (self.sigma + 1)\n", "        self.batch_size = int(np.round(np.clip(new_B_float, self.min_batch_size, self.max_batch_size)))\n", "\n", "    # --- Methods for Adaptive Step Size (dt) Control ---\n", "    \n", "    def set_gradnorm(self):\n", "        self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters() if p.grad is not None)\n", "        self.gradnorm *= self.scale_g\n", "\n", "    def Z_step_dt(self):\n", "        if self.alpha_dt <= 0: return\n", "        self.zeta_dt = self.exptau_half_dt * self.zeta_dt + self.alpha_inv_dt * (1 - self.exptau_half_dt) * self.gradnorm\n", "\n", "    def Sundman_transform_dt(self):\n", "        # --- BUG FIX: Clamp zeta_dt to prevent NaN from negative numbers ---\n", "        zeta_clamped = torch.clamp(self.zeta_dt, min=1e-8) # Use small epsilon for stability\n", "        zeta_r = zeta_clamped**0.25 # r=0.25\n", "        self.lr = self.dtau * self.m_dt * (zeta_r + self.M_dt / self.m_dt) / (zeta_r + 1) # K2 kernel\n", "\n", "    # --- Core Training and Integrator Logic ---\n", "\n", "    def train(self):\n", "        squeeze = isinstance(self.criterion, torch.nn.modules.loss.BCELoss)\n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\")\n", "\n", "        for p in self.model.parameters():\n", "            p.buf = torch.normal(torch.zeros_like(p), np.sqrt(0.5 * self.T)).to(self.device)\n", "\n", "        # Create a loader with the initial_batch_size for the first gradient and first evaluation\n", "        initial_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True)\n", "        (data, target) = next(iter(initial_loader))\n", "        data, target = data.to(self.device), target.to(self.device)\n", "        self.fill_gradients(data, target, squeeze)\n", "        \n", "        results_eval = []\n", "        # Use the same loader for a consistent initial evaluation\n", "        initial_metrics = evaluate(self.model, initial_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "        results_eval.append(initial_metrics)\n", "        \n", "        # For subsequent evaluations, use a loader with the same consistent initial batch size\n", "        consistent_eval_loader = torch.utils.data.DataLoader(self.train_dataset, batch_size=self.initial_batch_size, shuffle=False)\n", "        \n", "        start_time = time.time()\n", "        print(\"Starting ZBAOABZ sampling...\")\n", "        \n", "        self.set_gradnorm()\n", "        self.zeta_dt = self.gradnorm\n", "        self.<PERSON><PERSON>_transform_dt()\n", "        \n", "        # Record initial dynamic state\n", "        self.dt_raw.append(self.lr)\n", "        self.zeta_dt_raw.append(self.zeta_dt)\n", "        self.batch_size_raw.append(self.batch_size)\n", "        self.zeta_batch_raw.append(self.zeta_batch)\n", "        self.ess_raw.append(np.nan)\n", "        tkin_init, tconf_init = self.get_current_temps()\n", "        self.tkin_raw.append(tkin_init)\n", "        self.tconf_raw.append(tconf_init)\n", "\n", "        for epoch in range(1, self.epochs + 1):\n", "            self.model.train()\n", "            \n", "            shuffled_indices = torch.randperm(self.datasize)\n", "            current_index = 0\n", "            \n", "            while current_index < self.datasize:\n", "                \n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "                self.a = self.a_gamma**(self.lr)\n", "                self.sqrt_aT = torch.sqrt((1 - self.a**2) * self.T)\n", "                \n", "                self.update_params_BAOA()\n", "\n", "                if epoch > self.burn_in_epochs:\n", "                    self.adapt_batch_size()\n", "                \n", "                # Store every batch size used for final analysis\n", "                self.all_batch_sizes_in_training.append(self.batch_size)\n", "                \n", "                batch_end_index = min(current_index + self.batch_size, self.datasize)\n", "                batch_indices = shuffled_indices[current_index:batch_end_index]\n", "                \n", "                data = self.train_dataset.data[batch_indices]\n", "                target = self.train_dataset.targets[batch_indices]\n", "\n", "                if self.train_dataset.transform:\n", "                    data_transformed = []\n", "                    for img_tensor in data:\n", "                        pil_img = Image.fromarray(img_tensor.numpy(), mode='L')\n", "                        data_transformed.append(self.train_dataset.transform(pil_img))\n", "                    data = torch.stack(data_transformed)\n", "\n", "                current_index = batch_end_index\n", "                \n", "                data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)\n", "                self.fill_gradients(data, target, squeeze)\n", "                \n", "                self.update_params_B()\n", "                \n", "                self.set_gradnorm()\n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "\n", "                self.loss_history_for_ess.append(self.running_loss.item() / self.datasize)\n", "\n", "            if epoch % self.meas_freq == 0:\n", "                eval_metrics = evaluate(self.model, consistent_eval_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "                results_eval.append(eval_metrics)\n", "                \n", "                tkin_now, tconf_now = self.get_current_temps()\n", "                self.tkin_raw.append(tkin_now)\n", "                self.tconf_raw.append(tconf_now)\n", "                \n", "                self.dt_raw.append(self.lr)\n", "                self.zeta_dt_raw.append(self.zeta_dt)\n", "                self.batch_size_raw.append(self.batch_size)\n", "                self.zeta_batch_raw.append(self.zeta_batch)\n", "                ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "                self.ess_raw.append(ess_value)\n", "\n", "                print(f\"ZBAOABZ EPOCH {epoch} DONE! | Test Accu: {eval_metrics[2]:.2f}% | dt: {self.lr.item():.6f} | Batch: {self.batch_size}\")\n", "\n", "        end_time = time.time()\n", "        print(f\"Training took {end_time-start_time:.2f} seconds\")\n", "        \n", "        # --- NEW: Calculate and print mean batch sizes ---\n", "        all_batches = np.array(self.all_batch_sizes_in_training)\n", "        if len(all_batches) > 0:\n", "            steps_per_epoch = len(all_batches) / self.epochs\n", "            burn_in_idx = int(self.burn_in_epochs * steps_per_epoch)\n", "            \n", "            mean_batch_overall = np.mean(all_batches)\n", "            mean_batch_post_burn = np.mean(all_batches[burn_in_idx:]) if burn_in_idx < len(all_batches) else np.nan\n", "            \n", "            print(f\"\\n--- Adaptive Batch Size Stats ---\")\n", "            print(f\"Mean batch size (overall): {mean_batch_overall:.2f}\")\n", "            print(f\"Mean batch size (post-burn-in): {mean_batch_post_burn:.2f}\")\n", "            print(f\"---------------------------------\")\n", "\n", "\n", "        results_eval = np.array(results_eval)\n", "        dt_np = np.array([i.cpu().item() for i in self.dt_raw])\n", "        zeta_dt_np = np.array([i.cpu().item() for i in self.zeta_dt_raw])\n", "        batch_size_np = np.array(self.batch_size_raw)\n", "        zeta_batch_np = np.array(self.zeta_batch_raw)\n", "        ess_np = np.array(self.ess_raw)\n", "        tkin_np = np.array(self.tkin_raw)\n", "        tconf_np = np.array(self.tconf_raw)\n", "        \n", "        epoch_axis = np.arange(0, len(results_eval[:,0])) * self.meas_freq\n", "        \n", "        results = np.column_stack((epoch_axis, \n", "                                   results_eval[:,:3],\n", "                                   tkin_np, tconf_np,\n", "                                   results_eval[:,3:],\n", "                                   ess_np, dt_np, zeta_dt_np, batch_size_np, zeta_batch_np))\n", "\n", "        return results, self.results_header\n", "\n", "    def update_params_BAOA(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "            eps = torch.randn_like(p.buf)\n", "            p.buf.mul_(self.a)\n", "            p.buf.add_(eps, alpha=self.sqrt_aT)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "\n", "    def update_params_B(self):\n", "        for p in self.model.parameters():\n", "            if p.grad is None: continue\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "\n", "    def fill_gradients(self, data, target, squeeze):\n", "        self.model.zero_grad()\n", "        output = self.model(data)\n", "        if squeeze: output = output.squeeze()\n", "        self.running_loss = self.criterion(output, target) * self.datasize\n", "        self.running_loss.backward()\n", "        for p in self.model.parameters():\n", "            if p.grad is not None:\n", "                p.grad.data.add_(p.data, alpha=self.weight_decay)\n", "\n", "def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "    \"\"\"\n", "    Returns a list containing:\n", "    [train loss, train accuracy, test accuracy, NLL, Brier Score, ECE, OOD AUC]\n", "    \"\"\"\n", "    model.eval()\n", "    all_test_preds, all_test_labels, all_ood_preds = [], [], []\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            probs = torch.exp(output)\n", "            all_test_preds.append(probs)\n", "            all_test_labels.append(target)\n", "        all_test_preds = torch.cat(all_test_preds, dim=0)\n", "        all_test_labels = torch.cat(all_test_labels, dim=0)\n", "        for data, _ in ood_loader:\n", "            data = data.to(device, non_blocking=True)\n", "            output = model(data)\n", "            all_ood_preds.append(torch.exp(output))\n", "        all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "    \n", "    nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "    brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "    ece = calculate_ece(all_test_preds, all_test_labels)\n", "    test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "    entropy_id = get_predictive_entropy(all_test_preds)\n", "    entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "    entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "    ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "    correct, loss = 0, 0\n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "    loss /= len(train_loader)\n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "    \n", "    return [loss, train_accu, test_accu, nll, brier, ece, ood_auc]\n", "\n", "# ... (The rest of your code: helper functions, model definition, parameters, and training script can remain the same) ...\n", "\n", "def calculate_ess(chain):\n", "    if len(chain) < 10: return np.nan\n", "    try:\n", "        return az.ess(chain).item()\n", "    except:\n", "        return np.nan\n", "\n", "def calculate_ece(preds, labels, n_bins=15):\n", "    bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "    bin_lowers = bin_boundaries[:-1]\n", "    bin_uppers = bin_boundaries[1:]\n", "    confidences, predictions = torch.max(preds, 1)\n", "    accuracies = predictions.eq(labels)\n", "    ece = torch.zeros(1, device=preds.device)\n", "    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "        in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "        prop_in_bin = in_bin.float().mean()\n", "        if prop_in_bin.item() > 0:\n", "            accuracy_in_bin = accuracies[in_bin].float().mean()\n", "            avg_confidence_in_bin = confidences[in_bin].mean()\n", "            ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "    return ece.item()\n", "\n", "def calculate_brier_score(preds, labels):\n", "    one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "    return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "def get_predictive_entropy(preds):\n", "    return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "        self.fc_input_size = 128 * 3 * 3\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = F.log_softmax(x, dim=1)\n", "        return x\n", "\n", "#%% MAIN SCRIPT\n", "if __name__ == '__main__':\n", "    epochs = 60\n", "    initial_B_train = 10000\n", "    min_B_train = 512\n", "    max_B_train = 30000\n", "    seed = 2\n", "    meas_freq = 5\n", "    gamma = 1\n", "    temperature = 1\n", "    weight_decay = 1e-5\n", "    \n", "    # --- HYPERPARAMETER FIX: Make dt controller less sensitive to noise ---\n", "    alpha_dt = 1.0 # Reduced from 50 to give the controller more memory\n", "    scale_g = 1/60000\n", "    dtau = 4e-4\n", "    # --- HYPERPARAMETER FIX: Make dt controller more conservative ---\n", "    m_dt = 0.1\n", "    M_dt = 1.0 # Reduced from 10 to 1.0 to prevent instability\n", "\n", "    # --- Burn-in is now in epochs ---\n", "    burn_in_epochs = 3 \n", "    alpha_batch = 0.05\n", "    threshold_batch = 0.01\n", "    steepness_batch = 1.0\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "\n", "    transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.5,), (0.5,))])\n", "    train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "    test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "    ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "    \n", "    torch.manual_seed(seed)\n", "    model = SimpleCNN().to(device)\n", "\n", "    # Create test/ood loaders with the consistent initial_B_train\n", "    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=initial_B_train, shuffle=False)\n", "    ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=initial_B_train, shuffle=False)\n", "\n", "    sampler = ZBAOABZ(\n", "        model, train_dataset, test_loader, ood_loader, nn.NLLLoss(reduction=\"mean\"),\n", "        dtau=dtau, weight_decay=weight_decay, gamma=gamma, temperature=temperature, \n", "        epochs=epochs, device=device, meas_freq=meas_freq,\n", "        alpha_dt=alpha_dt, scale_g=scale_g, m_dt=m_dt, M_dt=M_dt,\n", "        burn_in_epochs=burn_in_epochs, initial_batch_size=initial_B_train,\n", "        min_batch_size=min_B_train, max_batch_size=max_B_train,\n", "        alpha_batch=alpha_batch, threshold_batch=threshold_batch,\n", "        steepness_batch=steepness_batch\n", "    )\n", "    \n", "    results, header = sampler.train()\n", "    results_dict = {h: results[:, i] for i, h in enumerate(header)}\n", "\n", "    fig, ax = plt.subplots(3, 2, figsize=(14, 12))\n", "    ax = ax.ravel()\n", "    ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Train Loss\"], marker='o')\n", "    ax[0].set_title(\"Training Loss\")\n", "    ax[0].set_ylabel(\"NLL Loss\")\n", "    ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "    ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "    ax[1].set_title(\"Train and Test Accuracy\")\n", "    ax[1].set_ylabel(\"Accuracy (%)\")\n", "    ax[1].legend()\n", "    ax[2].plot(results_dict[\"Epoch\"], results_dict[\"dt\"], marker='o', color='purple')\n", "    ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "    ax[2].set_ylabel(\"Stepsize\")\n", "    ax[3].plot(results_dict[\"Epoch\"], results_dict[\"Batch Size\"], marker='o', color='green')\n", "    ax[3].set_title(\"Adaptive Batch Size\")\n", "    ax[3].set_ylabel(\"Batch Size\")\n", "    ax[3].axhline(min_B_train, color='k', linestyle=':', lw=1)\n", "    ax[3].axhline(max_B_train, color='k', linestyle=':', lw=1)\n", "    ax[4].plot(results_dict[\"Epoch\"], results_dict[\"Tconf\"], marker='o', color='red')\n", "    ax[4].axhline(temperature, color='k', linestyle='--')\n", "    ax[4].set_title(\"Configurational Temperature\")\n", "    ax[4].set_ylabel(\"T_conf\")\n", "    ax[5].plot(results_dict[\"Epoch\"], results_dict[\"zeta_batch\"], marker='o', color='orange')\n", "    ax[5].axhline(0, color='k', linestyle='--')\n", "    ax[5].set_title(\"Batch Size Auxiliary Variable (zeta_batch)\")\n", "    ax[5].set_ylabel(\"zeta_batch\")\n", "    for a in ax:\n", "        a.set_xlabel(\"Epochs\")\n", "        a.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%% Imports\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import time\n", "import arviz as az\n", "import math\n", "\n", "import torchvision\n", "from torchvision import datasets, transforms\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_auc_score\n", "\n", "#%% ZBAOABZ with Adaptive Batch Size\n", "\n", "class ZBAOABZ(nn.Module):\n", "    \"\"\"\n", "    ZBAOABZ Sampler with two adaptive controllers:\n", "    1. <PERSON><PERSON><PERSON><PERSON> for step-size (dt) based on gradient norm.\n", "    2. Adaptive Bat<PERSON> Si<PERSON> (B) based on configurational temperature.\n", "    \"\"\"\n", "\n", "    def __init__(self, model, train_dataset, test_loader, ood_loader, criterion, \n", "                 # Sam<PERSON> params\n", "                 dtau, weight_decay, gamma, temperature, epochs, device, meas_freq,\n", "                 # dt control params\n", "                 alpha_dt, scale_g_dt, m_dt, M_dt,\n", "                 # --- NEW: Batch size control params ---\n", "                 burn_in_steps, initial_batch_size, min_batch_size, max_batch_size,\n", "                 alpha_batch, threshold_batch, steepness_batch,\n", "                 # Schedules\n", "                 lr_schedule=None, T_schedule=None):\n", "        super(ZBAOABZ, self).__init__()\n", "        \n", "        # Core components\n", "        self.model = model\n", "        self.train_dataset = train_dataset # Store dataset, not loader\n", "        self.test_loader = test_loader\n", "        self.ood_loader = ood_loader\n", "        self.criterion = criterion\n", "        self.device = device\n", "        self.epochs = epochs\n", "        self.meas_freq = meas_freq\n", "        self.weight_decay = weight_decay  # weight decay for L2 regularization\n", "        self.gamma = gamma\n", "        self.T = temperature\n", "        \n", "        # --- State variables for dt control ---\n", "        self.dtau = dtau\n", "        self.alpha_dt = alpha_dt\n", "        self.scale_g_dt = scale_g_dt # This is Omega_inv in the paper\n", "        self.r_dt=0.25\n", "        self.m_dt = m_dt\n", "        self.M_dt = M_dt\n", "        self.lr = None # dt\n", "        self.zeta_dt = None\n", "        self.gradnorm = None\n", "        self.exptau_half_dt = np.exp(-0.5 * self.alpha_dt * self.dtau)\n", "        self.alpha_inv_dt = 1 / self.alpha_dt if self.alpha_dt > 0 else 0\n", "\n", "        # --- NEW: State variables for batch size control ---\n", "        self.burn_in_steps = burn_in_steps\n", "        self.min_batch_size = min_batch_size\n", "        self.max_batch_size = max_batch_size\n", "        self.alpha_batch = alpha_batch\n", "        self.threshold_batch = threshold_batch\n", "        self.steepness_batch = steepness_batch\n", "        \n", "        self.batch_size = initial_batch_size\n", "        self.zeta_batch = 0.0 # Start auxiliary variable at neutral\n", "        # Calculate initial sigma from initial batch size\n", "        self.datasize = len(self.train_dataset)\n", "        self.sigma = (self.datasize - self.batch_size) / self.batch_size\n", "        # Pre-calculate sigma bounds from batch size bounds\n", "        self.sigma_min = (self.datasize - self.max_batch_size) / self.max_batch_size\n", "        self.sigma_max = (self.datasize - self.min_batch_size) / self.min_batch_size\n", "        \n", "        # --- Pre-calculated constants ---\n", "        self.a_gamma = np.exp(-self.gamma)  # exp(-γ)\n", "        self.a = None\n", "        self.sqrt_aT = None\n", "        \n", "        # --- Schedules ---\n", "        self.lr_schedule = lr_schedule\n", "        self.T_schedule = T_schedule\n", "        self.T_idx = 0\n", "        \n", "        # --- Metrics Storage ---\n", "        self.dt_raw = []\n", "        self.zeta_dt_raw = []\n", "        self.batch_size_raw = []\n", "        self.zeta_batch_raw = []\n", "        self.ess_raw = []\n", "        self.loss_history_for_ess = []\n", "        \n", "        self.results_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \n", "                               \"Tkin\", \"Tconf\", \"NLL\", \"Brier\", \"ECE\", \n", "                               \"OOD AUC\", \"ESS\", \"dt\", \"zeta_dt\", \"Batch Size\", \"zeta_batch\"]\n", "\n", "    # --- NEW: Methods for Adaptive Batch Size Control ---\n", "\n", "    def monitor_g_batch(self, T_conf):\n", "        \"\"\" Two-way linear monitor for temperature gap. \"\"\"\n", "        gap = np.abs(T_conf - self.T) / self.T\n", "        return gap - self.threshold_batch\n", "\n", "    def psi_batch(self):\n", "        \"\"\" Arctan mapping from zeta_batch to sigma. \"\"\"\n", "        midpoint = (self.sigma_min + self.sigma_max) / 2.0\n", "        sigma_range = self.sigma_max - self.sigma_min\n", "        atan_val = np.arctan(self.steepness_batch * self.zeta_batch)\n", "        # Use a minus sign to make it a decreasing function\n", "        return midpoint - (sigma_range / np.pi) * atan_val\n", "\n", "    def zeta_step_batch(self, g_val):\n", "        \"\"\" Update rule for the batch size auxiliary variable. \"\"\"\n", "        # The effective time step for this update is dtau, as it happens once per ZBAOABZ step\n", "        rho = np.exp(-self.alpha_batch * self.dtau)\n", "        self.zeta_batch = rho * self.zeta_batch + (1 - rho) / self.alpha_batch * g_val\n", "\n", "    def get_current_Tconf(self):\n", "        \"\"\" Calculates the instantaneous configurational temperature. \"\"\"\n", "        Tconf = 0\n", "        param_count = 0\n", "        with torch.no_grad():\n", "            for param in self.model.parameters():\n", "                if hasattr(param, 'buf') and param.grad is not None:\n", "                    Tconf += (param.data * param.grad.data).sum().item()\n", "                    param_count += param.numel()\n", "        return Tconf / param_count if param_count > 0 else 0\n", "        \n", "    def adapt_batch_size(self):\n", "        \"\"\"\n", "        Orchestrates the batch size adaptation. This is called once per ZBAOABZ step.\n", "        \"\"\"\n", "        # 1. Get current T_conf (lagging indicator from previous step's gradient)\n", "        T_conf = self.get_current_Tconf()\n", "        \n", "        # 2. Get monitor value\n", "        g_val = self.monitor_g_batch(T_conf)\n", "        \n", "        # 3. Update auxiliary variable\n", "        self.zeta_step_batch(g_val)\n", "        \n", "        # 4. Map to new sigma\n", "        new_sigma = self.psi_batch()\n", "        self.sigma = np.clip(new_sigma, self.sigma_min, self.sigma_max)\n", "        \n", "        # 5. Convert sigma to new batch size B\n", "        new_B_float = self.datasize / (self.sigma + 1)\n", "        self.batch_size = int(np.round(np.clip(new_B_float, self.min_batch_size, self.max_batch_size)))\n", "\n", "    # --- Methods for Adaptive Step Size (dt) Control ---\n", "    \n", "    def set_gradnorm(self):\n", "        self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters())\n", "        self.gradnorm *= self.scale_g_dt\n", "\n", "    def Z_step_dt(self):\n", "        self.zeta_dt = self.exptau_half_dt * self.zeta_dt + self.alpha_inv_dt * (1 - self.exptau_half_dt) * self.gradnorm\n", "\n", "    def Sundman_transform_dt(self):\n", "        zeta_r = self.zeta_dt**self.r_dt # r=0.25\n", "        self.lr = self.dtau * self.m_dt * (zeta_r + self.M_dt) / (zeta_r + self.m_dt)\n", "\n", "    # --- Core Training and Integrator Logic ---\n", "\n", "    def train(self):\n", "        squeeze = isinstance(self.criterion, torch.nn.modules.loss.BCELoss)\n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\")\n", "\n", "        # Create momentum buffers\n", "        for p in self.model.parameters():\n", "            p.buf = torch.normal(torch.zeros_like(p), np.sqrt(0.5 * self.T)).to(self.device)\n", "\n", "        # Create a single data loader with the MAX batch size\n", "        # We will subsample from this loader in the loop\n", "        train_loader = torch.utils.data.DataLoader(\n", "            self.train_dataset, batch_size=self.max_batch_size, shuffle=True, \n", "            num_workers=10, pin_memory=True\n", "        )\n", "        \n", "        # Initial gradients\n", "        (data, target) = next(iter(train_loader))\n", "        self.fill_gradients(data, target, squeeze)\n", "        \n", "        results = []\n", "        initial_metrics = evaluate(self.model, train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "        results.append(initial_metrics)\n", "        \n", "        start_time = time.time()\n", "        print(\"Starting ZBAOABZ sampling with adaptive batch size...\")\n", "        \n", "        # Initial dt and zeta_dt\n", "        self.set_gradnorm()\n", "        self.zeta_dt = self.gradnorm\n", "        self.<PERSON><PERSON>_transform_dt()\n", "        \n", "        self.dt_raw.append(self.lr)\n", "        self.zeta_dt_raw.append(self.zeta_dt)\n", "        self.batch_size_raw.append(self.batch_size)\n", "        self.zeta_batch_raw.append(self.zeta_batch)\n", "        self.ess_raw.append(np.nan)\n", "\n", "        step_counter = 0\n", "        for epoch in range(1, self.epochs + 1):\n", "            self.model.train()\n", "            \n", "            # adjust stepsize and temperature (optional)\n", "            if self.lr_schedule is not None and epoch % self.lr_schedule[0]==0:\n", "                self.dtau *= self.lr_schedule[1]\n", "\n", "            if self.T_schedule is not None and epoch % self.T_schedule[self.T_idx][0]==0:\n", "                new_T = self.T_schedule[self.T_idx][1]\n", "                print(\"Adjusting temperature to \", new_T)\n", "                self.change_temperature(new_T)\n", "                self.T_idx += 1\n", "                if self.T_idx == len(self.T_schedule):\n", "                    self.T_schedule = None\n", "\n", "            # This loop now iterates over large batches\n", "            for batch_idx, (large_data, large_target) in enumerate(train_loader):\n", "                step_counter += 1\n", "                \n", "                # --- ZBAOABZ Step ---\n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "                self.a = self.a_gamma**(self.lr)\n", "                self.sqrt_aT = torch.sqrt((1 - self.a**2) * self.T)\n", "                \n", "                self.update_params_BAOA()\n", "\n", "                # --- ADAPTIVE BATCH SIZE LOGIC ---\n", "                if step_counter > self.burn_in_steps:\n", "                    self.adapt_batch_size()\n", "                \n", "                # Subsample from the large batch to the adaptively chosen size\n", "                indices = torch.randperm(large_data.size(0))[:self.batch_size]\n", "                data = large_data[indices]\n", "                target = large_target[indices]\n", "                \n", "                data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)\n", "                self.fill_gradients(data, target, squeeze)\n", "                \n", "                self.update_params_B()\n", "                \n", "                self.set_gradnorm()\n", "                self.Z_step_dt()\n", "                self.<PERSON><PERSON>_transform_dt()\n", "\n", "                self.loss_history_for_ess.append(self.running_loss.item() / self.datasize)\n", "\n", "            if epoch % self.meas_freq == 0:\n", "                metrics = evaluate(self.model, train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "                results.append(metrics)\n", "                \n", "                self.dt_raw.append(self.lr)\n", "                self.zeta_dt_raw.append(self.zeta_dt)\n", "                self.batch_size_raw.append(self.batch_size)\n", "                self.zeta_batch_raw.append(self.zeta_batch)\n", "                ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "                self.ess_raw.append(ess_value)\n", "\n", "                print(f\"ZBAOABZ EPOCH {epoch} DONE! | Test Accu: {results[-1][2]:.2f}% | dt: {self.lr.item():.6f} | Batch: {self.batch_size}\")\n", "\n", "        end_time = time.time()\n", "        print(f\"Training took {end_time-start_time:.2f} seconds\")\n", "        \n", "        results = np.array(results)\n", "        dt_np = np.array([i.cpu().item() for i in self.dt_raw])\n", "        zeta_dt_np = np.array([i.cpu().item() for i in self.zeta_dt_raw])\n", "        batch_size_np = np.array(self.batch_size_raw)\n", "        zeta_batch_np = np.array(self.zeta_batch_raw)\n", "        ess_np = np.array(self.ess_raw)\n", "        \n", "        epoch_axis = np.arange(0, len(results[:,0])) * self.meas_freq\n", "        results = np.column_stack((epoch_axis, results, ess_np, dt_np, zeta_dt_np, batch_size_np, zeta_batch_np))\n", "\n", "        return results, self.results_header\n", "\n", "    def update_params_BAOA(self):\n", "        for p in self.model.parameters():\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "            eps = torch.randn_like(p.buf)\n", "            p.buf.mul_(self.a)\n", "            p.buf.add_(eps, alpha=self.sqrt_aT)\n", "            p.data.add_(p.buf, alpha=0.5 * self.lr)\n", "\n", "    def update_params_B(self):\n", "        for p in self.model.parameters():\n", "            p.buf.add_(p.grad.data, alpha=-0.5 * self.lr)\n", "\n", "    def fill_gradients(self, data, target, squeeze):\n", "        self.model.zero_grad()\n", "        output = self.model(data)\n", "        if squeeze: output = output.squeeze()\n", "        # SGLD-style gradient scaling\n", "        self.running_loss = self.criterion(output, target) * self.datasize\n", "        self.running_loss.backward()\n", "        for p in self.model.parameters():\n", "            if p.grad is not None:\n", "                p.grad.data.add_(p.data, alpha=self.weight_decay)\n", "\n", "    def change_temperature(self, T):\n", "        \"\"\"\n", "        Changes the temperature of the sampler.\n", "        \"\"\"\n", "        self.T = T\n", "\n", "# ... (The rest of your code: helper functions, model definition, parameters, and training script can remain largely the same) ...\n", "# ... I will just update the main script part to include the new parameters.\n", "\n", "#%% Helper functions, Model, etc. (No changes needed here)\n", "# ... (calculate_ess, calculate_ece, etc. are all fine)\n", "# ... (SimpleCNN class is fine)\n", "#%% Helper functions for metrics\n", "# Expected Calibration Error (ECE): Measures how well-calibrated the model's confidence scores are.\n", "\n", "# Negative Log-Likelihood (NLL) & Brier Score: Proper scoring rules that assess the quality of the predicted probability distributions.\n", "\n", "# OOD AUC: Evaluates the model's ability to distinguish between in-distribution (MNIST) and out-of-distribution (Fashion-MNIST) data \n", "# based on predictive uncertainty (entropy). An AUC of 1.0 would be perfect.\n", "\n", "# Effective Sample Size (ESS): Calculated for the training loss to measure the sampling efficiency. \n", "# A higher ESS indicates less correlation between samples and more efficient exploration.\n", "\n", "def calculate_ess(chain):\n", "    \"\"\"\n", "    Calculate Effective Sample Size (ESS) for a chain.\n", "    \"\"\"\n", "    if len(chain) < 10: # ESS is unreliable for very short chains\n", "        return np.nan\n", "    return az.ess(chain).item()\n", "    \n", "def calculate_ece(preds, labels, n_bins=15):\n", "    \"\"\"\n", "    Calculate Expected Calibration Error.\n", "    ECE=\\\\sum_{b=1}^B\\\\frac{n_b}{N}|acc(b)-conf(b)|\n", "    b: b-th bin; n_b: number of samples in b-th bin; N: total number of samples\n", "    acc(b): accuracy of samples in b-th bin; conf(b): confidence of samples in b-th bin\n", "    \"\"\"\n", "    bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "    bin_lowers = bin_boundaries[:-1]\n", "    bin_uppers = bin_boundaries[1:]\n", "\n", "    confidences, predictions = torch.max(preds, 1)\n", "    accuracies = predictions.eq(labels)\n", "\n", "    ece = torch.zeros(1, device=preds.device)\n", "    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "        in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "        prop_in_bin = in_bin.float().mean()\n", "        if prop_in_bin.item() > 0:\n", "            accuracy_in_bin = accuracies[in_bin].float().mean()\n", "            avg_confidence_in_bin = confidences[in_bin].mean()\n", "            ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "    return ece.item()\n", "\n", "def calculate_brier_score(preds, labels):\n", "    \"\"\"\n", "    Calculate Brier Score.\n", "    BS=\\\\frac{1}{N}\\\\sum_{t=1}^N\\\\sum_{i=1}^R\\\\left(f_{ti}-o_{ti}\\\\right)^2\n", "    N: number of samples; R: number of classes\n", "    f_{ti}: predicted probability of t-th sample being in i-th class\n", "    o_{ti}: one-hot label of t-th sample being in i-th class (predicted label)\n", "    \"\"\"\n", "    one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "    return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "def get_predictive_entropy(preds):\n", "    \"\"\"\n", "    Calculate predictive entropy for given predictions.\n", "    H(p)=-\\\\sum_{i=1}^R p_i * log(p_i)\n", "    \"\"\"\n", "    return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "\n", "#%% external evaluate function\n", "\n", "def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "    \"\"\"\n", "    Returns a list containing:\n", "    [train loss, train accuracy, test accuracy, Tkin, Tconf, NLL, Brier Score, ECE, OOD AUC]\n", "    \"\"\"\n", "    model.eval()\n", "    \n", "    all_test_preds = []\n", "    all_test_labels = []\n", "    all_ood_preds = []\n", "\n", "    # --- In-Distribution (Test Set) Evaluation ---\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            probs = torch.exp(output) # Convert log-probabilities to probabilities\n", "            all_test_preds.append(probs)\n", "            all_test_labels.append(target)\n", "    \n", "    all_test_preds = torch.cat(all_test_preds, dim=0)\n", "    all_test_labels = torch.cat(all_test_labels, dim=0)\n", "\n", "    # --- Out-of-Distribution (OOD Set) Evaluation ---\n", "    with torch.no_grad():\n", "        for data, _ in ood_loader:\n", "            data = data.to(device, non_blocking=True)\n", "            output = model(data)\n", "            all_ood_preds.append(torch.exp(output))\n", "    \n", "    all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "\n", "    # --- Calculate Metrics ---\n", "    # Test Metrics\n", "    nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "    brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "    ece = calculate_ece(all_test_preds, all_test_labels)\n", "    test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "    # OOD AUC\n", "    entropy_id = get_predictive_entropy(all_test_preds)\n", "    entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    \n", "    ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "    entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "    ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "    # --- Train Loss and Accuracy ---\n", "    correct = 0\n", "    loss = 0\n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "    loss /= len(train_loader)\n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "\n", "    # --- Temperatures ---\n", "    Tkin = 0\n", "    Tconf = 0\n", "    param_count = 0\n", "    with torch.no_grad():\n", "        for param in model.parameters():\n", "            if hasattr(param, 'buf'):\n", "                Tkin += (param.buf**2).sum().item()\n", "                Tconf += (param.data * param.grad.data).sum().item()\n", "                param_count += param.numel()\n", "    \n", "    if param_count > 0:\n", "        Tkin /= param_count\n", "        Tconf /= param_count\n", "    \n", "    return [loss, train_accu, test_accu, <PERSON><PERSON>, Tconf, nll, brier, ece, ood_auc]\n", "\n", "#%% Model \n", "\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "\n", "        self.fc_input_size = 128 * 3 * 3 # Based on MNIST image dimension after pooling\n", "\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = F.log_softmax(x, dim=1)\n", "        return x\n", "\n", "#%% MAIN SCRIPT\n", "if __name__ == '__main__':\n", "    # --- Parameters ---\n", "    epochs = 60\n", "    # B_train is now controlled adaptively, we set the initial, min, and max\n", "    initial_B_train = 10000\n", "    min_B_train = 512\n", "    max_B_train = 10000\n", "    \n", "    seed = 2\n", "    meas_freq = 5\n", "    gamma = 1\n", "    temperature = 1\n", "    weight_decay = 1e-5\n", "    \n", "    # dt control params\n", "    alpha_dt = 50\n", "    scale_g_dt = 1/60000 # Omega_inv\n", "    dtau = 4e-4\n", "    m_dt = 0.1\n", "    M_dt = 10\n", "\n", "    # --- NEW: Batch size control params ---\n", "    burn_in_steps = 500 # Start adapting after this many steps (batches)\n", "    alpha_batch = 0.05\n", "    threshold_batch = 0.01 # Target 1% absolute temperature gap\n", "    steepness_batch = 1.0  # Gain for the atan controller\n", "\n", "    # --- Setup ---\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "\n", "    transform = transforms.Compose([\n", "        transforms.To<PERSON><PERSON><PERSON>(), \n", "        transforms.Normalize((0.5,), (0.5,))\n", "    ])\n", "\n", "    train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "    test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "    ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "    \n", "    # --- Run Training ---\n", "    torch.manual_seed(seed)\n", "    model = SimpleCNN().to(device)\n", "\n", "    # Note: We pass the DATASET to the sampler now, not the loader\n", "    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=max_B_train, shuffle=False)\n", "    ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=max_B_train, shuffle=False)\n", "\n", "    sampler = ZBAOABZ(\n", "        model, train_dataset, test_loader, ood_loader, nn.NLLLoss(reduction=\"mean\"),\n", "        dtau=dtau, weight_decay=weight_decay, gamma=gamma, temperature=temperature, \n", "        epochs=epochs, device=device, meas_freq=meas_freq,\n", "        alpha_dt=alpha_dt, scale_g_dt=scale_g_dt, m_dt=m_dt, M_dt=M_dt,\n", "        burn_in_steps=burn_in_steps, initial_batch_size=initial_B_train,\n", "        min_batch_size=min_B_train, max_batch_size=max_B_train,\n", "        alpha_batch=alpha_batch, threshold_batch=threshold_batch,\n", "        steepness_batch=steepness_batch\n", "    )\n", "    \n", "    results, header = sampler.train()\n", "    results_dict = {h: results[:, i] for i, h in enumerate(header)}\n", "\n", "    #%% Plotting (with new metrics)\n", "    fig, ax = plt.subplots(3, 2, figsize=(14, 12))\n", "    ax = ax.ravel()\n", "\n", "    ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Train Loss\"], marker='o')\n", "    ax[0].set_title(\"Training Loss\")\n", "    ax[0].set_ylabel(\"NLL Loss\")\n", "\n", "    ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "    ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "    ax[1].set_title(\"Train and Test Accuracy\")\n", "    ax[1].set_ylabel(\"Accuracy (%)\")\n", "    ax[1].legend()\n", "\n", "    ax[2].plot(results_dict[\"Epoch\"], results_dict[\"dt\"], marker='o', color='purple')\n", "    ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "    ax[2].set_ylabel(\"Stepsize\")\n", "\n", "    ax[3].plot(results_dict[\"Epoch\"], results_dict[\"Batch Size\"], marker='o', color='green')\n", "    ax[3].set_title(\"Adaptive Batch Size\")\n", "    ax[3].set_ylabel(\"Batch Size\")\n", "    ax[3].axhline(min_B_train, color='k', linestyle=':', lw=1)\n", "    ax[3].axhline(max_B_train, color='k', linestyle=':', lw=1)\n", "\n", "\n", "    ax[4].plot(results_dict[\"Epoch\"], results_dict[\"Tconf\"], marker='o', color='red')\n", "    ax[4].axhline(temperature, color='k', linestyle='--')\n", "    ax[4].set_title(\"Configurational Temperature\")\n", "    ax[4].set_ylabel(\"T_conf\")\n", "\n", "    ax[5].plot(results_dict[\"Epoch\"], results_dict[\"zeta_batch\"], marker='o', color='orange')\n", "    ax[5].axhline(0, color='k', linestyle='--')\n", "    ax[5].set_title(\"Batch Size Auxiliary Variable (zeta_batch)\")\n", "    ax[5].set_ylabel(\"zeta_batch\")\n", "\n", "    for a in ax:\n", "        a.set_xlabel(\"Epochs\")\n", "        a.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}