{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# two dimension example\n", "\n", "## $U_{\\mathrm{channel}}(x,y)=\\frac{y^2}{1+10x^4}+0.001(x^2-9)^2$"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import ot\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "def plot_traj(V_arr, burn_in, ax):\n", "    if V_arr.ndim == 2:            # single chain: shape (K, 2)\n", "        xy = V_arr[burn_in:].cpu().numpy()\n", "        ax.plot(xy[:, 0], xy[:, 1])\n", "        ax.scatter(xy[0, 0],  xy[0, 1],  marker='o', label='start')\n", "        ax.scatter(xy[-1, 0], xy[-1, 1], marker='x', label='end')\n", "\n", "        ax.set_xlabel('x')\n", "        ax.set_ylabel('y')\n", "\n", "    else:                          # many chains: shape (par_runs, K, 2)\n", "        print('Please plotting chains by yourself.')\n", "        # for run in range(V_arr.shape[0]):\n", "        #     xy = V_arr[run, burn_in:].cpu().numpy()\n", "        #     ax.plot(xy[:, 0], xy[:, 1], alpha=0.3)   # semi-transparent lines\n", "    \n", "def plot_cumulative_mean_potential(U_arr, h, burn_in, ax):\n", "    K_total = U_arr.shape[-1]\n", "    if U_arr.ndim == 1:            # single chain: shape (K, 2)\n", "        # U_arr has shape (K,)  –> (K_after, )\n", "        U_after = U_arr[burn_in:]\n", "    else:\n", "        # U_arr has shape (par_runs, K)\n", "        # First average across chains (axis 0) to get a single curve in time\n", "        U_after = U_arr[:, burn_in:].mean(dim=0)        # shape (K_after,)\n", "\n", "    steps_after = U_after.shape[0]       # K_after\n", "    cum_sum     = torch.cumsum(U_after, dim=0)   # shape (K_after,)\n", "    cum_mean    = cum_sum / torch.arange(1, steps_after+1, device=device, dtype=torch.float32)   # shape (K_after,)\n", "\n", "    h_val    = float(h)      # from the sampler call\n", "    t_axis   = h_val * torch.arange(burn_in + 1, K_total + 1, device=device, dtype=torch.float32)   # shape (K_after,)\n", "\n", "    ax.plot(t_axis.cpu().numpy(), cum_mean.cpu().numpy())\n", "    ax.set_xlabel('Time')\n", "    ax.set_ylabel(r'$\\langle U \\rangle$')\n", "\n", "def plot_x_traj(V_arr, h, burn_in, ax):\n", "    h_val    = float(h)      # from the sampler call\n", "\n", "    if V_arr.ndim == 2:            # single chain: shape (K, 2)\n", "        x_vals = V_arr[burn_in:, 0].cpu().numpy()\n", "        t_axis = h_val * np.arange(burn_in, burn_in + x_vals.size)\n", "        ax.plot(t_axis, x_vals)\n", "        ax.set_xlabel('Time')\n", "        ax.set_ylabel('x')\n", "\n", "    else:                          # many chains: shape (par_runs, K, 2)\n", "        print('Please plotting chains by yourself.')\n", "\n", "def plots(V_arr, V1_arr, V2_arr, U_arr, U1_arr, U2_arr, h, burn_in):\n", "    fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "    # Plotting the trajectories\n", "    plot_traj(V_arr, burn_in, axes[0, 0])\n", "    plot_traj(V1_arr, burn_in, axes[0, 1])\n", "    plot_traj(V2_arr, burn_in, axes[0, 2])\n", "    # Plotting the cumulative mean potential\n", "    plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "    plot_cumulative_mean_potential(U1_arr, h, burn_in, axes[1, 1])\n", "    plot_cumulative_mean_potential(U2_arr, h, burn_in, axes[1, 2])\n", "    # Plotting the x-coordinate trajectory\n", "    plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "    plot_x_traj(V1_arr, h, burn_in, axes[2, 1])\n", "    plot_x_traj(V2_arr, h, burn_in, axes[2, 2])\n", "\n", "    # axes[0,0].set_title('BAOAB')\n", "    # axes[0,1].set_title('Without noise')\n", "    # axes[0,2].set_title('With noise')\n", "    # plt.tight_layout()\n", "    # plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BAOAB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for BAOAB\n", "@dataclass\n", "class BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "# constant parameters for BAOAB\n", "# def BAOAB_hconst(h,gam):\n", "#     with torch.no_grad():\n", "#         hh=copy.deepcopy(h).detach().double()\n", "#         gamm=copy.deepcopy(gam).detach().double()\n", "#         gh=gamm*hh\n", "#         eta=(torch.exp(-gh/2))\n", "#         xc1=hh/2*(1+eta)\n", "#         xc2=(hh*hh/4)*(1+eta)\n", "#         xc3=hh/2*torch.sqrt(-torch.expm1(-gh))\n", "#         vc1=eta*(hh/2)\n", "#         vc2=(hh/2)\n", "#         vc3=torch.sqrt(-torch.expm1(-gh))\n", "\n", "#         hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "#         return(hc)\n", "\n", "def BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --- global settings --------------------------------------------------\n", "no_batches = torch.tensor(2, device=device)  # number of batches\n", "batch_size = 1   \n", "par_runs = 1     # number of parallel chain runs\n", "\n", "# regularisation constant used in original code just to set gamma\n", "l2regconst = torch.tensor(25.0, device=device)\n", "gam        = torch.sqrt(l2regconst)              # damping parameter γ\n", "\n", "# toy example for 2 dimensions\n", "dim          = 2          # <— NEW\n", "# epsilon      = 0.1        # funnel strength ε\n", "idx_x        = 0          # column 0 = x\n", "idx_y        = 1          # column 1 = θ\n", "\n", "if par_runs > 1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # two coordinates now\n", "else:\n", "    p = torch.zeros(2, device=device)\n", "\n", "p.v = torch.randn_like(p, device=device)        # same trick as before"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace V and grad by the funnel versions\n", "def V_entropy(p2d: Tensor) -> Tensor:  # p2d shape: (N,2)\n", "    \"\"\"\n", "    Entropic Barrier 2D potential:\n", "        U(x, y) = y^2/(1+10x^4) + 0.001(x^2-9)^2.\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    return y.pow(2) / (1 + 10*x.pow(4)) + 0.001 * (x.pow(2) - 9).pow(2)\n", "\n", "def grad_entropy(p2d: Tensor) -> Tensor:\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = - 40*x^3*y^2/(1+10x^4)^2 + 0.004*x*(x^2-9)\n", "      dU/dy     = 2 * y / (1 + 10x^4)\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    grad_x = - 40*x.pow(3)*y.pow(2) / (1 + 10*x.pow(4)).pow(2) + 0.004*x*(x.pow(2)-9)\n", "    grad_y = 2 * y / (1 + 10*x.pow(4))\n", "    return torch.stack((grad_x, grad_y), dim=-1)   # shape (N,2)\n", "\n", "def grad(p2d: Tensor, batch_it):\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    \n", "    # # Convert batch_it to tensor if it's a numpy array\n", "    # if isinstance(batch_it, np.ndarray):\n", "    #     batch_it = torch.tensor(batch_it, device=device, dtype=torch.long)\n", "    \n", "    \n", "    # Initialize gradients\n", "    grad_x = torch.zeros_like(x)\n", "    grad_y = torch.zeros_like(y)\n", "    \n", "    if par_runs > 1:   \n", "        # Mask for batch indices\n", "        mask0 = (batch_it == 0)\n", "        mask1 = (batch_it == 1)\n", "        \n", "        # Compute gradients based on batch index\n", "        if mask0.any():\n", "            x0, y0  = x[mask0], y[mask0]\n", "            denom = 1 + 10*x0.pow(4)\n", "            grad_x0 = -40 * x0.pow(3) * y0.pow(2) / denom.pow(2)\n", "            grad_y0 = 2 * y0 / denom\n", "            grad_x[mask0] = grad_x0\n", "            grad_y[mask0] = grad_y0\n", "        \n", "        if mask1.any():\n", "            x1 = x[mask1]\n", "            grad_x1 = 0.004 * x1 * (x1.pow(2) - 9)\n", "            # grad_y remains 0 for this component\n", "            grad_x[mask1] = grad_x1\n", "    \n", "    else:\n", "        indx: int = int(batch_it.item())\n", "        if indx == 0:\n", "            x0, y0  = x, y\n", "            denom = 1 + 10*x0.pow(4)\n", "            grad_x = -40 * x0.pow(3) * y0.pow(2) / denom.pow(2)\n", "            grad_y = 2 * y0 / denom\n", "        elif indx == 1:\n", "            x1 = x\n", "            grad_x = 0.004 * x1 * (x1.pow(2) - 9)\n", "    \n", "    return torch.stack((grad_x, grad_y), dim=-1)\n", "\n", "def w2_distance_2d(samples1: Tensor, samples2: Tensor, sqrt=True):\n", "    \"\"\"\n", "    Computes the 2D Wasserstein-2 distance (or its square) between two point clouds\n", "    using the exact EMD solver from `ot.emd2`.\n", "\n", "    Note: \n", "      - `samples1` and `samples2` should be shape (N, 2), each with N points. \n", "      - If they differ in number, you must adapt e.g. by random subset or other approach.\n", "      - The cost used is the squared Euclidean distance. \n", "      - If sqrt=True, returns the actual W2 distance = sqrt( EMD cost ). \n", "        If sqrt=False, returns the 2-Wasserstein cost = EMD with squared distance.\n", "    \n", "    You need to have installed POT (Python Optimal Transport):\n", "        pip install pot\n", "    or\n", "        conda install -c conda-forge pot\n", "\n", "    Args:\n", "      samples1: (N, 2)\n", "      samples2: (N, 2)\n", "      sqrt:     bool, if True returns sqrt(emd2), else returns the raw EMD in squared distances\n", "    Returns:\n", "      a float (Python float) with the W2 distance or its square\n", "    \"\"\"\n", "    s1 = samples1.detach().cpu().numpy().reshape(-1, 2)\n", "    s2 = samples2.detach().cpu().numpy().reshape(-1, 2)\n", "\n", "    n = s1.shape[0]\n", "    if s2.shape[0] != n:\n", "        raise ValueError(f\"The two sample sets must have the same number of points for this method, \"\n", "                         f\"got {s1.shape[0]} vs {s2.shape[0]}.\")\n", "\n", "    # Uniform weights\n", "    a = np.ones(n) / n\n", "    b = np.ones(n) / n\n", "\n", "    # # Pairwise cost = squared Euclidean distance\n", "    cost_mat = ot.dist(s1, s2, metric='sqeuclidean')\n", "    cost_mat = cost_mat.astype(np.float64)\n", "\n", "    # Solve the EMD problem\n", "    emd_value = ot.emd2(a, b, cost_mat)  # returns the raw objective = sum_{i,j} pi_{i,j} * cost_{i,j}\n", "\n", "    # W2 distance is the sqrt of the EMD over squared distances\n", "    if sqrt:\n", "        return float(np.sqrt(emd_value))\n", "    else:\n", "        return float(emd_value)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def BAOAB_step(p,hc,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "    return(grads)\n", "\n", "def perturbed_BAOAB_step(p,hc,batch_it,last_grad,noise_sd):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        noise1=torch.randn_like(p.data,device=device)*noise_sd\n", "        noise2=torch.randn_like(p.data,device=device)*noise_sd\n", "\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1+hc.xc2*noise1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1+hc.vc1*noise1+hc.vc2*noise2\n", "\n", "    return(grads)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def whole_BAOAB_step(p,hc,last_grad):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad_entropy(p)\n", "\n", "    p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "  return(grads)\n", "\n", "def whole_BAOAB(K, h, gam):\n", "  \"\"\"\n", "  K: Total samples after BAOAB\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=BAOAB_hconst(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    grads = grad_entropy(p.data)\n", "\n", "    for i in range(K):\n", "      grads = whole_BAOAB_step(p, hper2c, grads)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        U_arr[i] = V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def SMS_BAOAB(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SMS_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr=torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr=torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=BAOAB_hconst(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      if par_runs>1:\n", "        rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      else:\n", "        rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "\n", "      \n", "      if i==0:\n", "        if par_runs>1:\n", "          grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "        else:\n", "          grads = grad(p.data, rng_perm[0]) * no_batches # k = 0 gradient\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        if par_runs>1:\n", "          grads = BAOAB_step(p, hper2c, rng_perm[:,k+1], grads)\n", "          V_arr[:,(2*i)*Nm + k,:]=p.data.clone()\n", "          U_arr[:,(2*i)*Nm + k]=V_entropy(p.data) \n", "        else:\n", "          grads = BAOAB_step(p, hper2c, rng_perm[k+1], grads)\n", "          V_arr[(2*i)*Nm + k,:]=p.data.clone().detach()\n", "          U_arr[(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "      for k in range(n_bw):\n", "        if par_runs>1:\n", "          grads = BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads)\n", "          V_arr[:,(2*i+1)*Nm + k,:]=p.data.clone()\n", "          U_arr[:,(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "        else:\n", "          grads = BAOAB_step(p, hper2c, rng_perm[Nm-1-k], grads)\n", "          V_arr[(2*i+1)*Nm + k,:]=p.data.clone()\n", "          U_arr[(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def perturbed_SMS_BAOAB(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after perturbed_SMS_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c = BAOAB_hconst(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      if par_runs>1:\n", "        rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      else:\n", "        rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "      \n", "      if i==0:\n", "        if par_runs>1:\n", "          grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "        else:\n", "          grads = grad(p.data, rng_perm[0]) * no_batches # k = 0 gradient\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw): \n", "        if par_runs>1:\n", "          grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,k+1], grads, noise_sd)\n", "          V_arr[:,(2*i)*Nm + k,:]=p.data.clone()\n", "          U_arr[:,(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "        else:\n", "          grads = perturbed_BAOAB_step(p, hper2c, rng_perm[k+1], grads, noise_sd)\n", "          V_arr[(2*i)*Nm + k,:]=p.data.clone()\n", "          U_arr[(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "      for k in range(n_bw):\n", "        if par_runs>1:\n", "          grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads, noise_sd)\n", "          V_arr[:,(2*i+1)*Nm + k,:]=p.data.clone()\n", "          U_arr[:,(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "        else:\n", "          grads = perturbed_BAOAB_step(p, hper2c, rng_perm[Nm-1-k], grads, noise_sd)\n", "          V_arr[(2*i+1)*Nm + k,:]=p.data.clone()\n", "          U_arr[(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def SG_BAOAB_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:   \n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    else:\n", "      rperm=rng.permutation(Nm)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      if par_runs>1:\n", "        grads=BAOAB_step(p,hper2c,rperm[:,k],grads)\n", "        V_arr[:,i*Nm+k,:]=p.data.clone()\n", "        U_arr[:,i*Nm+k]=V_entropy(p.data).detach()\n", "      else:\n", "        grads=BAOAB_step(p,hper2c,rperm[k],grads)\n", "        V_arr[i*Nm+k,:]=p.data.clone()\n", "        U_arr[i*Nm+k]=V_entropy(p.data).detach()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr, U_arr)\n", "\n", "def perturbed_SG_BAOAB_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:   \n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    else:\n", "      rperm=rng.permutation(Nm)\n", "\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      if par_runs>1:\n", "        grads=perturbed_BAOAB_step(p,hper2c,rperm[:,k],grads,noise_sd)\n", "        V_arr[:,i*Nm+k,:]=p.data.clone()\n", "        U_arr[:,i*Nm+k]=V_entropy(p.data).detach()\n", "      else:\n", "        grads=perturbed_BAOAB_step(p,hper2c,rperm[k],grads,noise_sd)\n", "        V_arr[i*Nm+k,:]=p.data.clone()\n", "        U_arr[i*Nm+k]=V_entropy(p.data).detach()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr, U_arr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### h=0.1, noise_sd=0.01"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Wass_arr=torch.zeros(1,4).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "it = 0\n", "rat = pow(2,it)\n", "num_epochs=int(rat*10**6)\n", "h=torch.tensor(0.1)/rat\n", "burn_in=int(rat*10**4)\n", "\n", "V_arr, U_arr = whole_BAOAB(num_epochs,h,gam)\n", "V1_SMS_arr, U1_SMS_arr = SMS_BAOAB(num_epochs,h,gam)\n", "print(f\"When h={h}, using SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V1_SMS_arr[burn_in:])}\")\n", "\n", "noise_sd=0.01\n", "V2_SMS_arr, U2_SMS_arr = perturbed_SMS_BAOAB(num_epochs,h,gam,noise_sd=noise_sd)\n", "print(f\"When h={h}, using perturbed_SMS_BAOAB, the <PERSON>serstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SMS_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SMS_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SMS_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SMS_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SMS_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SMS_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SMS_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SMS BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V1_SG_arr, U1_SG_arr = SG_BAOAB_without_replacement(num_epochs,h,gam)\n", "print(f\"When h={h}, using SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V1_SG_arr[burn_in:])}\")\n", "\n", "V2_SG_arr, U2_SG_arr = perturbed_SG_BAOAB_without_replacement(num_epochs,h,gam,noise_sd=noise_sd)\n", "print(f\"When h={h}, using perturbed_SMS_BAOAB, the <PERSON>serstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SG_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SG_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SG_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SG_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SG_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SG_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SG_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SG BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### h=0.1, noise_sd=0.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["noise_sd=0.1\n", "V2_SMS_arr, U2_SMS_arr = perturbed_SMS_BAOAB(num_epochs,h,gam,noise_sd=noise_sd)\n", "print(f\"When h={h}, using perturbed_SMS_BAOAB, the <PERSON>serstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SMS_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SMS_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SMS_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SMS_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SMS_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SMS_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SMS_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SMS BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V2_SG_arr, U2_SG_arr = perturbed_SG_BAOAB_without_replacement(num_epochs,h,gam,noise_sd=noise_sd)\n", "print(f\"When h={h}, using perturbed_SMS_BAOAB, the <PERSON>serstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SG_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SG_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SG_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SG_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SG_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SG_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SG_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SG BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### UBU"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for UBU\n", "@dataclass\n", "class hclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    etam1g: Tensor  # eta-1/gamma\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "# constant parameters for UBU\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc,xi1,xi2):\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --- global settings --------------------------------------------------\n", "no_batches = torch.tensor(2, device=device)  # number of batches\n", "batch_size = 1   \n", "par_runs = 1     # number of parallel chain runs\n", "\n", "# regularisation constant used in original code just to set gamma\n", "l2regconst = torch.tensor(25.0, device=device)\n", "gam        = torch.sqrt(l2regconst)              # damping parameter γ\n", "\n", "# toy example for 2 dimensions\n", "dim          = 2          # <— NEW\n", "# epsilon      = 0.1        # funnel strength ε\n", "idx_x        = 0          # column 0 = x\n", "idx_y        = 1          # column 1 = θ\n", "\n", "if par_runs > 1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # two coordinates now\n", "else:\n", "    p = torch.zeros(2, device=device)\n", "\n", "p.v = torch.randn_like(p, device=device)        # same trick as before"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace V and grad by the funnel versions\n", "def V_entropy(p2d: Tensor) -> Tensor:  # p2d shape: (N,2)\n", "    \"\"\"\n", "    Entropic Barrier 2D potential:\n", "        U(x, y) = y^2/(1+10x^4) + 0.001(x^2-9)^2.\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    return y.pow(2) / (1 + 10*x.pow(4)) + 0.001 * (x.pow(2) - 9).pow(2)\n", "\n", "def grad_entropy(p2d: Tensor) -> Tensor:\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = - 40*x^3*y^2/(1+10x^4)^2 + 0.004*x*(x^2-9)\n", "      dU/dy     = 2 * y / (1 + 10x^4)\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    grad_x = - 40*x.pow(3)*y.pow(2) / (1 + 10*x.pow(4)).pow(2) + 0.004*x*(x.pow(2)-9)\n", "    grad_y = 2 * y / (1 + 10*x.pow(4))\n", "    return torch.stack((grad_x, grad_y), dim=-1)   # shape (N,2)\n", "\n", "def grad(p2d: Tensor, batch_it):\n", "    x, y = p2d[...,0], p2d[...,1]\n", "    \n", "    # # Convert batch_it to tensor if it's a numpy array\n", "    # if isinstance(batch_it, np.ndarray):\n", "    #     batch_it = torch.tensor(batch_it, device=device, dtype=torch.long)\n", "    \n", "    \n", "    # Initialize gradients\n", "    grad_x = torch.zeros_like(x)\n", "    grad_y = torch.zeros_like(y)\n", "    \n", "    if par_runs > 1:   \n", "        # Mask for batch indices\n", "        mask0 = (batch_it == 0)\n", "        mask1 = (batch_it == 1)\n", "        \n", "        # Compute gradients based on batch index\n", "        if mask0.any():\n", "            x0, y0  = x[mask0], y[mask0]\n", "            denom = 1 + 10*x0.pow(4)\n", "            grad_x0 = -40 * x0.pow(3) * y0.pow(2) / denom.pow(2)\n", "            grad_y0 = 2 * y0 / denom\n", "            grad_x[mask0] = grad_x0\n", "            grad_y[mask0] = grad_y0\n", "        \n", "        if mask1.any():\n", "            x1 = x[mask1]\n", "            grad_x1 = 0.004 * x1 * (x1.pow(2) - 9)\n", "            # grad_y remains 0 for this component\n", "            grad_x[mask1] = grad_x1\n", "    \n", "    else:\n", "        indx: int = int(batch_it.item())\n", "        if indx == 0:\n", "            x0, y0  = x, y\n", "            denom = 1 + 10*x0.pow(4)\n", "            grad_x = -40 * x0.pow(3) * y0.pow(2) / denom.pow(2)\n", "            grad_y = 2 * y0 / denom\n", "        elif indx == 1:\n", "            x1 = x\n", "            grad_x = 0.004 * x1 * (x1.pow(2) - 9)\n", "    \n", "    return torch.stack((grad_x, grad_y), dim=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def UBU_step(p,hper2c,batch_it):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "    p.v-=hper2c.h*grads\n", "\n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def perturbed_UBU_step(p,hper2c,batch_it,noise_sd):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "        noise=torch.randn_like(p.data,device=device)*noise_sd\n", "        grads=grad(p,batch_it)*no_batches \n", "        p.v-=hper2c.h*grads+hper2c.h*noise\n", "\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def whole_UBU_step(p,hper2c):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    xi2=torch.randn_like(p.data,device=device)\n", "    p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=grad_entropy(p)\n", "    p.v-=hper2c.h*grads\n", "\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    xi2=torch.randn_like(p.data,device=device)\n", "    p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def whole_UBU(K, h, gam):\n", "  \"\"\"\n", "  K: Total samples after UBU\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=hper2const(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    \n", "    for i in range(K):\n", "      whole_UBU_step(p,hper2c)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        U_arr[i] = V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def SMS_UBU(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr=torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr=torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:\n", "        rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "    else:\n", "        rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      if par_runs>1:\n", "        UBU_step(p, hper2c, rng_perm[:,k])\n", "        V_arr[:,(2*i)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i)*Nm + k]=V_entropy(p.data) \n", "      else:\n", "        UBU_step(p, hper2c, rng_perm[k])\n", "        V_arr[(2*i)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      if par_runs>1:\n", "        UBU_step(p, hper2c, rng_perm[:,Nm-1-k])\n", "        V_arr[:,(2*i+1)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "      else:\n", "        UBU_step(p, hper2c, rng_perm[Nm-1-k])\n", "        V_arr[(2*i+1)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def perturbed_SMS_UBU(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr=torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr=torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:\n", "        rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "    else:\n", "        rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      if par_runs>1:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[:,k], noise_sd)\n", "        V_arr[:,(2*i)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i)*Nm + k]=V_entropy(p.data) \n", "      else:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[k], noise_sd)\n", "        V_arr[(2*i)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      if par_runs>1:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[:,Nm-1-k], noise_sd)\n", "        V_arr[:,(2*i+1)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "      else:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[Nm-1-k], noise_sd)\n", "        V_arr[(2*i+1)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def SG_UBU_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:   \n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    else:\n", "      rperm=rng.permutation(Nm)\n", "    \n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      if par_runs>1:\n", "        UBU_step(p,hper2c,rperm[:,k])\n", "        V_arr[:,i*Nm+k,:]=p.data.clone()\n", "        U_arr[:,i*Nm+k]=V_entropy(p.data).detach()\n", "      else:\n", "        UBU_step(p,hper2c,rperm[k])\n", "        V_arr[i*Nm+k,:]=p.data.clone().detach()\n", "        U_arr[i*Nm+k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def perturbed_SG_UBU_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 0] = 3.0                    # x = 3\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[0] = 3.0                    # x = 3\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:   \n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    else:\n", "      rperm=rng.permutation(Nm)\n", "    \n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      if par_runs>1:\n", "        perturbed_UBU_step(p,hper2c,rperm[:,k],noise_sd)\n", "        V_arr[:,i*Nm+k,:]=p.data.clone()\n", "        U_arr[:,i*Nm+k]=V_entropy(p.data).detach()\n", "      else:\n", "        perturbed_UBU_step(p,hper2c,rperm[k],noise_sd)\n", "        V_arr[i*Nm+k,:]=p.data.clone().detach()\n", "        U_arr[i*Nm+k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### h=0.1, noise_sd=0.01"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Wass_arr=torch.zeros(1,4).detach()\n", "methods_list=[SMS_UBU, SG_UBU_without_replacement, perturbed_SMS_UBU, perturbed_SG_UBU_without_replacement]\n", "it = 0\n", "rat = pow(2,it)\n", "num_epochs=int(rat*10**6)\n", "h=torch.tensor(0.1)/rat\n", "burn_in=int(rat*10**4)\n", "\n", "V_arr, U_arr = whole_UBU(num_epochs,h,gam)\n", "V1_SMS_arr, U1_SMS_arr = SMS_UBU(num_epochs,h,gam)\n", "# print(f\"When h={h}, using SMS_UBU, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V1_SMS_arr[burn_in:])}\")\n", "\n", "noise_sd=0.01\n", "V2_SMS_arr, U2_SMS_arr = perturbed_SMS_UBU(num_epochs,h,gam,noise_sd=noise_sd)\n", "# print(f\"When h={h}, using perturbed_SMS_UBU, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SMS_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SMS_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SMS_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SMS_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SMS_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SMS_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SMS_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"UBU (h={h})\")\n", "axes[0,1].set_title(f\"SMS UBU\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V1_SG_arr, U1_SG_arr = SG_UBU_without_replacement(num_epochs,h,gam)\n", "# print(f\"When h={h}, using SMS_UBU, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V1_SG_arr[burn_in:])}\")\n", "\n", "V2_SG_arr, U2_SG_arr = perturbed_SG_UBU_without_replacement(num_epochs,h,gam,noise_sd=noise_sd)\n", "# print(f\"When h={h}, using perturbed_SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SG_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SG_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SG_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SG_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SG_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SG_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SG_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"UBU (h={h})\")\n", "axes[0,1].set_title(f\"SG UBU\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## $U_{\\mathrm{Neal}}(x,\\theta)=\\frac{x^2}{2e^\\theta}+\\frac{\\epsilon}{2}(x^2+\\theta^2)$\n", "### BAOAB"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import ot\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for BAOAB\n", "@dataclass\n", "class BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "# # parameters for perturbed BAOAB\n", "# @dataclass\n", "# class perturbed_BAOABhclass:\n", "#     h: Tensor    # step size\n", "#     eta: Tensor  # exp(-gamma*h/2)\n", "#     xc1: Tensor\n", "#     xc2: Tensor\n", "#     xc3: Tensor\n", "#     vc1: Tensor\n", "#     vc2: Tensor\n", "#     vc3: Tensor\n", "#     xcn1: Tensor # perturbation on x (parameter for gaussian noise 1)\n", "#     vcn1: Tensor # perturbation on v (parameter for gaussian noise 1)\n", "#     vcn2: Tensor # perturbation on v (parameter for gaussian noise 2)\n", "\n", "# constant parameters for BAOAB\n", "def BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)\n", "    \n", "# # constant parameters for perturbed BAOAB\n", "# def perturbed_BAOAB_hconst(h,gam):\n", "#     with torch.no_grad():\n", "#         hh=copy.deepcopy(h).detach().double()\n", "#         gamm=copy.deepcopy(gam).detach().double()\n", "#         gh=gamm*hh\n", "#         eta=(torch.exp(-gh/2))\n", "#         xc1=hh/2*(1+eta)\n", "#         xc2=(hh*hh/4)*(1+eta)\n", "#         xc3=hh/2*torch.sqrt(-torch.expm1(-gh))\n", "#         vc1=eta*(hh/2)\n", "#         vc2=(hh/2)\n", "#         vc3=torch.sqrt(-torch.expm1(-gh))\n", "\n", "#         xcn1=(hh*hh/4)*(1+eta)  # xc2\n", "#         vcn1=eta*(hh/2)         # vc1\n", "#         vcn2=(hh/2)             # vc2\n", "\n", "#         hc=perturbed_BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float(),xcn1=xcn1.float(),vcn1=vcn1.float(),vcn2=vcn2.float())\n", "#         return(hc)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# --- global settings --------------------------------------------------\n", "no_batches = torch.tensor(2, device=device)  # number of batches\n", "batch_size = 1   \n", "par_runs = 1     # number of parallel chain runs\n", "\n", "# regularisation constant used in original code just to set gamma\n", "l2regconst = torch.tensor(25.0, device=device)\n", "gam        = torch.sqrt(l2regconst)              # damping parameter γ\n", "\n", "# toy example for 2 dimensions\n", "dim          = 2          # <— NEW\n", "epsilon      = 0.1        # funnel strength ε\n", "idx_x        = 0          # column 0 = x\n", "idx_theta    = 1          # column 1 = θ\n", "\n", "if par_runs > 1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # two coordinates now\n", "else:\n", "    p = torch.zeros(2, device=device)\n", "\n", "p.v = torch.randn_like(p, device=device)        # same trick as before"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# # Replace V and grad by the funnel versions\n", "def V_neal(p2d: Tensor) -> Tensor:  # p2d shape: (N,2)\n", "    \"\"\"\n", "    Neal-like 2D potential:\n", "        U(x, theta) = x^2/(2 e^theta) + (epsilon/2)*(x^2 + theta^2).\n", "    states: shape (n_samples, 2).\n", "    returns: shape (n_samples,) of potential values.\n", "    \"\"\"\n", "    x, theta = p2d[...,0], p2d[...,1]\n", "    return 0.5 * x.pow(2) * torch.exp(-theta) + 0.5 * epsilon * (x.pow(2) + theta.pow(2))\n", "\n", "def grad_neal(p2d: Tensor) -> Tensor:\n", "    \"\"\"\n", "    Gradient of the above potential wrt x and theta:\n", "      dU/dx     = x * exp(-theta) + epsilon*x\n", "      dU/dtheta = -0.5 * x^2 * exp(-theta) + epsilon * theta\n", "    Returns shape (n_samples, 2).\n", "    \"\"\"\n", "    x, theta = p2d[...,0], p2d[...,1]\n", "    grad_x = x * torch.exp(-theta) + epsilon * x\n", "    grad_theta = -0.5 * x.pow(2) * torch.exp(-theta) + epsilon * theta\n", "    return torch.stack((grad_x, grad_theta), dim=-1)   # shape (N,2)\n", "\n", "# # wrappers to keep the old BAOAB/UBU signatures intact\n", "\n", "# # def grad(p2d: Tensor, batch_it, epsilon=0.1):\n", "# #     # scale by no_batches to mimic the original SGD‑style variance inflation\n", "# #     x, theta = p2d[...,0], p2d[...,1]\n", "\n", "# #     if batch_it==0:\n", "# #         grad_x = x * torch.exp(-theta)\n", "# #         grad_theta = -0.5 * x.pow(2) * torch.exp(-theta)\n", "    \n", "# #     elif batch_it==1:\n", "# #         grad_x = epsilon * x\n", "# #         grad_theta = epsilon * theta\n", "\n", "# #     return torch.stack((grad_x, grad_theta), dim=-1) * no_batches  # shape (N,2)\n", "\n", "def grad(p2d: Tensor, batch_it):\n", "    x, theta = p2d[...,0], p2d[...,1]\n", "    \n", "    # # Convert batch_it to tensor if it's a numpy array\n", "    # if isinstance(batch_it, np.ndarray):\n", "    #     batch_it = torch.tensor(batch_it, device=device, dtype=torch.long)\n", "    \n", "    # Initialize gradients\n", "    grad_x = torch.zeros_like(x)\n", "    grad_theta = torch.zeros_like(theta)\n", "    \n", "    if par_runs > 1:\n", "        # Mask for batch indices\n", "        mask0 = (batch_it == 0)\n", "        mask1 = (batch_it == 1)\n", "        \n", "        # Compute gradients based on batch index\n", "        if mask0.any():\n", "            grad_x[mask0] = x[mask0] * torch.exp(-theta[mask0])\n", "            grad_theta[mask0] = -0.5 * x[mask0].pow(2) * torch.exp(-theta[mask0])\n", "        \n", "        if mask1.any():\n", "            grad_x[mask1] = epsilon * x[mask1]\n", "            grad_theta[mask1] = epsilon * theta[mask1]\n", "    else:\n", "        indx: int = int(batch_it.item())\n", "        if indx == 0:\n", "            grad_x = x * torch.exp(-theta)\n", "            grad_theta = -0.5 * x.pow(2) * torch.exp(-theta)\n", "        elif indx == 1:\n", "            grad_x = epsilon * x\n", "            grad_theta = epsilon * theta\n", "    \n", "    return torch.stack((grad_x, grad_theta), dim=-1)\n", "\n", "# # def Wass2_2d(a: Tensor, b: <PERSON>sor) -> Tensor:\n", "# #     a_ = a.reshape(-1, 2).sort(dim=0)[0]\n", "# #     b_ = b.reshape(-1, 2).sort(dim=0)[0]\n", "# #     return ((a_ - b_).pow(2).sum(dim=1)).mean().sqrt()\n", "\n", "# def w2_distance_2d(samples1: Tensor, samples2: Tensor, sqrt=True):\n", "#     \"\"\"\n", "#     Computes the 2D Wasserstein-2 distance (or its square) between two point clouds\n", "#     using the exact EMD solver from `ot.emd2`.\n", "\n", "#     Note: \n", "#       - `samples1` and `samples2` should be shape (N, 2), each with N points. \n", "#       - If they differ in number, you must adapt e.g. by random subset or other approach.\n", "#       - The cost used is the squared Euclidean distance. \n", "#       - If sqrt=True, returns the actual W2 distance = sqrt( EMD cost ). \n", "#         If sqrt=False, returns the 2-Wasserstein cost = EMD with squared distance.\n", "    \n", "#     You need to have installed POT (Python Optimal Transport):\n", "#         pip install pot\n", "#     or\n", "#         conda install -c conda-forge pot\n", "\n", "#     Args:\n", "#       samples1: (N, 2)\n", "#       samples2: (N, 2)\n", "#       sqrt:     bool, if True returns sqrt(emd2), else returns the raw EMD in squared distances\n", "#     Returns:\n", "#       a float (Python float) with the W2 distance or its square\n", "#     \"\"\"\n", "#     s1 = samples1.detach().cpu().numpy()\n", "#     s2 = samples2.detach().cpu().numpy()\n", "\n", "#     n = s1.shape[0]\n", "#     if s2.shape[0] != n:\n", "#         raise ValueError(f\"The two sample sets must have the same number of points for this method, \"\n", "#                          f\"got {s1.shape[0]} vs {s2.shape[0]}.\")\n", "\n", "#     # Uniform weights\n", "#     a = np.ones(n) / n\n", "#     b = np.ones(n) / n\n", "\n", "#     # Pairwise cost = squared Euclidean distance\n", "#     cost_mat = ot.dist(s1, s2, metric='sqeuclidean')\n", "#     cost_mat = cost_mat.astype(np.float64)\n", "\n", "#     # Solve the EMD problem\n", "#     emd_value = ot.emd2(a, b, cost_mat)  # returns the raw objective = sum_{i,j} pi_{i,j} * cost_{i,j}\n", "\n", "#     # W2 distance is the sqrt of the EMD over squared distances\n", "#     if sqrt:\n", "#         return float(np.sqrt(emd_value))\n", "#     else:\n", "#         return float(emd_value)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def BAOAB_step(p,hc,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "    return(grads)\n", "\n", "def perturbed_BAOAB_step(p,hc,batch_it,last_grad,noise_sd):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        noise1=torch.randn_like(p.data,device=device)*noise_sd\n", "        noise2=torch.randn_like(p.data,device=device)*noise_sd\n", "\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1+hc.xc2*noise1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1+hc.vc1*noise1+hc.vc2*noise2\n", "\n", "    return(grads)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def whole_BAOAB_step(p,hc,last_grad):\n", "  with torch.no_grad():\n", "    xi1=torch.randn_like(p.data,device=device)\n", "    p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad_entropy(p)\n", "\n", "    p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "  return(grads)\n", "\n", "def whole_BAOAB(K, h, gam):\n", "  \"\"\"\n", "  K: Total samples after BAOAB\n", "  \"\"\"\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 1] = 5.0                    # x = 5\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[1] = 5.0\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "\n", "    hper2c=BAOAB_hconst(h,gam)\n", "    # Initialise velocities\n", "    p.v = torch.randn_like(p, device=device).detach()\n", "    grads = grad_entropy(p.data)\n", "\n", "    for i in range(K):\n", "      grads = whole_BAOAB_step(p, hper2c, grads)\n", "\n", "      if par_runs>1:\n", "        V_arr[:, i, :] = p.data.clone()\n", "        U_arr[:, i] = V_entropy(p.data).detach()\n", "      else:\n", "        V_arr[i,:]=p.data.clone()\n", "        U_arr[i] = V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 1] = 5.0                    # theta = 5\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[1] = 5.0                    # x = 5\n", "\n", "  rng = np.random.default_rng()\n", "def SMS_UBU(K,h,gam):\n", "  \"\"\"\n", "  K: Total sa\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr=torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr=torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:\n", "        rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "    else:\n", "        rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      if par_runs>1:\n", "        UBU_step(p, hper2c, rng_perm[:,k])\n", "        V_arr[:,(2*i)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i)*Nm + k]=V_entropy(p.data) \n", "      else:\n", "        UBU_step(p, hper2c, rng_perm[k])\n", "        V_arr[(2*i)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      if par_runs>1:\n", "        UBU_step(p, hper2c, rng_perm[:,Nm-1-k])\n", "        V_arr[:,(2*i+1)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "      else:\n", "        UBU_step(p, hper2c, rng_perm[Nm-1-k])\n", "        V_arr[(2*i+1)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def perturbed_SMS_UBU(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 1] = 5.0                    # theta = 5\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[1] = 5.0                    # theta = 5\n", "\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr=torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr=torch.zeros((K, 2), device=device).detach()\n", "      U_arr=torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:\n", "        rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "    else:\n", "        rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "        rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      if par_runs>1:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[:,k], noise_sd)\n", "        V_arr[:,(2*i)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i)*Nm + k]=V_entropy(p.data) \n", "      else:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[k], noise_sd)\n", "        V_arr[(2*i)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      if par_runs>1:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[:,Nm-1-k], noise_sd)\n", "        V_arr[:,(2*i+1)*Nm + k,:]=p.data.clone()\n", "        U_arr[:,(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "      else:\n", "        perturbed_UBU_step(p, hper2c, rng_perm[Nm-1-k], noise_sd)\n", "        V_arr[(2*i+1)*Nm + k,:]=p.data.clone().detach()\n", "        U_arr[(2*i+1)*Nm + k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def SG_UBU_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 1] = 5.0                    # theta = 5\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[1] = 5.0                    # theta = 5\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:   \n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    else:\n", "      rperm=rng.permutation(Nm)\n", "    \n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      if par_runs>1:\n", "        UBU_step(p,hper2c,rperm[:,k])\n", "        V_arr[:,i*Nm+k,:]=p.data.clone()\n", "        U_arr[:,i*Nm+k]=V_entropy(p.data).detach()\n", "      else:\n", "        UBU_step(p,hper2c,rperm[k])\n", "        V_arr[i*Nm+k,:]=p.data.clone().detach()\n", "        U_arr[i*Nm+k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n", "\n", "def perturbed_SG_UBU_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  if par_runs>1:\n", "    p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "    p[:, 1] = 5.0                    # theta = 5\n", "  else:\n", "    p = torch.zeros(2, device=device)   # (par_runs,2)\n", "    p[1] = 5.0                    # theta = 5\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    if par_runs>1:\n", "      V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "      U_arr = torch.zeros(par_runs, K, device=device).detach()\n", "    else:\n", "      V_arr = torch.zeros((K, 2), device=device).detach()\n", "      U_arr = torch.zeros(K, device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    if par_runs>1:   \n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    else:\n", "      rperm=rng.permutation(Nm)\n", "    \n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      if par_runs>1:\n", "        perturbed_UBU_step(p,hper2c,rperm[:,k],noise_sd)\n", "        V_arr[:,i*Nm+k,:]=p.data.clone()\n", "        U_arr[:,i*Nm+k]=V_entropy(p.data).detach()\n", "      else:\n", "        perturbed_UBU_step(p,hper2c,rperm[k],noise_sd)\n", "        V_arr[i*Nm+k,:]=p.data.clone().detach()\n", "        U_arr[i*Nm+k]=V_entropy(p.data).detach()\n", "\n", "  return(V_arr, U_arr)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def SMS_BAOAB(K,h,gam):\n", "#   \"\"\"\n", "#   K: Total samples after SMS_BAOAB\n", "#   no_batches: Nm\n", "#   batch_size: Nb, here is 1\n", "#   \"\"\"\n", "#   p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr=torch.zeros((par_runs, K, 2), device=device).detach()\n", "#     hper2c=BAOAB_hconst(h,gam)\n", "#     # Initialise velocities\n", "#     p.v = torch.randn_like(p, device=device).detach()\n", "\n", "#     for i in range(1, np.ceil(K/(2*no_batches))+1):\n", "#       rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "#       rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "#       if i==1:\n", "#         grads = grad(p.data,0)\n", "\n", "#       for k in range(1, min(no_batches, K-(2*i-2)*no_batches)+1):\n", "#         grads = BAOAB_step(p, hper2c, rng_perm[:,k], grads)\n", "\n", "#         V_arr[:,(2*i-2)*no_batches+k-1,:]=p.data.clone()\n", "\n", "#       for k in range(1, min(no_batches, K-(2*i-1)*no_batches)+1):\n", "#         grads = BAOAB_step(p, hper2c, rng_perm[:,no_batches-k], grads)\n", "\n", "#         V_arr[:,(2*i-1)*no_batches+k-1,:]=p.data.clone()\n", "\n", "#   return(V_arr)\n", "\n", "# def perturbed_SMS_BAOAB(K,h,gam,noise_sd):\n", "#   \"\"\"\n", "#   K: Total samples after perturbed_SMS_BAOAB\n", "#   no_batches: Nm\n", "#   batch_size: Nb, here is 1\n", "#   \"\"\"\n", "#   p = torch.zeros((par_runs, 2), device=device)   # (par_runs,2)\n", "#   rng = np.random.default_rng()\n", "\n", "#   with torch.no_grad():\n", "#     V_arr = torch.zeros((par_runs, K, 2), device=device).detach()\n", "#     hper2c = BAOAB_hconst(h,gam)\n", "#     # Initialise velocities\n", "#     p.v = torch.randn_like(p, device=device).detach()\n", "\n", "#     for i in range(1, np.ceil(K/(2*no_batches))+1):\n", "#       rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "#       rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "#       if i==1:\n", "#         grads = grad(p.data,0)\n", "\n", "#       for k in range(1, min(no_batches, K-(2*i-2)*no_batches)+1): \n", "#         grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,k], grads, noise_sd)\n", "\n", "#         V_arr[:,(2*i-2)*no_batches+k-1,:]=p.data.clone()\n", "\n", "#       for k in range(1, min(no_batches, K-(2*i-1)*no_batches)+1):\n", "#         grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,no_batches-k], grads, noise_sd)\n", "\n", "#         V_arr[:,(2*i-1)*no_batches+k-1,:]=p.data.clone()\n", "\n", "#   return(V_arr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### h=0.02, noise_sd=0.1"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running baseline SMS_BAOAB…\n", "Running perturbed SMS_BAOAB (noise_sd = 0.02)…\n"]}, {"ename": "RuntimeError", "evalue": "The size of tensor a (20000) must match the size of tensor b (2) at non-singleton dimension 1", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 12\u001b[0m\n\u001b[0;32m      9\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mW₂(base, noisy) on last half of trajectory = \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mw\u001b[38;5;241m.\u001b[39mitem()\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.6f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m---> 12\u001b[0m     \u001b[43m_demo\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[14], line 7\u001b[0m, in \u001b[0;36m_demo\u001b[1;34m()\u001b[0m\n\u001b[0;32m      5\u001b[0m base \u001b[38;5;241m=\u001b[39m SMS_BAOAB(num_epochs, h, gam)\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRunning perturbed SMS_BAOAB (noise_sd = 0.02)…\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 7\u001b[0m noisy \u001b[38;5;241m=\u001b[39m \u001b[43mperturbed_SMS_BAOAB\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnum_epochs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mh\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgam\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnoise_sd\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.02\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m w \u001b[38;5;241m=\u001b[39m Wass2_2d(base[:, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m500\u001b[39m:, :], noisy[:, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m500\u001b[39m:, :])\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mW₂(base, noisy) on last half of trajectory = \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mw\u001b[38;5;241m.\u001b[39mitem()\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.6f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "Cell \u001b[1;32mIn[13], line 48\u001b[0m, in \u001b[0;36mperturbed_SMS_BAOAB\u001b[1;34m(num_epochs, h, gam, noise_sd)\u001b[0m\n\u001b[0;32m     46\u001b[0m   it\u001b[38;5;241m=\u001b[39mepoch\u001b[38;5;241m*\u001b[39mno_batches\u001b[38;5;241m+\u001b[39mb\n\u001b[0;32m     47\u001b[0m   ind\u001b[38;5;241m=\u001b[39mind_create(it)\n\u001b[1;32m---> 48\u001b[0m   grads\u001b[38;5;241m=\u001b[39m\u001b[43mperturbed_BAOAB_step\u001b[49m\u001b[43m(\u001b[49m\u001b[43mp\u001b[49m\u001b[43m,\u001b[49m\u001b[43mhper2c\u001b[49m\u001b[43m,\u001b[49m\u001b[43mind\u001b[49m\u001b[43m,\u001b[49m\u001b[43mgrads\u001b[49m\u001b[43m,\u001b[49m\u001b[43mnoise_sd\u001b[49m\u001b[43m)\u001b[49m     \n\u001b[0;32m     50\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m torch\u001b[38;5;241m.\u001b[39mno_grad():\n\u001b[0;32m     51\u001b[0m   V_arr[:,epoch,:]\u001b[38;5;241m=\u001b[39mp\u001b[38;5;241m.\u001b[39mdata\n", "Cell \u001b[1;32mIn[12], line 21\u001b[0m, in \u001b[0;36mperturbed_BAOAB_step\u001b[1;34m(p, hc, batch_it, last_grad, noise_sd)\u001b[0m\n\u001b[0;32m     18\u001b[0m     noise1\u001b[38;5;241m=\u001b[39mtorch\u001b[38;5;241m.\u001b[39mrandn_like(p\u001b[38;5;241m.\u001b[39mdata,device\u001b[38;5;241m=\u001b[39mdevice)\u001b[38;5;241m*\u001b[39mnoise_sd\n\u001b[0;32m     19\u001b[0m     noise2\u001b[38;5;241m=\u001b[39mtorch\u001b[38;5;241m.\u001b[39mrandn_like(p\u001b[38;5;241m.\u001b[39mdata,device\u001b[38;5;241m=\u001b[39mdevice)\u001b[38;5;241m*\u001b[39mnoise_sd\n\u001b[1;32m---> 21\u001b[0m     p\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m=\u001b[39m\u001b[43mp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43mhc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mxc1\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mv\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43mhc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mxc2\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mlast_grad\u001b[49m\u001b[38;5;241m+\u001b[39mhc\u001b[38;5;241m.\u001b[39mxc3\u001b[38;5;241m*\u001b[39mxi1\u001b[38;5;241m+\u001b[39mhc\u001b[38;5;241m.\u001b[39mxc2\u001b[38;5;241m*\u001b[39mnoise1\n\u001b[0;32m     23\u001b[0m grads\u001b[38;5;241m=\u001b[39mgrad(p,batch_it) \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# retain scaling inside step\u001b[39;00m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m torch\u001b[38;5;241m.\u001b[39mno_grad():\n", "\u001b[1;31mRuntimeError\u001b[0m: The size of tensor a (20000) must match the size of tensor b (2) at non-singleton dimension 1"]}], "source": ["# Wass_arr=torch.zeros(1,4).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "it = 0\n", "rat = pow(2,it)\n", "num_epochs=int(2*10**6)\n", "h=torch.tensor(0.02)/rat\n", "burn_in=int(rat*10**5)\n", "\n", "V_arr, U_arr = whole_BAOAB(num_epochs,h,gam)\n", "V1_SMS_arr, U1_SMS_arr = SMS_BAOAB(num_epochs,h,gam)\n", "# print(f\"When h={h}, using SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V1_SMS_arr[burn_in:])}\")\n", "\n", "noise_sd=0.1\n", "V2_SMS_arr, U2_SMS_arr = perturbed_SMS_BAOAB(num_epochs,h,gam,noise_sd=noise_sd)\n", "# print(f\"When h={h}, using perturbed_SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SMS_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SMS_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SMS_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SMS_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SMS_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SMS_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SMS_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SMS BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V1_SG_arr, U1_SG_arr = SG_BAOAB_without_replacement(num_epochs,h,gam)\n", "# print(f\"When h={h}, using SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V1_SG_arr[burn_in:])}\")\n", "\n", "V2_SG_arr, U2_SG_arr = perturbed_SG_BAOAB_without_replacement(num_epochs,h,gam,noise_sd=noise_sd)\n", "# print(f\"When h={h}, using perturbed_SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SG_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SG_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SG_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SG_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SG_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SG_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SG_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SG BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### h=0.02, noise_sd=0.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["noise_sd=0.5\n", "V2_SMS_arr, U2_SMS_arr = perturbed_SMS_BAOAB(num_epochs,h,gam,noise_sd=noise_sd)\n", "# print(f\"When h={h}, using perturbed_SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SMS_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SMS_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SMS_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SMS_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SMS_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SMS_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SMS_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SMS BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V2_SG_arr, U2_SG_arr = perturbed_SG_BAOAB_without_replacement(num_epochs,h,gam,noise_sd=noise_sd)\n", "# print(f\"When h={h}, using perturbed_SMS_BAOAB, the Wasserstein distance is {w2_distance_2d(V_arr[burn_in:], V2_SG_arr[burn_in:])}\")\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(16, 12))\n", "# Plotting the trajectories\n", "plot_traj(V_arr, burn_in, axes[0, 0])\n", "plot_traj(V1_SG_arr, burn_in, axes[0, 1])\n", "plot_traj(V2_SG_arr, burn_in, axes[0, 2])\n", "# Plotting the cumulative mean potential\n", "plot_cumulative_mean_potential(U_arr, h, burn_in, axes[1, 0])\n", "plot_cumulative_mean_potential(U1_SG_arr, h, burn_in, axes[1, 1])\n", "plot_cumulative_mean_potential(U2_SG_arr, h, burn_in, axes[1, 2])\n", "# Plotting the x-coordinate trajectory\n", "plot_x_traj(V_arr, h, burn_in, axes[2, 0])\n", "plot_x_traj(V1_SG_arr, h, burn_in, axes[2, 1])\n", "plot_x_traj(V2_SG_arr, h, burn_in, axes[2, 2])\n", "\n", "axes[0,0].set_title(f\"BAOAB (h={h})\")\n", "axes[0,1].set_title(f\"SG BAOAB\")\n", "axes[0,2].set_title(f\"noise SD={noise_sd}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import torch\n", "# import numpy as np\n", "# from dataclasses import dataclass\n", "\n", "# device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# ##############################################\n", "# # 1. Define the 2D Potential and Gradient\n", "# ##############################################\n", "\n", "# def potential_2d(states, epsilon=0.1):\n", "#     \"\"\"\n", "#     Neal-like 2D potential:\n", "#         U(x, theta) = x^2/(2 e^theta) + (epsilon/2)*(x^2 + theta^2).\n", "#     states: shape (n_samples, 2).\n", "#     returns: shape (n_samples,) of potential values.\n", "#     \"\"\"\n", "#     x = states[:, 0]\n", "#     theta = states[:, 1]\n", "#     return 0.5 * x**2 * torch.exp(-theta) + 0.5 * epsilon * (x**2 + theta**2)\n", "\n", "# def grad_potential_2d(states, epsilon=0.1):\n", "#     \"\"\"\n", "#     Gradient of the above potential wrt x and theta:\n", "#       dU/dx     = x * exp(-theta) + epsilon*x\n", "#       dU/dtheta = -0.5 * x^2 * exp(-theta) + epsilon * theta\n", "#     Returns shape (n_samples, 2).\n", "#     \"\"\"\n", "#     x = states[:, 0]\n", "#     theta = states[:, 1]\n", "#     dU_dx = x * torch.exp(-theta) + epsilon * x\n", "#     dU_dtheta = -0.5 * x**2 * torch.exp(-theta) + epsilon * theta\n", "#     return torch.stack([dU_dx, dU_dtheta], dim=1)\n", "\n", "\n", "# ##############################################\n", "# # 2. Define BAOAB Coefficients (Same as 1D, but used in 2D)\n", "# ##############################################\n", "\n", "# @dataclass\n", "# class BAOABCoeffs:\n", "#     \"\"\"\n", "#     Holds the integrator coefficients for BAOAB.\n", "#     \"\"\"\n", "#     h: float      # step size\n", "#     eta: float\n", "#     xc1: float\n", "#     xc2: float\n", "#     xc3: float\n", "#     vc1: float\n", "#     vc2: float\n", "#     vc3: float\n", "\n", "# def make_baoab_coeffs(h, gamma):\n", "#     \"\"\"\n", "#     Create BAOAB coefficients. (Same approach as your 1D code, no change needed.)\n", "#     \"\"\"\n", "#     # all done in double precision, then cast to float\n", "#     hh = float(h)\n", "#     gam = float(gamma)\n", "#     gh = gam * hh\n", "#     eta = np.exp(-gh/2)\n", "\n", "#     xc1 = hh / 2 * (1 + eta)\n", "#     xc2 = (hh**2 / 4) * (1 + eta)\n", "#     xc3 = hh / 2 * np.sqrt(-np.expm1(-gh))\n", "#     vc1 = eta * (hh/2)\n", "#     vc2 = (hh/2)\n", "#     vc3 = np.sqrt(-np.expm1(-gh))\n", "\n", "#     return BAOABCoeffs(h=hh, eta=eta, xc1=xc1, xc2=xc2, xc3=xc3,\n", "#                        vc1=vc1, vc2=vc2, vc3=vc3)\n", "\n", "\n", "# ##############################################\n", "# # 3. BAOAB Step (2D)\n", "# ##############################################\n", "\n", "# def baoab_step(p, v, coeffs, grad_prev, gamma):\n", "#     \"\"\"\n", "#     One BAOAB update (unperturbed).\n", "#     p, v: shape (n_samples, 2).\n", "#     grad_prev: shape (n_samples, 2) from the previous iteration.\n", "#     returns: new grad (for next iteration).\n", "#     \"\"\"\n", "#     with torch.no_grad():\n", "#         # noise for position update\n", "#         xi1 = torch.randn_like(p)  # shape (n_samples, 2)\n", "\n", "#         # position update\n", "#         p = p + coeffs.xc1 * v - coeffs.xc2 * grad_prev + coeffs.xc3 * xi1\n", "\n", "#         # new gradient\n", "#         grad_new = grad_potential_2d(p)\n", "\n", "#         # velocity update\n", "#         v = coeffs.eta * v - coeffs.vc1 * grad_prev - coeffs.vc2 * grad_new + coeffs.vc3 * xi1\n", "\n", "#     return p, v, grad_new\n", "\n", "\n", "# ##############################################\n", "# # 4. Perturbed BAOAB Step (Add gradient noise)\n", "# ##############################################\n", "\n", "# def perturbed_baoab_step(p, v, coeffs, grad_prev, gamma, noise_sd):\n", "#     \"\"\"\n", "#     One BAOAB update but with Gaussian noise added to the gradient.\n", "#     p, v: shape (n_samples, 2).\n", "#     noise_sd: scalar float\n", "#     returns: new grad (for next iteration).\n", "#     \"\"\"\n", "#     with torch.no_grad():\n", "#         # noise for position update\n", "#         xi1 = torch.randn_like(p)\n", "#         # noise for gradient\n", "#         gnoise1 = torch.randn_like(p) * noise_sd\n", "#         gnoise2 = torch.randn_like(p) * noise_sd\n", "\n", "#         # position update (including gradient perturbation)\n", "#         p = p + coeffs.xc1 * v - coeffs.xc2 * grad_prev + coeffs.xc3 * xi1 + coeffs.xc2 * gnoise1\n", "\n", "#         # new gradient + perturbation\n", "#         grad_new = grad_potential_2d(p) + gnoise2\n", "\n", "#         # velocity update\n", "#         v = coeffs.eta * v \\\n", "#             - coeffs.vc1 * grad_prev \\\n", "#             - coeffs.vc2 * grad_new \\\n", "#             + coeffs.vc3 * xi1 \\\n", "#             + coeffs.vc1 * gnoise1\n", "\n", "#     return p, v, grad_new\n", "\n", "\n", "# ##############################################\n", "# # 5. Simple Distribution Distance in 2D\n", "# ##############################################\n", "\n", "# def average_l2_distance(samples1, samples2):\n", "#     \"\"\"\n", "#     A simple measure of how far apart two sets of points are on average.\n", "#     samples1, samples2: shape (n_samples, 2)\n", "#     Return scalar.\n", "#     \"\"\"\n", "#     return (samples1 - samples2).norm(dim=1).mean()\n", "\n", "# #\n", "# # For a 'proper' 2D Wasserstein distance, consider using\n", "# # the Python Optimal Transport (POT) library, e.g.\n", "# #\n", "# #   pip install pot\n", "# #\n", "# # then use ot.emd2(...) or ot.sinkhorn(...) to compute 2D Wasserstein.\n", "# #\n", "\n", "# ##############################################\n", "# # 6. Example <PERSON><PERSON><PERSON> to Run a BAOAB (or Perturbed) Chain\n", "# ##############################################\n", "\n", "# def run_baoab(num_steps, h, gamma, noise_sd=0.0, n_chains=10000):\n", "#     \"\"\"\n", "#     Run either unperturbed or perturbed BAOAB on the 2D Neal potential,\n", "#     for `num_steps` steps, with step size h and friction gamma.\n", "\n", "#     If noise_sd > 0, we use 'perturbed_baoab_step' else 'baoab_step'.\n", "#     Returns samples at each step as a 3D tensor [num_steps, n_chains, 2].\n", "#     \"\"\"\n", "#     # Make coefficient object\n", "#     coeffs = make_baoab_coeffs(h, gamma)\n", "\n", "#     # Initialize (x,theta) and velocities\n", "#     p = torch.zeros(n_chains, 2, device=device)\n", "#     v = torch.randn(n_chains, 2, device=device)\n", "\n", "#     # pre-compute gradient\n", "#     grad_prev = grad_potential_2d(p)\n", "\n", "#     # Save trajectory\n", "#     traj = torch.zeros(num_steps, n_chains, 2, device=device)\n", "\n", "#     # Choose which stepping function\n", "#     # step_fn = perturbed_baoab_step if noise_sd > 0 else baoab_step\n", "#     step_fn = perturbed_baoab_step\n", "\n", "#     for step in range(num_steps):\n", "#         p, v, grad_prev = step_fn(p, v, coeffs, grad_prev, gamma, noise_sd)\n", "#         traj[step] = p\n", "\n", "#     return traj\n", "\n", "\n", "# ##############################################\n", "# # 7. Putting It All Together:\n", "# #    Testing multiple h and noise_sd\n", "# ##############################################\n", "\n", "# def main():\n", "#     # Some choices\n", "#     steps_list = [1000, 2000]        # how many integrator steps to run\n", "#     h_list = [0.01, 0.02, 0.05]      # step sizes\n", "#     noise_list = [0.0, 0.01, 0.05, 0.1, 0.5, 1, 2]   # gradient noise\n", "#     gamma = 1.0\n", "#     n_chains = 5000\n", "\n", "#     # For reference, let's say we want to compare to \"true\" samples\n", "#     # from some known distribution. If we want to compare with\n", "#     # a 'target' distribution, we can e.g. do a very long run with no perturbation:\n", "#     ref_long_run = run_baoab(num_steps=50000, h=0.005, gamma=gamma, noise_sd=0.0, n_chains=n_chains)\n", "#     reference_samples = ref_long_run[-1].detach()  # last step of the chain\n", "\n", "#     # We'll store results\n", "#     results = []\n", "\n", "#     for num_steps in steps_list:\n", "#         for h in h_list:\n", "#             for noise_sd in noise_list:\n", "#                 # Run BAOAB or perturbed for (num_steps, h, noise_sd)\n", "#                 traj = run_baoab(num_steps, h, gamma, noise_sd=noise_sd, n_chains=n_chains)\n", "#                 final_samples = traj[-1].detach()  # shape (n_chains, 2)\n", "\n", "#                 # measure difference from reference, e.g. average L2\n", "#                 dist = average_l2_distance(final_samples, reference_samples)\n", "#                 results.append((num_steps, h, noise_sd, float(dist)))\n", "#                 print(f\"Steps={num_steps}, h={h}, noise_sd={noise_sd}, L2 Dist={dist:.4f}\")\n", "\n", "#     # Show all results\n", "#     print(\"\\nSummary of runs:\")\n", "#     for (num_steps, h, nsd, dist) in results:\n", "#         print(f\"  steps={num_steps}, h={h}, noise_sd={nsd}, dist={dist:.4f}\")\n", "\n", "\n", "# if __name__ == \"__main__\":\n", "#     main()"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}