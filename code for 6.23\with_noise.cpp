// baoab_ubu_export.cpp
#include <cmath>
#include <random>
#include <vector>
#include <string>
#include <fstream>
#include <iostream>

// --- simple 2D vector class ---------------------------------------------
struct Vector2D {
    double x, y;
    Vector2D(double _x=0, double _y=0): x(_x), y(_y) {}
    Vector2D& operator+=(const Vector2D& o) { x+=o.x; y+=o.y; return *this; }
    Vector2D& operator-=(const Vector2D& o) { x-=o.x; y-=o.y; return *this; }
    Vector2D& operator*=(double s)       { x*=s;   y*=s;   return *this; }
    Vector2D operator+(const Vector2D& o) const { return {x+o.x, y+o.y}; }
    Vector2D operator-(const Vector2D& o) const { return {x-o.x, y-o.y}; }
    Vector2D operator*(double s)         const { return {x*s, y*s}; }
    Vector2D operator/(double s)         const { return {x/s, y/s}; }
};
inline Vector2D operator*(double s, const Vector2D& v) { return {v.x*s, v.y*s}; }

// --- entropic potential and gradients -----------------------------------
// double U_entropy(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     return 100.0*y*y/(1.0 + 10.0*std::pow(x,4))
//          + 0.001 * std::pow(x*x - 9.0, 2.0);
// }
double U_funnel(const Vector2D& p) {
    double x = p.x, y = p.y;
    return 0.5*x*x*std::exp(-y) + 0.5*0.1*(x*x + y*y);
}

// Vector2D grad_entropy(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     double denom = 1.0 + 10.0*std::pow(x,4);
//     double gx = -4000.0*std::pow(x,3)*y*y / std::pow(denom,2)
//                 + 0.004*x*(x*x - 9.0);
//     double gy = 200.0*y / denom;
//     return {gx, gy};
// }
Vector2D grad_funnel(const Vector2D& p, double eps = 0.01) {
    double x = p.x, y = p.y;
    double gx = x*std::exp(-y) + eps*x;
    double gy = -0.5*x*x*std::exp(-y) + eps*y;
    return {gx, gy};
}

// Vector2D perturbed_grad_entropy(const Vector2D& p, std::mt19937_64& gen) {
//     auto g = grad_entropy(p);
//     std::normal_distribution<> N(0.0,1.0);
//     Vector2D r{N(gen), N(gen)};
//     double s = r.x + r.y;
//     r = r / s;
//     return {r.x * g.x, r.y * g.y};
// }
Vector2D perturbed_grad_funnel(const Vector2D& p, std::mt19937_64& gen) {
    auto g = grad_funnel(p);
    std::normal_distribution<> N(0.0,1.0);
    Vector2D r{N(gen), N(gen)};
    double s = r.x + r.y;
    r = r / s;
    return {r.x * g.x, r.y * g.y};
}

// --- BAOAB params & steps ------------------------------------------------
struct BAOABParams {
    double h, eta, xc1, xc2, xc3, vc1, vc2, vc3;
};
BAOABParams makeBAOAB(double h, double gam, double T=1.0) {
    double gh = gam*h;
    double eta = std::exp(-gh);
    BAOABParams P;
    P.h   = h;
    P.eta = eta;
    P.xc1 = h/2.0*(1.0 + eta);
    P.xc2 = h*h/4.0*(1.0 + eta);
    P.xc3 = h/2.0 * std::sqrt(-std::expm1(-2.0*gh)*T);
    P.vc1 = eta*(h/2.0);
    P.vc2 = h/2.0;
    P.vc3 = std::sqrt(-std::expm1(-2.0*gh)*T);
    return P;
}
Vector2D BAOAB_step(Vector2D& pos, Vector2D& vel,
                    const BAOABParams& P,
                    const Vector2D& last_grad,
                    std::mt19937_64& gen)
{
    std::normal_distribution<> N(0.0,1.0);
    Vector2D xi{N(gen), N(gen)};
    pos += P.xc1*vel - P.xc2*last_grad + P.xc3*xi;
    // auto grad = grad_entropy(pos);
    auto grad = grad_funnel(pos);
    vel = P.eta*vel - P.vc1*last_grad - P.vc2*grad + P.vc3*xi;
    return grad;
}
Vector2D pBAOAB_step(Vector2D& pos, Vector2D& vel,
                     const BAOABParams& P,
                     const Vector2D& last_grad,
                     std::mt19937_64& gen)
{
    std::normal_distribution<> N(0.0,1.0);
    Vector2D xi{N(gen), N(gen)};
    pos += P.xc1*vel - P.xc2*last_grad + P.xc3*xi;
    // auto grad = perturbed_grad_entropy(pos, gen);
    auto grad = perturbed_grad_funnel(pos, gen);
    vel = P.eta*vel - P.vc1*last_grad - P.vc2*grad + P.vc3*xi;
    return grad;
}
void run_BAOAB(int K, double h, double gam, double T, bool perturbed,
               std::vector<Vector2D>& out_pos,
               std::vector<double>& out_Umean)
{
    std::mt19937_64 gen(42);
    BAOABParams P = makeBAOAB(h,gam,T);
    // Vector2D pos{3.0, 0.0}, vel{0.0,0.0};
    Vector2D pos{0.0, 5.0}, vel{0.0,0.0};
    std::normal_distribution<> N(0.0,1.0);
    vel = {N(gen), N(gen)};
    Vector2D grad = perturbed
                //   ? perturbed_grad_entropy(pos, gen)
                //   : grad_entropy(pos);
                ? perturbed_grad_funnel(pos, gen)
                : grad_funnel(pos);
    out_pos.resize(K);
    out_Umean.resize(K);
    // double running_mean = U_entropy(pos);
    double running_mean = U_funnel(pos);
    for(int i=0; i<K; ++i){
        grad = perturbed
             ? pBAOAB_step(pos, vel, P, grad, gen)
             : BAOAB_step(pos, vel, P, grad, gen);
        out_pos[i] = pos;
        // double Ui = U_entropy(pos);
        double Ui = U_funnel(pos);
        running_mean = (i*running_mean + Ui)/(i+1);
        out_Umean[i] = running_mean;
    }
}

// --- UBU params & steps --------------------------------------------------
struct UBUParams {
    double h, expm, noise_scale;
};
UBUParams makeUBU(double h, double gam, double T=1.0) {
    double half = h/2.0;
    double gh2  = gam*half;
    UBUParams P;
    P.h           = h;
    P.expm       = std::exp(-gh2);
    P.noise_scale = std::sqrt(-std::expm1(-2.0*gh2)*T);
    return P;
}
void U_step(Vector2D& pos, Vector2D& vel,
            const UBUParams& P,
            const Vector2D& xi1,
            const Vector2D& xi2)
{
    // OU half-step
    vel.x = P.expm * vel.x + P.noise_scale * xi1.x;
    vel.y = P.expm * vel.y + P.noise_scale * xi1.y;
    // free drift A for full h
    pos.x += P.h * vel.x;
    pos.y += P.h * vel.y;
    // OU half-step
    vel.x = P.expm * vel.x + P.noise_scale * xi2.x;
    vel.y = P.expm * vel.y + P.noise_scale * xi2.y;
}
void run_UBU(int K, double h, double gam, bool perturbed,
             std::vector<Vector2D>& out_pos,
             std::vector<double>& out_Umean)
{
    std::mt19937_64 gen(1337);
    std::normal_distribution<> N(0.0,1.0);
    UBUParams P = makeUBU(h,gam,1.0);
    // Vector2D pos{3.0,0.0}, vel{N(gen),N(gen)};
    Vector2D pos{0.0, 5.0}, vel{N(gen),N(gen)};
    out_pos.resize(K);
    out_Umean.resize(K);
    // double running_mean = U_entropy(pos);
    double running_mean = U_funnel(pos);
    for(int i=0; i<K; ++i){
        // first U half-step
        Vector2D xi1{N(gen),N(gen)}, xi2{N(gen),N(gen)};
        U_step(pos, vel, P, xi1, xi2);
        // B full-step: gradient kick
        Vector2D grad = perturbed
                //   ? perturbed_grad_entropy(pos, gen)
                //   : grad_entropy(pos);
                ? perturbed_grad_funnel(pos, gen)
                : grad_funnel(pos);
        vel -= P.h * grad;
        // second U half-step
        xi1 = {N(gen),N(gen)};
        xi2 = {N(gen),N(gen)};
        U_step(pos, vel, P, xi1, xi2);
        // record
        out_pos[i] = pos;
        // double Ui = U_entropy(pos);
        double Ui = U_funnel(pos);
        running_mean = (i*running_mean + Ui)/(i+1);
        out_Umean[i] = running_mean;
    }
}

// --- main: run, write to file --------------------------------------------
int main(){
    int    num_epochs = 10000000;
    double h          = 0.01;
    double T          = 1;
    double l2reg      = 25.0;
    double gam        = std::sqrt(l2reg);

    std::string method= "BAOAB";   // "BAOAB" or "UBU"

    std::vector<Vector2D> V, Vp;
    std::vector<double>    U, Up;

    if(method == "BAOAB"){
        run_BAOAB(num_epochs, h, gam, T, false, V,  U);
        run_BAOAB(num_epochs, h, gam, T, true,  Vp, Up);
    } else {
        run_UBU(num_epochs, h, gam, false, V,  U);
        run_UBU(num_epochs, h, gam, true,  Vp, Up);
    }

    std::string fn = (method=="BAOAB"
        ? "perturbed_BAOAB.txt"
        : "perturbed_UBU.txt");
    std::ofstream out(fn);
    out << "# Vx    Vy    Umean    V1x    V1y    Umean1\n";
    for(int i=0; i<num_epochs; ++i){
        out
          << V[i].x  << ' ' << V[i].y  << ' ' << U[i]  << ' '
          << Vp[i].x << ' ' << Vp[i].y << ' ' << Up[i]
          << '\n';
    }
    out.close();
    std::cout<<"Saved arrays to "<<fn<<"\n";
    return 0;
}
