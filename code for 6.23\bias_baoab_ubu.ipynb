{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9e454779-aa22-49f5-9464-bf9aa9c96b6c", "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "markdown", "id": "2d373806-54fb-4bce-9c56-d4edd92582c7", "metadata": {}, "source": ["## BAOAB and perturbed_BAOAB"]}, {"cell_type": "code", "execution_count": 2, "id": "a6110bd7-19a0-45dd-b1c9-24790c4c91d7", "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for BAOAB\n", "@dataclass\n", "class BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "# parameters for perturbed BAOAB\n", "@dataclass\n", "class perturbed_BAOABhclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "    xcn1: Tensor # perturbation on x (parameter for gaussian noise 1)\n", "    vcn1: Tensor # perturbation on v (parameter for gaussian noise 1)\n", "    vcn2: Tensor # perturbation on v (parameter for gaussian noise 2)\n", "\n", "# constant parameters for BAOAB\n", "def BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)\n", "    \n", "# constant parameters for perturbed BAOAB\n", "def perturbed_BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "\n", "        xcn1=(hh*hh/4)*(1+eta)\n", "        vcn1=eta*(hh/2)\n", "        vcn2=(hh/2)\n", "\n", "        hc=perturbed_BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float(),xcn1=xcn1.float(),vcn1=vcn1.float(),vcn2=vcn2.float())\n", "        return(hc)"]}, {"cell_type": "code", "execution_count": 3, "id": "fb617645-131b-4ceb-a592-2b1124f09b6f", "metadata": {}, "outputs": [], "source": ["no_batches=torch.tensor(2,device=device)      # Nm in paper\n", "batch_size=1\n", "par_runs=20000\n", "X=torch.tensor([[-1, 1], [0.5, 2.0]],device=device) #X[0,:] denotes mean, X[1, :] denotes standard deviation\n", "target_mean=(X[0,:]*(X[1,:].pow(-2))).sum()/(X[1,:].pow(-2).sum())\n", "target_sd=(X[1,:].pow(-2)).sum().pow(-0.5)\n", "l2regconst=torch.tensor(1,device=device).detach()\n", "gam=torch.sqrt(l2regconst)\n", "\n", "def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "\n", "\n", "def BAOAB_step(p,hc,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "    return(grads)     \n", "\n", "def perturbed_BAOAB_step(p,hc,batch_it,last_grad,noise_sd):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        noise1=torch.randn_like(p.data,device=device)*noise_sd\n", "        noise2=torch.randn_like(p.data,device=device)*noise_sd\n", "\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1+hc.xcn1*noise1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1+hc.vcn1*noise1+hc.vcn2*noise2\n", "\n", "    return(grads) \n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "  V1s,_=torch.sort(V1.flatten())\n", "  V2s,_=torch.sort(V2.flatten())\n", "  return (V1s-V2s).abs().mean()"]}, {"cell_type": "code", "execution_count": 4, "id": "47b74149-0874-44e0-a3af-1b2f91e75812", "metadata": {}, "outputs": [], "source": ["def SMS_BAOAB(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SMS_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "      rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "      if i==0:\n", "        grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        grads = BAOAB_step(p, hper2c, rng_perm[:,k+1], grads)\n", "\n", "        V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "      for k in range(n_bw):\n", "        grads = BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads)\n", "\n", "        V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "      # V_epoch_arr[:,i]=V_arr.mean(dim=0)\n", "      # V_epoch_arr[:,i]=p.data.clone()\n", "\n", "  return(V_arr)\n", "\n", "def perturbed_SMS_BAOAB(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SMS_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    hper2c=perturbed_BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "      rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      \n", "      if i==0:\n", "        grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,k+1], grads, noise_sd)\n", "\n", "        V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "      for k in range(n_bw):\n", "        grads = perturbed_BAOAB_step(p, hper2c, rng_perm[:,Nm-1-k], grads, noise_sd)\n", "\n", "        V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "  return(V_arr)\n", "\n", "\n", "def SG_BAOAB_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      grads=BAOAB_step(p,hper2c,ind,grads)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def perturbed_SG_BAOAB_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_BAOAB\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=perturbed_BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      grads=perturbed_BAOAB_step(p,hper2c,ind,grads,noise_sd)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)"]}, {"cell_type": "code", "execution_count": 5, "id": "d084a760-24b3-4529-9662-9d30130ac75f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.02076351\n", "Wasserstein distance: 0.03355240\n", "Wasserstein distance: 0.00628609\n", "Wasserstein distance: 0.00617938\n", "Wasserstein distance: 0.00136030\n", "Wasserstein distance: 0.00173983\n", "Wasserstein distance: 0.00050120\n", "Wasserstein distance: 0.00037291\n"]}, {"data": {"text/plain": ["tensor([[0.0208, 0.0336],\n", "        [0.0063, 0.0062],\n", "        [0.0014, 0.0017],\n", "        [0.0005, 0.0004]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr=torch.zeros(4,2).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for it in range(4):\n", "    for mit in range(2):\n", "        rat=pow(2,it)\n", "        num_epochs=max(int(2000*rat), 2000)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "Wass_arr"]}, {"cell_type": "code", "execution_count": 8, "id": "29d01796-f0f5-4999-8ab3-1e79505b6589", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 18.14297485\n", "Wasserstein distance: 102.72009277\n"]}, {"data": {"text/plain": ["tensor([[1.8143e+01, 1.0272e+02],\n", "        [2.0764e-02, 3.3552e-02],\n", "        [6.2861e-03, 6.1794e-03],\n", "        [1.3603e-03, 1.7398e-03],\n", "        [5.0120e-04, 3.7291e-04]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -1\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=int(2000*rat)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,200:],V2_arr[:,200:])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "Wass_arr = torch.cat((new_row, Wass_arr), dim=0)\n", "Wass_arr"]}, {"cell_type": "code", "execution_count": 9, "id": "dea4ea95-5ef1-4c74-a5ba-72b09bca0485", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: nan\n", "Wasserstein distance: nan\n"]}, {"data": {"text/plain": ["tensor([[       nan,        nan],\n", "        [1.8143e+01, 1.0272e+02],\n", "        [2.0764e-02, 3.3552e-02],\n", "        [6.2861e-03, 6.1794e-03],\n", "        [1.3603e-03, 1.7398e-03],\n", "        [5.0120e-04, 3.7291e-04]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -2\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=int(2000*rat)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,int(200*rat):],V2_arr[:,int(200*rat):])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "Wass_arr = torch.cat((new_row, Wass_arr), dim=0)\n", "Wass_arr"]}, {"cell_type": "code", "execution_count": 10, "id": "febd762c-97d5-4e54-90b2-d43955ab94fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: nan\n", "Wasserstein distance: nan\n"]}, {"data": {"text/plain": ["tensor([[nan, nan]])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -1.5\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=int(2000*rat)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,int(200*rat):],V2_arr[:,int(200*rat):])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "new_row"]}, {"cell_type": "code", "execution_count": 17, "id": "ecf144f8-3df1-43d9-810c-2f21a56e2296", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: nan\n", "Wasserstein distance: 104483.25781250\n"]}, {"data": {"text/plain": ["tensor([[        nan, 104483.2578]])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -1.1\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=max(int(2000*rat), 2000)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,int(200*rat):],V2_arr[:,int(200*rat):])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "new_row"]}, {"cell_type": "code", "execution_count": 16, "id": "6f602cc5-db27-44dc-a095-63e8daaafa32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: nan\n", "Wasserstein distance: 20.25315094\n"]}, {"data": {"text/plain": ["tensor([[    nan, 20.2532]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -1.2\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=max(int(2000*rat), 2000)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,int(200*rat):],V2_arr[:,int(200*rat):])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "new_row"]}, {"cell_type": "code", "execution_count": 19, "id": "93848b2e-7a84-4319-832c-bcdf4ea12359", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: nan\n", "Wasserstein distance: nan\n"]}, {"data": {"text/plain": ["tensor([[nan, nan]])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -1.25\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=max(int(2000*rat), 2000)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,int(200*rat):],V2_arr[:,int(200*rat):])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "new_row"]}, {"cell_type": "code", "execution_count": 18, "id": "781053bb-3d16-4e87-89e2-07ae92c62a82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: nan\n", "Wasserstein distance: nan\n"]}, {"data": {"text/plain": ["tensor([[nan, nan]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["new_row = torch.zeros(1,2).detach()\n", "it = -1.3\n", "for mit in range(2):\n", "    rat=pow(2,it)\n", "    num_epochs=max(int(2000*rat), 2000)\n", "    h=torch.tensor(0.25)/rat\n", "    V_arr=methods_list[mit](num_epochs,h,gam)\n", "    \n", "    V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "    diff=Wass(V_arr[:,int(200*rat):],V2_arr[:,int(200*rat):])\n", "    print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "    new_row[0, mit]=diff\n", "\n", "new_row"]}, {"cell_type": "code", "execution_count": null, "id": "8a064a02-8ac8-4603-812e-9d573324b180", "metadata": {}, "outputs": [], "source": ["Wass_arr=torch.zeros(4,2).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for it in range(4):\n", "    for mit in range(2):\n", "        rat=pow(2,it)\n", "        num_epochs=max(int(2000*rat), 2000)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff\n", "\n", "Wass_arr"]}, {"cell_type": "code", "execution_count": 22, "id": "f65eff20-f8b4-429b-bf2c-8cd0cfeaa813", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.00000000e+00, -9.00000000e-01, -8.00000000e-01, -7.00000000e-01,\n", "       -6.00000000e-01, -5.00000000e-01, -4.00000000e-01, -3.00000000e-01,\n", "       -2.00000000e-01, -1.00000000e-01, -2.22044605e-16,  1.00000000e-01,\n", "        2.00000000e-01,  3.00000000e-01,  4.00000000e-01,  5.00000000e-01,\n", "        6.00000000e-01,  7.00000000e-01,  8.00000000e-01,  9.00000000e-01,\n", "        1.00000000e+00,  1.10000000e+00,  1.20000000e+00,  1.30000000e+00,\n", "        1.40000000e+00,  1.50000000e+00,  1.60000000e+00,  1.70000000e+00,\n", "        1.80000000e+00,  1.90000000e+00,  2.00000000e+00,  2.10000000e+00,\n", "        2.20000000e+00,  2.30000000e+00,  2.40000000e+00,  2.50000000e+00,\n", "        2.60000000e+00,  2.70000000e+00,  2.80000000e+00,  2.90000000e+00,\n", "        3.00000000e+00])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["np.arange(-1, 3.1, 0.1)"]}, {"cell_type": "code", "execution_count": 26, "id": "5259ebd9-1126-48f6-9fa0-612a04afaf66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["When it = -1.00, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 59.53687286\n", "When it = -1.00, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 2316.95556641\n", "When it = -0.90, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.22683463\n", "When it = -0.90, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.65995741\n", "When it = -0.80, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.10423503\n", "When it = -0.80, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.30481124\n", "When it = -0.70, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.06205498\n", "When it = -0.70, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.19312909\n", "When it = -0.60, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.04378996\n", "When it = -0.60, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.13532633\n", "When it = -0.50, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.03488601\n", "When it = -0.50, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.10126749\n", "When it = -0.40, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.02979633\n", "When it = -0.40, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.08041760\n", "When it = -0.30, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.02636335\n", "When it = -0.30, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.06435148\n", "When it = -0.20, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.02404292\n", "When it = -0.20, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.05171755\n", "When it = -0.10, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.02237202\n", "When it = -0.10, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.04164675\n", "When it = -0.00, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.02072354\n", "When it = -0.00, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.03365855\n", "When it = 0.10, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.01907658\n", "When it = 0.10, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.02766974\n", "When it = 0.20, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.01740200\n", "When it = 0.20, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.02276491\n", "When it = 0.30, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.01579440\n", "When it = 0.30, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.01895424\n", "When it = 0.40, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.01422308\n", "When it = 0.40, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.01593710\n", "When it = 0.50, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.01258966\n", "When it = 0.50, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.01353847\n", "When it = 0.60, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.01117610\n", "When it = 0.60, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.01158397\n", "When it = 0.70, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00964338\n", "When it = 0.70, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00948685\n", "When it = 0.80, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00847271\n", "When it = 0.80, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00839168\n", "When it = 0.90, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00749908\n", "When it = 0.90, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00725013\n", "When it = 1.00, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00661438\n", "When it = 1.00, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00606699\n", "When it = 1.10, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00535638\n", "When it = 1.10, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00517684\n", "When it = 1.20, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00498562\n", "When it = 1.20, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00449673\n", "When it = 1.30, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00408676\n", "When it = 1.30, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00407361\n", "When it = 1.40, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00355536\n", "When it = 1.40, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00355915\n", "When it = 1.50, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00308295\n", "When it = 1.50, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00305169\n", "When it = 1.60, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00261322\n", "When it = 1.60, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00255444\n", "When it = 1.70, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00243548\n", "When it = 1.70, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00226273\n", "When it = 1.80, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00195263\n", "When it = 1.80, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00222930\n", "When it = 1.90, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00173631\n", "When it = 1.90, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00182224\n", "When it = 2.00, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00143147\n", "When it = 2.00, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00154987\n", "When it = 2.10, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00143340\n", "When it = 2.10, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00142571\n", "When it = 2.20, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00113706\n", "When it = 2.20, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00126026\n", "When it = 2.30, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00095589\n", "When it = 2.30, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00099366\n", "When it = 2.40, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00090523\n", "When it = 2.40, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00088486\n", "When it = 2.50, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00061922\n", "When it = 2.50, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00069308\n", "When it = 2.60, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00053059\n", "When it = 2.60, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00071950\n", "When it = 2.70, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00068139\n", "When it = 2.70, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00064979\n", "When it = 2.80, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00065218\n", "When it = 2.80, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00043395\n", "When it = 2.90, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00045522\n", "When it = 2.90, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00048107\n", "When it = 3.00, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 0.00045418\n", "When it = 3.00, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 0.00032945\n"]}, {"data": {"text/plain": ["tensor([[5.9537e+01, 2.3170e+03],\n", "        [2.2683e-01, 6.5996e-01],\n", "        [1.0424e-01, 3.0481e-01],\n", "        [6.2055e-02, 1.9313e-01],\n", "        [4.3790e-02, 1.3533e-01],\n", "        [3.4886e-02, 1.0127e-01],\n", "        [2.9796e-02, 8.0418e-02],\n", "        [2.6363e-02, 6.4351e-02],\n", "        [2.4043e-02, 5.1718e-02],\n", "        [2.2372e-02, 4.1647e-02],\n", "        [2.0724e-02, 3.3659e-02],\n", "        [1.9077e-02, 2.7670e-02],\n", "        [1.7402e-02, 2.2765e-02],\n", "        [1.5794e-02, 1.8954e-02],\n", "        [1.4223e-02, 1.5937e-02],\n", "        [1.2590e-02, 1.3538e-02],\n", "        [1.1176e-02, 1.1584e-02],\n", "        [9.6434e-03, 9.4869e-03],\n", "        [8.4727e-03, 8.3917e-03],\n", "        [7.4991e-03, 7.2501e-03],\n", "        [6.6144e-03, 6.0670e-03],\n", "        [5.3564e-03, 5.1768e-03],\n", "        [4.9856e-03, 4.4967e-03],\n", "        [4.0868e-03, 4.0736e-03],\n", "        [3.5554e-03, 3.5592e-03],\n", "        [3.0830e-03, 3.0517e-03],\n", "        [2.6132e-03, 2.5544e-03],\n", "        [2.4355e-03, 2.2627e-03],\n", "        [1.9526e-03, 2.2293e-03],\n", "        [1.7363e-03, 1.8222e-03],\n", "        [1.4315e-03, 1.5499e-03],\n", "        [1.4334e-03, 1.4257e-03],\n", "        [1.1371e-03, 1.2603e-03],\n", "        [9.5589e-04, 9.9366e-04],\n", "        [9.0523e-04, 8.8486e-04],\n", "        [6.1922e-04, 6.9308e-04],\n", "        [5.3059e-04, 7.1950e-04],\n", "        [6.8139e-04, 6.4979e-04],\n", "        [6.5218e-04, 4.3395e-04],\n", "        [4.5522e-04, 4.8107e-04],\n", "        [4.5418e-04, 3.2945e-04]])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr=torch.zeros(np.arange(-1, 3.1, 0.1).size, 2).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for ind, it in enumerate(np.arange(-1, 3.1, 0.1)):\n", "    for mit in range(2):\n", "        rat=pow(2,it)\n", "        num_epochs=max(int(2000*rat), 2000)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,min(int(200*rat), 200):],V2_arr[:,min(int(200*rat), 200):])\n", "        print(f\"When it = {it:.2f}, Using {methods_list[mit]}, <PERSON><PERSON><PERSON> distance: {diff:.8f}\")\n", "        Wass_arr[ind,mit]=diff\n", "\n", "Wass_arr"]}, {"cell_type": "code", "execution_count": 58, "id": "3e0b944c-9ee1-46a5-bd61-08930cae12f3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1172/*********.py:24: UserWarning: Attempt to set non-positive ylim on a log-scaled axis will be ignored.\n", "  ax.set_ylim(-10, 20)\n"]}, {"data": {"image/png": "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**************************************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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "exponents = -np.arange(-1, 3.1, 0.1)\n", "stepsizes = 2**exponents\n", "\n", "fig, ax = plt.subplots(figsize=(6, 4))\n", "ax.loglog(stepsizes, Wass_arr[:,0], 'b-', markersize=4, linewidth=2, label='SMS BAOAB', base=2)\n", "ax.loglog(stepsizes, Wass_arr[:,1], 'r-', markersize=4, linewidth=2, label='SG BAOAB', base=2)\n", "\n", "# ax.loglog(stepsizes, baoab08[:,0], 'o--', markersize=2, linewidth=2, label='SMS BAOAB with noise', base=2)\n", "# ax.loglog(stepsizes, baoab08[:,1], '^--', markersize=2, linewidth=2, label='SG BAOAB with noise', base=2)\n", "\n", "# plt.fill_between(stepsizes, norm_baoab[:,0], baoab08[:,0],  alpha=0.3)\n", "# plt.fill_between(stepsizes, norm_baoab[:,1], baoab08[:,1], color='bisque', alpha=0.5)\n", "\n", "\n", "def exponent_formatter(x, pos):\n", "    exp = np.log2(x)\n", "    return f\"{int(exp) if exp.is_integer() else round(exp, 2)}\"\n", "\n", "ax.xaxis.set_major_formatter(ticker.FuncFormatter(exponent_formatter))\n", "ax.yaxis.set_major_formatter(ticker.FuncFormatter(exponent_formatter))\n", "ax.axvline(x=2**0.9, color='k', linestyle='--', linewidth=1.5, label=r'stepsize $2^{0.9}\\approx1.87$')\n", "ax.set_ylim(-10, 20)\n", "ax.set_xlabel('log₂ h', fontsize=12)\n", "ax.set_ylabel('log₂ Was<PERSON>stein distance', fontsize=12)\n", "ax.set_title('<PERSON><PERSON><PERSON> bias via BAOAB', fontsize=14)\n", "ax.grid(True, which=\"both\", linestyle='--', alpha=0.5)\n", "ax.legend(fontsize=10)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 35, "id": "85153226-26ca-4451-8e15-b8b90ba06ec3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["When h = 0.50, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: 54.39373016\n", "When h = 0.50, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: 5148.97412109\n", "When h = 0.60, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: nan\n", "When h = 0.60, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: nan\n", "When h = 0.70, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: nan\n", "When h = 0.70, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: nan\n", "When h = 0.80, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: nan\n", "When h = 0.80, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: nan\n", "When h = 0.90, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: nan\n", "When h = 0.90, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: nan\n", "When h = 1.00, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: nan\n", "When h = 1.00, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: nan\n", "When h = 1.10, Using <function SMS_BAOAB at 0x7fae69b06c00>, Wasserstein distance: nan\n", "When h = 1.10, Using <function SG_BAOAB_without_replacement at 0x7fae5de90900>, Wasserstein distance: nan\n"]}, {"data": {"text/plain": ["tensor([[  54.3937, 5148.9741],\n", "        [      nan,       nan],\n", "        [      nan,       nan],\n", "        [      nan,       nan],\n", "        [      nan,       nan],\n", "        [      nan,       nan],\n", "        [      nan,       nan]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr_=torch.zeros(np.arange(0.5, 1.1, 0.1).size, 2).detach()\n", "methods_list=[SMS_BAOAB, SG_BAOAB_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for ind, it in enumerate(np.arange(0.5, 1.1, 0.1)):\n", "    for mit in range(2):\n", "        # rat=pow(2,it)\n", "        num_epochs=2000\n", "        h=torch.tensor(it)\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,200:],V2_arr[:,200:])\n", "        print(f\"When h = {it:.2f}, Using {methods_list[mit]}, <PERSON><PERSON><PERSON> distance: {diff:.8f}\")\n", "        Wass_arr_[ind,mit]=diff\n", "\n", "Wass_arr_"]}, {"cell_type": "code", "execution_count": 43, "id": "8297bb93-7ae5-4d45-af1a-48a3bfd5c569", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.8660659830736148"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["2**(0.9)"]}, {"cell_type": "markdown", "id": "4091dbd6-2fa2-429c-adcf-be22a86b70e6", "metadata": {}, "source": ["## UBU and perturbed_UBU"]}, {"cell_type": "code", "execution_count": 46, "id": "3b36658a-a1e5-4575-a7b8-5c0c67a0acc7", "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "# parameters for UBU\n", "@dataclass\n", "class hclass:\n", "    h: Tensor    # step size\n", "    eta: Tensor  # exp(-gamma*h/2)\n", "    etam1g: Tensor  # eta-1/gamma\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "# constant parameters for UBU\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc,xi1,xi2):\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])"]}, {"cell_type": "code", "execution_count": 47, "id": "d2dc9601-d765-472b-a6cf-1f1ec289a7cc", "metadata": {}, "outputs": [], "source": ["def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "\n", "\n", "\n", "def UBU_step(p,hper2c,batch_it):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "    p.v-=hper2c.h*grads\n", "\n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def perturbed_UBU_step(p,hper2c,batch_it,noise_sd):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "        noise=torch.randn_like(p.data,device=device)*noise_sd\n", "        grads=grad(p,batch_it)*no_batches \n", "        p.v-=hper2c.h*grads+hper2c.h*noise\n", "\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "  V1s,_=torch.sort(V1.flatten())\n", "  V2s,_=torch.sort(V2.flatten())\n", "  return (V1s-V2s).abs().mean()"]}, {"cell_type": "code", "execution_count": 48, "id": "0049b313-5a80-4547-8363-d9205a1a0dc6", "metadata": {}, "outputs": [], "source": ["def SMS_UBU(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      UBU_step(p, hper2c, rng_perm[:,k])\n", "      V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      UBU_step(p, hper2c, rng_perm[:,Nm-1-k])\n", "      V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "\n", "    # V_epoch_arr[:,i]=V_arr.mean(dim=0)\n", "    # V_epoch_arr[:,i]=p.data.clone()\n", "\n", "  return(V_arr)\n", "\n", "def perturbed_SMS_UBU(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SMS_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "\n", "    rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "\n", "    # ---------- forward sweep ----------\n", "    n_fw = min(Nm, K - (2*i)*Nm)\n", "    for k in range(n_fw):\n", "      perturbed_UBU_step(p, hper2c, rng_perm[:,k], noise_sd)\n", "      V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "\n", "    # ---------- backward sweep ----------\n", "    n_bw = min(Nm, K - (2*i + 1)*Nm)\n", "    for k in range(n_bw):\n", "      perturbed_UBU_step(p, hper2c, rng_perm[:,Nm-1-k], noise_sd)\n", "      V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "    \n", "    # V_epoch_arr[:,i]=V_arr.mean(dim=0)      \n", "    # V_epoch_arr[:,i]=p.data.clone()\n", "  return(V_arr)\n", "\n", "def SG_UBU_without_replacement(K,h,gam):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      UBU_step(p,hper2c,ind)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def perturbed_SG_UBU_without_replacement(K,h,gam,noise_sd):\n", "  \"\"\"\n", "  K: Total samples after SG_UBU\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # V_epoch_arr=torch.zeros([par_runs,num_epochs],device=device).detach()   # record each epoch after for/backward sweep\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for i in range(num_epochs):\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    n_sw=min(Nm,K-i*Nm)\n", "    for k in range(n_sw):\n", "      ind=rperm[:,k]\n", "      perturbed_UBU_step(p,hper2c,ind,noise_sd)\n", "      V_arr[:,i*Nm+k]=p.data.clone()\n", "\n", "    # with torch.no_grad():\n", "    #   V_epoch_arr[:,i]=p.data\n", "    \n", "  return(V_arr)"]}, {"cell_type": "code", "execution_count": 49, "id": "2d2016a9-c352-417a-ae39-afa8cfcb2fe2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["When it = -1.00, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 3.21097517\n", "When it = -1.00, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 5.17935753\n", "When it = -0.90, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 741110710272.00000000\n", "When it = -0.90, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.48084739\n", "When it = -0.80, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 10770.78613281\n", "When it = -0.80, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.24192309\n", "When it = -0.70, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.77302527\n", "When it = -0.70, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.15158948\n", "When it = -0.60, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.33630273\n", "When it = -0.60, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.10404651\n", "When it = -0.50, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.20749034\n", "When it = -0.50, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.07820629\n", "When it = -0.40, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.15219277\n", "When it = -0.40, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.06207372\n", "When it = -0.30, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.10924931\n", "When it = -0.30, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.04948178\n", "When it = -0.20, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.07671164\n", "When it = -0.20, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.03967655\n", "When it = -0.10, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.05425464\n", "When it = -0.10, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.03210975\n", "When it = -0.00, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.03988431\n", "When it = -0.00, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.02705102\n", "When it = 0.10, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.03030479\n", "When it = 0.10, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.02253501\n", "When it = 0.20, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.02368072\n", "When it = 0.20, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.01907244\n", "When it = 0.30, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.01929132\n", "When it = 0.30, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.01616021\n", "When it = 0.40, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.01609457\n", "When it = 0.40, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.01386301\n", "When it = 0.50, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.01361172\n", "When it = 0.50, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.01203385\n", "When it = 0.60, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.01174912\n", "When it = 0.60, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.01026312\n", "When it = 0.70, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00990773\n", "When it = 0.70, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00896557\n", "When it = 0.80, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00897593\n", "When it = 0.80, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00800015\n", "When it = 0.90, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00768640\n", "When it = 0.90, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00693870\n", "When it = 1.00, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00737120\n", "When it = 1.00, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00611309\n", "When it = 1.10, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00657240\n", "When it = 1.10, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00531808\n", "When it = 1.20, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00579651\n", "When it = 1.20, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00471986\n", "When it = 1.30, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00512950\n", "When it = 1.30, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00408603\n", "When it = 1.40, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00448090\n", "When it = 1.40, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00340130\n", "When it = 1.50, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00419841\n", "When it = 1.50, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00341001\n", "When it = 1.60, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00350740\n", "When it = 1.60, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00276842\n", "When it = 1.70, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00315431\n", "When it = 1.70, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00234579\n", "When it = 1.80, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00295500\n", "When it = 1.80, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00229313\n", "When it = 1.90, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00255521\n", "When it = 1.90, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00192045\n", "When it = 2.00, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00216974\n", "When it = 2.00, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00168060\n", "When it = 2.10, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00173645\n", "When it = 2.10, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00163601\n", "When it = 2.20, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00178596\n", "When it = 2.20, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00126637\n", "When it = 2.30, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00159078\n", "When it = 2.30, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00115109\n", "When it = 2.40, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00111091\n", "When it = 2.40, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00116932\n", "When it = 2.50, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00114047\n", "When it = 2.50, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00086257\n", "When it = 2.60, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00104217\n", "When it = 2.60, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00085079\n", "When it = 2.70, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00080977\n", "When it = 2.70, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00079628\n", "When it = 2.80, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00065632\n", "When it = 2.80, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00072353\n", "When it = 2.90, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00067667\n", "When it = 2.90, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00060744\n", "When it = 3.00, Using <function SMS_UBU at 0x7fadfc5293a0>, Wasserstein distance: 0.00056226\n", "When it = 3.00, Using <function SG_UBU_without_replacement at 0x7fadfc528720>, Wasserstein distance: 0.00029485\n"]}, {"data": {"text/plain": ["tensor([[3.2110e+00, 5.1794e+00],\n", "        [7.4111e+11, 4.8085e-01],\n", "        [1.0771e+04, 2.4192e-01],\n", "        [7.7303e-01, 1.5159e-01],\n", "        [3.3630e-01, 1.0405e-01],\n", "        [2.0749e-01, 7.8206e-02],\n", "        [1.5219e-01, 6.2074e-02],\n", "        [1.0925e-01, 4.9482e-02],\n", "        [7.6712e-02, 3.9677e-02],\n", "        [5.4255e-02, 3.2110e-02],\n", "        [3.9884e-02, 2.7051e-02],\n", "        [3.0305e-02, 2.2535e-02],\n", "        [2.3681e-02, 1.9072e-02],\n", "        [1.9291e-02, 1.6160e-02],\n", "        [1.6095e-02, 1.3863e-02],\n", "        [1.3612e-02, 1.2034e-02],\n", "        [1.1749e-02, 1.0263e-02],\n", "        [9.9077e-03, 8.9656e-03],\n", "        [8.9759e-03, 8.0002e-03],\n", "        [7.6864e-03, 6.9387e-03],\n", "        [7.3712e-03, 6.1131e-03],\n", "        [6.5724e-03, 5.3181e-03],\n", "        [5.7965e-03, 4.7199e-03],\n", "        [5.1295e-03, 4.0860e-03],\n", "        [4.4809e-03, 3.4013e-03],\n", "        [4.1984e-03, 3.4100e-03],\n", "        [3.5074e-03, 2.7684e-03],\n", "        [3.1543e-03, 2.3458e-03],\n", "        [2.9550e-03, 2.2931e-03],\n", "        [2.5552e-03, 1.9204e-03],\n", "        [2.1697e-03, 1.6806e-03],\n", "        [1.7364e-03, 1.6360e-03],\n", "        [1.7860e-03, 1.2664e-03],\n", "        [1.5908e-03, 1.1511e-03],\n", "        [1.1109e-03, 1.1693e-03],\n", "        [1.1405e-03, 8.6257e-04],\n", "        [1.0422e-03, 8.5079e-04],\n", "        [8.0977e-04, 7.9628e-04],\n", "        [6.5632e-04, 7.2353e-04],\n", "        [6.7667e-04, 6.0744e-04],\n", "        [5.6226e-04, 2.9485e-04]])"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr_=torch.zeros(np.arange(-1, 3.1, 0.1).size, 2).detach()\n", "methods_list=[SMS_UBU, SG_UBU_without_replacement, perturbed_SMS_BAOAB, perturbed_SG_BAOAB_without_replacement]\n", "for ind, it in enumerate(np.arange(-1, 3.1, 0.1)):\n", "    for mit in range(2):\n", "        rat=pow(2,it)\n", "        num_epochs=max(int(2000*rat), 2000)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,min(int(200*rat), 200):],V2_arr[:,min(int(200*rat), 200):])\n", "        print(f\"When it = {it:.2f}, Using {methods_list[mit]}, <PERSON><PERSON><PERSON> distance: {diff:.8f}\")\n", "        Wass_arr_[ind,mit]=diff\n", "\n", "Wass_arr_"]}, {"cell_type": "code", "execution_count": 57, "id": "1c49cf9c-2482-42de-b2fb-87ff3045ac43", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1172/1105649592.py:28: UserWarning: Attempt to set non-positive ylim on a log-scaled axis will be ignored.\n", "  ax.set_ylim(-10, 10)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "exponents = -np.arange(-1, 3.1, 0.1)\n", "stepsizes = 2**exponents\n", "\n", "fig, ax = plt.subplots(figsize=(6, 4))\n", "ax.loglog(stepsizes[1:], Wass_arr_[1:,0], 'b-', markersize=4, linewidth=2, label='SMS UBU', base=2)\n", "ax.loglog(stepsizes[:], Wass_arr_[:,1], 'r-', markersize=4, linewidth=2, label='SG UBU', base=2)\n", "\n", "# ax.loglog(stepsizes, baoab08[:,0], 'o--', markersize=2, linewidth=2, label='SMS BAOAB with noise', base=2)\n", "# ax.loglog(stepsizes, baoab08[:,1], '^--', markersize=2, linewidth=2, label='SG BAOAB with noise', base=2)\n", "\n", "# plt.fill_between(stepsizes, norm_baoab[:,0], baoab08[:,0],  alpha=0.3)\n", "# plt.fill_between(stepsizes, norm_baoab[:,1], baoab08[:,1], color='bisque', alpha=0.5)\n", "\n", "\n", "def exponent_formatter(x, pos):\n", "    exp = np.log2(x)\n", "    return f\"{int(exp) if exp.is_integer() else round(exp, 2)}\"\n", "\n", "ax.xaxis.set_major_formatter(ticker.FuncFormatter(exponent_formatter))\n", "ax.yaxis.set_major_formatter(ticker.FuncFormatter(exponent_formatter))\n", "ax.axvline(x=2**0.7, color='k', linestyle='--', linewidth=1.5, label=r'stepsize $2^{0.7}\\approx1.62$')\n", "\n", "ax.set_xlabel('log₂ h', fontsize=12)\n", "ax.set_ylabel('log₂ Was<PERSON>stein distance', fontsize=12)\n", "ax.set_title('<PERSON><PERSON><PERSON> bias via BAOAB', fontsize=14)\n", "ax.set_ylim(-10, 10)\n", "ax.grid(True, which=\"both\", linestyle='--', alpha=0.5)\n", "ax.legend(fontsize=10)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 55, "id": "5dfcd722-08bc-4472-b873-381dd4c73eef", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.624504792712471"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["2**0.7"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}