% % dt vs. (x, y) figure; scatter(x(idx), y(idx), 5, dt(idx), 'filled');
% axis equal xlabel('x'); ylabel('y'); xlim([-10,10]); ylim([-10,10])
% title('\Delta t as a function of (x,y)'); colormap(flipud(turbo))
% colorbar
% 
% saveas(gcf, 'dt_vs_xy.png')

% % Unorm vs. (x, y)
% figure;
% scatter(x(idx), y(idx), 5, Unorm(idx), 'filled');
% axis equal
% xlabel('x'); ylabel('y');
% xlim([-10,10]); ylim([-10,10])
% title('Gradient norm as a function of (x,y)');
% colormap(flipud(turbo))
% colorbar
% 
% saveas(gcf, 'Unorm_vs_xy.png')