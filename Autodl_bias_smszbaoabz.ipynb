{"cells": [{"cell_type": "code", "execution_count": 134, "id": "48071674-d6a6-4b4c-b3b3-b7e7e057a831", "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": 135, "id": "e73ed6c1-bed7-45cc-812a-2ae2aa05312a", "metadata": {}, "outputs": [], "source": ["no_batches=torch.tensor(3,device=device)      # Nm in paper\n", "batch_size=1\n", "par_runs=20000\n", "X=torch.tensor([[-3, 1, 2], [0.5, 1.0, 2.0]],device=device) #X[0,:] denotes mean, X[1, :] denotes standard deviation\n", "target_mean=(X[0,:]*(X[1,:].pow(-2))).sum()/(X[1,:].pow(-2).sum())\n", "target_sd=(X[1,:].pow(-2)).sum().pow(-0.5)\n", "l2regconst=torch.tensor(1,device=device).detach()\n", "gam=torch.sqrt(l2regconst)\n", "\n", "def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]"]}, {"cell_type": "code", "execution_count": 155, "id": "c9048ff6-390f-48ab-a794-fde7227bee72", "metadata": {}, "outputs": [], "source": ["alpha = torch.tensor(1.0, device=device)\n", "Omega = torch.tensor(1.0, device=device)\n", "m = torch.tensor(2**(-2), device=device)\n", "M = torch.tensor(0.5, device=device)\n", "r = torch.tensor(0.25, device=device)"]}, {"cell_type": "code", "execution_count": 156, "id": "20a23773-3a3d-4ee6-b92c-583b43fc78bf", "metadata": {}, "outputs": [], "source": ["def BAOAB_coeffs(h: float, gam: float, device=device):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-2*gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-2*gh))\n", "        \n", "    return tuple(map(lambda v: torch.as_tensor(v, device=device),\n", "                (eta, xc1, xc2, xc3, vc1, vc2, vc3)))\n", "\n", "def BAOAB_step(p,h,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        eta, xc1, xc2, xc3, vc1, vc2, vc3 = BAOAB_coeffs(h, gam)\n", "\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+xc1*p.v-xc2*last_grad+xc3*xi1\n", "\n", "        grads=grad(p,batch_it)*no_batches \n", "\n", "        p.v=eta*p.v-vc1*last_grad-vc2*grads+vc3*xi1\n", "\n", "    return(grads)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind \n", "\n", "def Wass(V1,V2):\n", "    V1s,_=torch.sort(V1.flatten())\n", "    V2s,_=torch.sort(V2.flatten())\n", "    return (V1s-V2s).abs().mean()     "]}, {"cell_type": "code", "execution_count": 157, "id": "9ca8080a-4b7d-476d-abda-3ba46b8e55fc", "metadata": {}, "outputs": [], "source": ["def psi_of_zeta(z, m, M, r):\n", "    \"\"\"\n", "    The Sundman transform kernel, eq. (25) from paper, version (1):\n", "       ψ(1)(ζ) = m * ( ζ^r + M ) / ( ζ^r + m ).\n", "    \"\"\"\n", "    # clamp z so it's never negative or zero\n", "    if z<1e-14:\n", "        z=1e-14\n", "    zr = z**r\n", "    return m*(zr + M)/(zr + m)\n", "\n", "# def psi_of_zeta(z, m, M, r):\n", "#     \"\"\"\n", "#     The Sundman transform kernel, eq. (25) from paper, version (1):\n", "#        ψ(2)(ζ) = m * ( ζ^r + M/m ) / ( ζ^r + 1 ).\n", "#     \"\"\"\n", "#     # clamp z so it's never negative or zero\n", "#     # if z<1e-14:\n", "#     #     z=1e-14\n", "#     zr = z**r\n", "#     return m*(zr + M/m)/(zr + 1)\n", "\n", "def Z(zeta, force_norm, alpha, tau, Omega):\n", "    rho = torch.exp(-alpha * tau)\n", "    coeff = (1.0 - rho) / (Omega * alpha)\n", "    return rho * zeta + coeff * force_norm\n", "\n", "def ZBAOABZ_step(p,zeta,batch_it,last_grad, force_norm, tau):\n", "    zeta = Z(zeta, force_norm, alpha, tau/2, Omega)\n", "    psi = psi_of_zeta(zeta, m, M, r)\n", "    t = psi * tau\n", "    grads = BAOAB_step(p,t,batch_it,last_grad)\n", "    force_norm = torch.norm(grads, 2)**2\n", "    zeta = Z(zeta, force_norm, alpha, tau/2, Omega)\n", "    psi = psi_of_zeta(zeta, m, M, r)\n", "\n", "    return t, zeta, psi, grads, force_norm"]}, {"cell_type": "code", "execution_count": 158, "id": "ad0f1ff7-c467-4604-afbb-729d9a80b976", "metadata": {}, "outputs": [], "source": ["def SMS_ZBAOABZ(K,tau):\n", "  \"\"\"\n", "  K: Total samples after SMS_ZBAOABZ\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + 2*Nm - 1) // (2*Nm)   # ceil(K/2Nm)\n", "  # p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  p=torch.zeros(1,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    # V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # mu_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # t_arr=torch.zeros(par_runs, device=device).detach()\n", "    V_arr=torch.zeros(K,device=device).detach()\n", "    mu_arr=torch.zeros(K,device=device).detach()\n", "    t_arr=np.zeros(K)\n", "    # t_arr=torch.tensor(0.0, device=device).detach()\n", "    p.v = torch.zeros(1,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      # rng_perm = rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1) # w_1, …, w_Nm\n", "      # rng_perm = np.hstack((rng_perm, rng_perm[:, [0]])) # w_(Nm+1) = w_1\n", "      rng_perm = rng.permutation(Nm) # w_1, …, w_Nm\n", "      rng_perm = np.hstack((rng_perm, rng_perm[[0]])) # w_(Nm+1) = w_1\n", "\n", "      if i==0:\n", "        # grads = grad(p.data, rng_perm[:, 0]) * no_batches # k = 0 gradient\n", "        grads = grad(p.data, rng_perm[0]) * no_batches # k = 0 gradient\n", "        force_norm = torch.norm(grads, 2)**2\n", "        zeta = torch.tensor(M*tau,device=device).detach()\n", "\n", "      # ---------- forward sweep ----------\n", "      n_fw = min(Nm, K - (2*i)*Nm)\n", "      for k in range(n_fw):\n", "        # t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[:,k+1], grads, force_norm, tau)\n", "\n", "        # V_arr[:,(2*i)*Nm + k]=p.data.clone()\n", "        # mu_arr[:,(2*i)*Nm + k]=psi.clone()\n", "        # t_arr += t.clone()\n", "        t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[k+1], grads, force_norm, tau)\n", "\n", "        V_arr[(2*i)*Nm + k]=p.data.clone()\n", "        mu_arr[(2*i)*Nm + k]=psi.clone()\n", "        t_arr[(2*i)*Nm + k]=t\n", "        # t_arr += t.clone()\n", "\n", "      # ---------- backward sweep ----------\n", "      n_bw = min(Nm, K - (2*i+1)*Nm)  \n", "      for k in range(n_bw):\n", "        # t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[:,K-k-1], grads, force_norm, tau)\n", "\n", "        # V_arr[:,(2*i+1)*Nm + k]=p.data.clone()\n", "        # mu_arr[:,(2*i+1)*Nm + k]=psi.clone()\n", "        # t_arr += t.clone()\n", "        t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, rng_perm[Nm-1-k], grads, force_norm, tau)\n", "\n", "        V_arr[(2*i+1)*Nm + k]=p.data.clone()\n", "        mu_arr[(2*i+1)*Nm + k]=psi.clone()\n", "        t_arr[(2*i+1)*Nm + k]=t\n", "        # t_arr += t.clone()\n", "\n", "    return V_arr, mu_arr, t_arr\n", "\n", " \n", "def SG_ZBAOABZ_without_replacement(K,tau):\n", "  \"\"\"\n", "  K: Total samples after SG_ZBAOABZ\n", "  no_batches: Nm\n", "  batch_size: Nb, here is 1\n", "  \"\"\"\n", "\n", "  Nm: int = int(no_batches.item())\n", "  num_epochs = (K + Nm - 1) // (Nm)   # ceil(K/Nm)\n", "  # p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  p=torch.zeros(1,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    # V_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # mu_arr=torch.zeros([par_runs,K],device=device).detach()\n", "    # t_arr=torch.zeros(par_runs, device=device).detach()\n", "    V_arr=torch.zeros(K,device=device).detach()\n", "    mu_arr=torch.zeros(K,device=device).detach()\n", "    t_arr=torch.zeros(K,device=device).detach()\n", "    # t_arr=torch.tensor(0.0, device=device).detach()\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "    # ind = torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "    ind = torch.randint(high=no_batches).int()  # random ind from [0, no_batches-1] for each par_runs\n", "    grads = grad(p.data, ind) * no_batches # k = 0 gradient\n", "    force_norm = torch.norm(grads, 2)**2\n", "    zeta = torch.tensor(M*tau,device=device).detach()\n", "\n", "    for i in range(num_epochs):\n", "      # rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "      rperm=rng.permutation(Nm)\n", "      n_sw=min(Nm,K-i*Nm)\n", "      for k in range(n_sw):\n", "        # ind=rperm[:,k]\n", "        # t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, ind, grads, force_norm, tau)\n", "\n", "        # V_arr[:,i*Nm+k]=p.data.clone()\n", "        # mu_arr[:,i*Nm+k]=psi.clone()\n", "        # t_arr += t.clone()\n", "        ind=rperm[k]\n", "        t, zeta, psi, grads, force_norm = ZBAOABZ_step(p, zeta, ind, grads, force_norm, tau)\n", "\n", "        V_arr[i*Nm+k]=p.data.clone()\n", "        mu_arr[i*Nm+k]=psi.clone()\n", "        t_arr[i*Nm+k]=t.clone()\n", "        # t_arr += t.clone()\n", "\n", "    return V_arr, mu_arr, t_arr/K\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 64, "id": "8c940c1d-b825-430c-be89-1dcae34381e0", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running SMS-ZBAOABZ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1192/1441852859.py:33: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  zeta = torch.tensor(M*tau,device=device).detach()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The average stepsize is: 0.010132777504622936\n", "\n", "Calculating Wasserstein distance with reweighting...\n", "Wasserstein distance on reweighted samples: 0.03637053\n"]}], "source": ["if __name__ == '__main__':\n", "    K = 40000\n", "    tau = torch.tensor(1.0, device=device) # Base stepsize in virtual time\n", "\n", "    print(\"Running SMS-ZBAOABZ...\")\n", "    V_arr, mu_arr, t_mean = SMS_ZBAOABZ(K, tau)\n", "    print(f\"The average stepsize is: {t_mean}\")\n", "\n", "    # --- <PERSON><PERSON>tion with Reweighting ---\n", "    print(\"\\nCalculating <PERSON>serstein distance with reweighting...\")\n", "    burn_in = K // 5 # Discard first 20% of samples\n", "    \n", "    # Samples and weights after burn-in\n", "    samples_to_use = V_arr[burn_in:]\n", "    weights_to_use = mu_arr[burn_in:]\n", "\n", "    # Generate target distribution samples\n", "    target_samples = torch.randn_like(samples_to_use, device=device) * target_sd + target_mean\n", "\n", "    # # Perform resampling for each parallel run\n", "    # resampled_list = []\n", "    # for i in range(par_runs):\n", "    #     # Normalize weights for the current run\n", "    #     normalized_weights = weights_to_use[i] / torch.sum(weights_to_use[i])\n", "        \n", "    #     # Resample indices with replacement based on normalized weights\n", "    #     num_samples = samples_to_use.shape[0]\n", "    #     resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "        \n", "    #     # Get the new unweighted samples\n", "    #     resampled_list.append(samples_to_use[i, resampled_indices])\n", "\n", "    # resampled_V = torch.stack(resampled_list)\n", "\n", "    # normalize once (1-D tensor)\n", "    normalized_weights = weights_to_use / weights_to_use.sum()\n", "    \n", "    # resample indices\n", "    num_samples       = samples_to_use.shape[0]\n", "    resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "    \n", "    # grab your unweighted resampled samples\n", "    resampled_V = samples_to_use[resampled_indices]\n", "    \n", "    # Now, calculate <PERSON><PERSON><PERSON> distance on the resampled (unweighted) data\n", "    diff = Wass(resampled_V, target_samples)\n", "    print(f\"<PERSON><PERSON><PERSON> distance on reweighted samples: {diff:.8f}\")"]}, {"cell_type": "markdown", "id": "dcfc1bee-ff9d-4e44-bc7b-0687f4f73e74", "metadata": {}, "source": ["m=0.01, M=10"]}, {"cell_type": "code", "execution_count": 65, "id": "8be0e6f0-61ce-43dd-92ea-dcb99e021024", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-6.629500896797655)"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.0101)"]}, {"cell_type": "markdown", "id": "5ad615d4-08b8-4f44-b92f-53c9b9d6c035", "metadata": {}, "source": ["m=$2^{-3}$, M=10"]}, {"cell_type": "code", "execution_count": 70, "id": "c70e46ca-e859-43ba-a9a0-7df0f0131757", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running SMS-ZBAOABZ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1192/1441852859.py:33: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  zeta = torch.tensor(M*tau,device=device).detach()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The average stepsize is: 0.12629450857639313\n", "\n", "Calculating Wasserstein distance with reweighting...\n", "Wasserstein distance on reweighted samples: 0.10935930\n"]}], "source": ["if __name__ == '__main__':\n", "    K = 40000\n", "    tau = torch.tensor(1.0, device=device) # Base stepsize in virtual time\n", "\n", "    print(\"Running SMS-ZBAOABZ...\")\n", "    V_arr, mu_arr, t_mean = SMS_ZBAOABZ(K, tau)\n", "    print(f\"The average stepsize is: {t_mean}\")\n", "\n", "    # --- <PERSON><PERSON>tion with Reweighting ---\n", "    print(\"\\nCalculating <PERSON>serstein distance with reweighting...\")\n", "    burn_in = K // 5 # Discard first 20% of samples\n", "    \n", "    # Samples and weights after burn-in\n", "    samples_to_use = V_arr[burn_in:]\n", "    weights_to_use = mu_arr[burn_in:]\n", "\n", "    # Generate target distribution samples\n", "    target_samples = torch.randn_like(samples_to_use, device=device) * target_sd + target_mean\n", "\n", "    # # Perform resampling for each parallel run\n", "    # resampled_list = []\n", "    # for i in range(par_runs):\n", "    #     # Normalize weights for the current run\n", "    #     normalized_weights = weights_to_use[i] / torch.sum(weights_to_use[i])\n", "        \n", "    #     # Resample indices with replacement based on normalized weights\n", "    #     num_samples = samples_to_use.shape[0]\n", "    #     resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "        \n", "    #     # Get the new unweighted samples\n", "    #     resampled_list.append(samples_to_use[i, resampled_indices])\n", "\n", "    # resampled_V = torch.stack(resampled_list)\n", "\n", "    # normalize once (1-D tensor)\n", "    normalized_weights = weights_to_use / weights_to_use.sum()\n", "    \n", "    # resample indices\n", "    num_samples       = samples_to_use.shape[0]\n", "    resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "    \n", "    # grab your unweighted resampled samples\n", "    resampled_V = samples_to_use[resampled_indices]\n", "    \n", "    # Now, calculate <PERSON><PERSON><PERSON> distance on the resampled (unweighted) data\n", "    diff = Wass(resampled_V, target_samples)\n", "    print(f\"<PERSON><PERSON><PERSON> distance on reweighted samples: {diff:.8f}\")"]}, {"cell_type": "code", "execution_count": 71, "id": "aeea9650-1b57-4db3-915f-12f664c7ddeb", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-3.0588936890535687)"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.12)"]}, {"cell_type": "markdown", "id": "6fdf3866-701a-4190-944f-d6dfa69f8893", "metadata": {}, "source": ["m=$2^{-3}$, M=2"]}, {"cell_type": "code", "execution_count": 76, "id": "20ab1bd8-906e-4945-8e12-20ee1d837b57", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running SMS-ZBAOABZ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1192/1441852859.py:33: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  zeta = torch.tensor(M*tau,device=device).detach()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The average stepsize is: 0.12524551153182983\n", "\n", "Calculating Wasserstein distance with reweighting...\n", "Wasserstein distance on reweighted samples: 0.10602205\n"]}], "source": ["if __name__ == '__main__':\n", "    K = 40000\n", "    tau = torch.tensor(1.0, device=device) # Base stepsize in virtual time\n", "\n", "    print(\"Running SMS-ZBAOABZ...\")\n", "    V_arr, mu_arr, t_mean = SMS_ZBAOABZ(K, tau)\n", "    print(f\"The average stepsize is: {t_mean}\")\n", "\n", "    # --- <PERSON><PERSON>tion with Reweighting ---\n", "    print(\"\\nCalculating <PERSON>serstein distance with reweighting...\")\n", "    burn_in = K // 5 # Discard first 20% of samples\n", "    \n", "    # Samples and weights after burn-in\n", "    samples_to_use = V_arr[burn_in:]\n", "    weights_to_use = mu_arr[burn_in:]\n", "\n", "    # Generate target distribution samples\n", "    target_samples = torch.randn_like(samples_to_use, device=device) * target_sd + target_mean\n", "\n", "    # # Perform resampling for each parallel run\n", "    # resampled_list = []\n", "    # for i in range(par_runs):\n", "    #     # Normalize weights for the current run\n", "    #     normalized_weights = weights_to_use[i] / torch.sum(weights_to_use[i])\n", "        \n", "    #     # Resample indices with replacement based on normalized weights\n", "    #     num_samples = samples_to_use.shape[0]\n", "    #     resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "        \n", "    #     # Get the new unweighted samples\n", "    #     resampled_list.append(samples_to_use[i, resampled_indices])\n", "\n", "    # resampled_V = torch.stack(resampled_list)\n", "\n", "    # normalize once (1-D tensor)\n", "    normalized_weights = weights_to_use / weights_to_use.sum()\n", "    \n", "    # resample indices\n", "    num_samples       = samples_to_use.shape[0]\n", "    resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "    \n", "    # grab your unweighted resampled samples\n", "    resampled_V = samples_to_use[resampled_indices]\n", "    \n", "    # Now, calculate <PERSON><PERSON><PERSON> distance on the resampled (unweighted) data\n", "    diff = Wass(resampled_V, target_samples)\n", "    print(f\"<PERSON><PERSON><PERSON> distance on reweighted samples: {diff:.8f}\")"]}, {"cell_type": "code", "execution_count": 77, "id": "201c63f1-c676-4c50-a764-4d867fbda44f", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-3.0)"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.125)"]}, {"cell_type": "markdown", "id": "a7088194-8926-4e67-a03c-05ffe96e5735", "metadata": {}, "source": ["m=$2^{-2}$, M=1"]}, {"cell_type": "code", "execution_count": 103, "id": "71e2a624-7931-4dbf-9de1-96367f8e49b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running SMS-ZBAOABZ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1192/2041635201.py:33: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  zeta = torch.tensor(M*tau,device=device).detach()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The average stepsize is: 0.30268147587776184\n", "\n", "Calculating Wasserstein distance with reweighting...\n", "Wasserstein distance on reweighted samples: 0.73248917\n"]}], "source": ["if __name__ == '__main__':\n", "    K = 40000\n", "    tau = torch.tensor(1.0, device=device) # Base stepsize in virtual time\n", "\n", "    print(\"Running SMS-ZBAOABZ...\")\n", "    V_arr, mu_arr, t_mean = SMS_ZBAOABZ(K, tau)\n", "    print(f\"The average stepsize is: {t_mean}\")\n", "\n", "    # --- <PERSON><PERSON>tion with Reweighting ---\n", "    print(\"\\nCalculating <PERSON>serstein distance with reweighting...\")\n", "    burn_in = K // 5 # Discard first 20% of samples\n", "    \n", "    # Samples and weights after burn-in\n", "    samples_to_use = V_arr[burn_in:]\n", "    weights_to_use = mu_arr[burn_in:]\n", "\n", "    # Generate target distribution samples\n", "    target_samples = torch.randn_like(samples_to_use, device=device) * target_sd + target_mean\n", "\n", "    # # Perform resampling for each parallel run\n", "    # resampled_list = []\n", "    # for i in range(par_runs):\n", "    #     # Normalize weights for the current run\n", "    #     normalized_weights = weights_to_use[i] / torch.sum(weights_to_use[i])\n", "        \n", "    #     # Resample indices with replacement based on normalized weights\n", "    #     num_samples = samples_to_use.shape[0]\n", "    #     resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "        \n", "    #     # Get the new unweighted samples\n", "    #     resampled_list.append(samples_to_use[i, resampled_indices])\n", "\n", "    # resampled_V = torch.stack(resampled_list)\n", "\n", "    # normalize once (1-D tensor)\n", "    normalized_weights = weights_to_use / weights_to_use.sum()\n", "    \n", "    # resample indices\n", "    num_samples       = samples_to_use.shape[0]\n", "    resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "    \n", "    # grab your unweighted resampled samples\n", "    resampled_V = samples_to_use[resampled_indices]\n", "    \n", "    # Now, calculate <PERSON><PERSON><PERSON> distance on the resampled (unweighted) data\n", "    diff = Wass(resampled_V, target_samples)\n", "    print(f\"<PERSON><PERSON><PERSON> distance on reweighted samples: {diff:.8f}\")"]}, {"cell_type": "code", "execution_count": 95, "id": "58bf0f08-abd3-430a-b9c4-71c8b076d66c", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-1.5563933485243853)"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.34)"]}, {"cell_type": "code", "execution_count": 101, "id": "a9518fb4-b99b-4a74-a310-d7caeafc4768", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-0.46187763083350714)"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.72604072)"]}, {"cell_type": "markdown", "id": "e8cbb9f4-0f95-4707-b66f-762a6d9fb1cb", "metadata": {}, "source": ["m=$2^{-2}$, M=0.5, r=1"]}, {"cell_type": "code", "execution_count": 159, "id": "c6e07dcd-3a63-468d-8dcc-6d89a31bd56f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running SMS-ZBAOABZ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1192/3154432885.py:34: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  zeta = torch.tensor(M*tau,device=device).detach()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The average stepsize is: 0.26852251852676273\n", "\n", "Calculating Wasserstein distance with reweighting...\n", "Wasserstein distance on reweighted samples: 0.44838184\n"]}], "source": ["if __name__ == '__main__':\n", "    K = 40000\n", "    tau = torch.tensor(1.0, device=device) # Base stepsize in virtual time\n", "\n", "    print(\"Running SMS-ZBAOABZ...\")\n", "    V_arr, mu_arr, t_arr = SMS_ZBAOABZ(K, tau)\n", "    print(f\"The average stepsize is: {np.mean(t_arr)}\")\n", "\n", "    # --- <PERSON><PERSON>tion with Reweighting ---\n", "    print(\"\\nCalculating <PERSON>serstein distance with reweighting...\")\n", "    burn_in = K // 5 # Discard first 20% of samples\n", "    \n", "    # Samples and weights after burn-in\n", "    samples_to_use = V_arr[burn_in:]\n", "    weights_to_use = mu_arr[burn_in:]\n", "\n", "    # Generate target distribution samples\n", "    target_samples = torch.randn_like(samples_to_use, device=device) * target_sd + target_mean\n", "\n", "    # # Perform resampling for each parallel run\n", "    # resampled_list = []\n", "    # for i in range(par_runs):\n", "    #     # Normalize weights for the current run\n", "    #     normalized_weights = weights_to_use[i] / torch.sum(weights_to_use[i])\n", "        \n", "    #     # Resample indices with replacement based on normalized weights\n", "    #     num_samples = samples_to_use.shape[0]\n", "    #     resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "        \n", "    #     # Get the new unweighted samples\n", "    #     resampled_list.append(samples_to_use[i, resampled_indices])\n", "\n", "    # resampled_V = torch.stack(resampled_list)\n", "\n", "    # normalize once (1-D tensor)\n", "    normalized_weights = weights_to_use / weights_to_use.sum()\n", "    \n", "    # resample indices\n", "    num_samples       = samples_to_use.shape[0]\n", "    resampled_indices = torch.multinomial(normalized_weights, num_samples, replacement=True)\n", "    \n", "    # grab your unweighted resampled samples\n", "    resampled_V = samples_to_use[resampled_indices]\n", "    \n", "    # Now, calculate <PERSON><PERSON><PERSON> distance on the resampled (unweighted) data\n", "    diff = Wass(resampled_V, target_samples)\n", "    print(f\"<PERSON><PERSON><PERSON> distance on reweighted samples: {diff:.8f}\")"]}, {"cell_type": "code", "execution_count": 147, "id": "7ded7f86-cf2f-4734-b06f-b00131050f4f", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-1.8996950942043145)"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.268)"]}, {"cell_type": "code", "execution_count": 163, "id": "8f6def3c-96b4-4a0b-9bc6-2c161b832769", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-1.1584293626044828)"]}, "execution_count": 163, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log2(0.448)"]}, {"cell_type": "code", "execution_count": 160, "id": "b7e74dd6-ecef-45af-8b7b-92ba8320c88c", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Stepsize $t$')"]}, "execution_count": 160, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*********************************/PzMzE9u3bMXbsWOW2xMREuLu7q6yNFx4eDhsbGxw6dEjtcQoLC5Gbm6vyQ0RERNbJ4IApLS0NgiDAwcEBbdq0QdeuXeHi4qJ8zBB37txBaWkpvL29VbZ7e3sjIyNDp2NMnToVfn5+KkFXZatWrUK9evUwZMgQ5baMjAw0bNhQZT87Ozt4enpqPG9UVBTc3NyUP/7+/jqVj4iIiCyPwQFTs2bNcPv27Wrb7969i2bNmhlVKENFR0dj3bp12LJlC5ycnNTus2LFCowcOVLj47qaPn06cnJylD/Xr1836nhEREQkXwYvjSIIgtp14vLz8w0ORry8vGBra4vMzEyV7ZmZmfDx8anxufPmzUN0dDR27dqFDh06qN1n7969SE1Nxfr161W2+/j4VBtUXlJSgnv37mk8r6OjIxwdHbVViYiIiKyA3gFTZGQkgPIxTB9//DGcnZ2Vj5WWluLQoUMIDg42qDAODg4ICQlBbGwsBg0aBKB80HdsbCwmTpyo8Xlz587F559/jr/++ktlHFJVy5cvR0hICIKCglS2h4WFITs7G0lJSQgJCQEA7N69G2VlZQgNFXdwGREREVkevQOmY8eOAShvYTp58iQcHByUjzk4OCAoKAiTJ082uECRkZEYPXo0OnfujK5du2LBggV48OABxowZAwAYNWoUGjVqhKioKADAnDlzMGPGDKxduxYBAQHKMUcuLi7KMVVA+Sj4jRs34quvvqp2ztatW6N///4YN24clixZguLiYkycOBEvvfSSTjPkiIiIyPSkzPStd8AUFxcHABgzZgy++eYbUafQA8Dw4cNx+/ZtzJgxAxkZGQgODkZMTIxyIHhaWhpsbP4ZerV48WIUFRVh2LBhKseZOXMmZs2apfx73bp1EAQBI0aMUHveNWvWYOLEiejbty9sbGwwdOhQfPvtt6LWjYiIiCyT3nmYEhMTcffuXTz33HPKbT/++CNmzpyJBw8eYNCgQfjuu+9q3fge5mEiIiIyrR4tvfDTvywkD9Mnn3yC06dPK/8+efIkxo4di/DwcEybNg2//fabsruMiIiIyBroHTAdP34cffv2Vf69bt06hIaGYunSpYiMjMS3336LDRs2iFpIIiIiIinpHTDdv39fJbFkQkICnnnmGeXfXbp0YU4iIiIisip6B0ze3t64cuUKgPKlTJKTk9GtWzfl43l5ebC3txevhEREREQS0ztgGjBgAKZNm4a9e/di+vTpcHZ2Rs+ePZWPnzhxAi1atBC1kERERERq8mWbjd5pBWbPno0hQ4bgySefhIuLC1atWqWSi2nFihXo16+fqIUkIiIikpLeAZOXlxf27NmDnJwcuLi4wNbWVuXxjRs3qiSMJCIiIrJ0Bq8l5+bmpna7p6enwYUhIiIikiO9xzARERER1TYMmIiIiMgiSLmWHAMmIiIiIi0YMBERERFpwYCJiIiISAsGTERERERaMGAiIiIi0oIBExEREZEWDJiIiIiItGDARERERBZBysV3GTARERERacGAiYiIiEgLBkxEREREWjBgIiIiIovAteSIiIiItLh0O1+yczNgIiIiItKCARMRERGRFgyYiIiIiLRgwEREREQWgYO+iYiIiGSMARMRERFZBAHSNTExYCIiIiLSggETERERkRYMmIiIiIi0YMBEREREpAUDJiIiIiItGDARERGRRbCzkS5sYcBEREREFqFlQxfJzs2AiYiIiCyCQiHduRkwEREREWnBgImIiIhICwZMRERERFowYCIiIiLSggETERERkRYMmIiIiIi0YMBEREREFkHCrAIMmIiIiIi0YcBEREREpAUDJiIiIiItGDARERERacGAiYiIiCyCIOG5GTARERERacGAiYiIiEgLBkxEREREWsguYFq0aBECAgLg5OSE0NBQHD58WOO+S5cuRc+ePeHh4QEPDw+Eh4er3f/s2bN44YUX4Obmhrp166JLly5IS0tTPt67d28oFAqVnzfeeMMk9SMiIiLLI6uAaf369YiMjMTMmTORnJyMoKAgREREICsrS+3+8fHxGDFiBOLi4pCYmAh/f3/069cPN2/eVO5z6dIl9OjRA4GBgYiPj8eJEyfw8ccfw8nJSeVY48aNQ3p6uvJn7ty5Jq0rERER6UeQcNS3QhCkPL2q0NBQdOnSBQsXLgQAlJWVwd/fH2+//TamTZum9fmlpaXw8PDAwoULMWrUKADASy+9BHt7e6xevVrj83r37o3g4GAsWLBA57IWFhaisLBQ+Xdubi78/f2Rk5MDV1dXnY+jTcC07aIdi4iIyJI9+XgDrHq9q6jHzM3NhZubm9brt2xamIqKipCUlITw8HDlNhsbG4SHhyMxMVGnYxQUFKC4uBienp4AygOu7du34/HHH0dERAQaNmyI0NBQbN26tdpz16xZAy8vL7Rr1w7Tp09HQUFBjeeKioqCm5ub8sff31/3yhIREZHeFBIuJiebgOnOnTsoLS2Ft7e3ynZvb29kZGTodIypU6fCz89PGXRlZWUhPz8f0dHR6N+/P3bs2IHBgwdjyJAhSEhIUD7v5Zdfxk8//YS4uDhMnz4dq1evxiuvvFLjuaZPn46cnBzlz/Xr1/WsMREREelDysV37SQ8t6iio6Oxbt06xMfHK8cnlZWVAQAGDhyISZMmAQCCg4Nx4MABLFmyBE8++SQAYPz48crjtG/fHr6+vujbty8uXbqEFi1aqD2fo6MjHB0dTVklIiIiqoSJKwF4eXnB1tYWmZmZKtszMzPh4+NT43PnzZuH6Oho7NixAx06dFA5pp2dHdq0aaOyf+vWrVVmyVUVGhoKALh48aK+1SAiIiIrJJuAycHBASEhIYiNjVVuKysrQ2xsLMLCwjQ+b+7cuZg9ezZiYmLQuXPnasfs0qULUlNTVbafP38eTZs21XjMlJQUAICvr68BNSEiIiJrI6suucjISIwePRqdO3dG165dsWDBAjx48ABjxowBAIwaNQqNGjVCVFQUAGDOnDmYMWMG1q5di4CAAOVYJxcXF7i4uAAApkyZguHDh6NXr1546qmnEBMTg99++w3x8fEAytMOrF27FgMGDED9+vVx4sQJTJo0Cb169VJprSIiIqLaS1YB0/Dhw3H79m3MmDEDGRkZCA4ORkxMjHIgeFpaGmxs/mkUW7x4MYqKijBs2DCV48ycOROzZs0CAAwePBhLlixBVFQU3nnnHbRq1QqbNm1Cjx49AJS3Qu3atUsZnPn7+2Po0KH46KOPzFNpIiIikj1Z5WGyZLrmcdAX8zARERGV692qAVaOqeV5mIiIiIjkigETERERkRYMmIiIiIi0YMBEREREpAUDJiIiIrIIUi6NwoCJiIiISAsGTERERGQRuJYcERERkYwxYCIiIiLSggETERERkRYMmIiIiIi0YMBEREREpAUDJiIiIiItGDARERERacGAiYiIiEgLBkxERERkEbg0ChEREZGMMWAiIiIi0oIBExEREZEWDJiIiIiItGDARERERKQFAyYiIiIiLRgwEREREWnBgImIiIhICwZMRERERFowYCIiIiKL4FnXUbJzM2AiIiIii+BoL13YwoCJiIiISAsGTERERERaMGAiIiIi0oIBExEREVmEdn5ukp2bARMRERFZBA9ne8nOzYCJiIiILIJCId25GTARERERacGAiYiIiEgLBkxEREREWjBgIiKiWsXfs47URSALxICJar35LwZJXQQiMqOXuzaVughkgRgwUa1nI+W0CyIyOxcnO6mLQBaIARPVet1b1pe6CEQWq10jV6mLoJf6dR3AWyQyBAMmqvUa1nOCrQ2/QokM4WDLywjVDnynEwFwryNd9lgisl4+rk5SF0Grbs09pS6CRWDAZGXa+FpW8zgRkTn5ezpLXQTZ4ThO3TBgsjJv9m5RK2d9NajnKHURiMgEnuvgK+rx2jcy7+KtAgSzno9MhwGTlbG3VWBIp8b45qVgqYtiVktHdZa6CERWr3mDutW2mTocEPv49V0cRD4i1RYMmGTOs65hH+6BwY1ELglZKy8Xts6RdnXsbbH7P72lLobRBDb4WDjpug8ZMMnc5je7S10Ei9DQyC652tyFX5vrbkrNvaq3xpABGOCYHINI3TBgkrkAvb90a9/Vr0dLL/i6iTsTpWMTd1GPJ2eW9o55p09LqYugk1/e7I6FL3eUuhiy0OvxBgY/1xRjgFw5K5YMwICJLN5r3QOgMLKZZHBHdmFaipAA7VOg3zYwqIoe0t6g56njWdcBz3XwE+145lDXwdYkx11mxBhDU7R+MO2a/L3aTX7L1zBgIpOYPaid2c/5Vu8WBj93ZKjqh5Pfp5ZN3eBkXdT26dV/vNtT7+e4OlVvran6b5T631o5F1Kwv7t0BdFgSCf53bCtH9/NLOfRNIZy9qB2spvMw4CJTEKKu4P3+wca/Fx9L5SWthyENTH1tdfQYMsaNK2vf90/H1z95qhHSy8xigNAvBamuMm9sXhkJ/RuZXj3YE2e1NDtqNDlHSvDMUShzc2zZFSXAA+Nj8nt9kV2AdOiRYsQEBAAJycnhIaG4vDhwxr3Xbp0KXr27AkPDw94eHggPDxc7f5nz57FCy+8ADc3N9StWxddunRBWlqa8vFHjx5hwoQJqF+/PlxcXDB06FBkZmaapH5kGbR18W2b0MNMJdHfa90DpC6CqDyc7fHhgNYq254PMl1X18Z/h5ns2HLwfyGNRT1eYw/TJoL0djV+FqdCATTzqotn2vsa3X2vSR1703RnVtW5qeYAw1BS5opyd9Y8E1xucaSsAqb169cjMjISM2fORHJyMoKCghAREYGsrCy1+8fHx2PEiBGIi4tDYmIi/P390a9fP9y8eVO5z6VLl9CjRw8EBgYiPj4eJ06cwMcffwwnp3+aaCdNmoTffvsNGzduREJCAm7duoUhQ4aYvL6G+P1t+V6opWKK7z9th7SR6SCIfVOfwszn20hdDFENC2mMcb2aK/9WKIBvjcwz1qGx5uSF9V0csSuyl1HH16R7C3Hu2o1ZyiKirY9Bz9Pnoipm0B75dCujj2GOT6u5uh3rOdmZ50RUjawCpvnz52PcuHEYM2YM2rRpgyVLlsDZ2RkrVqxQu/+aNWvw1ltvITg4GIGBgVi2bBnKysoQGxur3OfDDz/EgAEDMHfuXHTs2BEtWrTACy+8gIYNGwIAcnJysHz5csyfPx99+vRBSEgIfvjhBxw4cAAHDx40S721cXH85wMi50Viu+owGFeubPT4JDjaad55UHDNLR8/vNYFbXxd4eFsmlk6jT2c9b6Dlnp8iSG01VFbN8iz7X1hV8NnqYmn9q6pl0ObaN2nqjdrGGf3fv9WWDsuVKfjOJmpNcNQfVt7q/xtTLeam7M93un7mJElUhVgQNejofzcxZ3Ba6oWMtJONgFTUVERkpKSEB4ertxmY2OD8PBwJCYm6nSMgoICFBcXw9Oz/MJdVlaG7du34/HHH0dERAQaNmyI0NBQbN26VfmcpKQkFBcXq5w3MDAQTZo0qfG8hYWFyM3NVfkxFX0+Hqb6LIm9PIE+Wvu64qexul1IdPHfV0OqNfM3cq+DZzXU8V89mqn8vWOS4a0PTwU2xB/v9kSgj+5joPxETJkQ3rqhaMeSik5jQip5sXNjpH7WH4E+9ZTbxvZoBhcLvlOvKWjXRm7dHDoxciBT1SCjnQHLo7zf37CWrtDm9THjuTb48fWuBj2/KgWAve8/hV2RT4pyPHWk6tZ/IcgPkU8/jpj3yicfCDJLECWbgOnOnTsoLS2Ft7fqnYm3tzcyMjJ0OsbUqVPh5+enDH6ysrKQn5+P6Oho9O/fHzt27MDgwYMxZMgQJCQkAAAyMjLg4OAAd3d3vc4bFRUFNzc35Y+/v78etdXPF39PdX4vXPtd1lOtTHNBXPhyJ637tPEzzUDoP9/tiR6PiTeINKKtD3o9pjpAU6FQYFGlOlb+fn2xi+pr6+yg+UJriru/Rh51sHXCE4if3Fv0Y5uaXAZQO9qptsjY2db81aeu+0ms3FwjumpumdK2ePbAYD8E+tQzaoKD3C5ClsLT2QH2toZ9vl/v0cyoXFRV+Xs6o2VDF9GOV5W7iVrA1ak8gcbZwRbv9H1MrxtKc5JNwGSs6OhorFu3Dlu2bFGOTyorKwMADBw4EJMmTUJwcDCmTZuG5557DkuWLDHqfNOnT0dOTo7y5/r160bXQZPng/xwclY/vBf+uNZ9HSrdeZq7+25sj2aYEtHKoHFWP4/rhgXDg8UvlIH0bcXQ+/h6HF4BBYL93Q1IYlqdumtlTXUNMXKAacy7+rXGqZvBVDUINTQmraNLjqEajr3lrSfQr4235h100CXAs8YZpO7ODjj8YV+cnNVP7eOvdGuKmPd6qXQpfTG4PZaP7oxzs/vrVAZjw6UvBtecqyqgvviDwA0p86Y3xR+8/+e7vYxKX6LOO30fk7zVr4mn+tfs3082V7v9P09rvxbp6qUu+ndtS0U2AZOXlxdsbW2rzU7LzMyEj0/NgxTnzZuH6Oho7NixAx06dFA5pp2dHdq0UR0E27p1a+UsOR8fHxQVFSE7O1uv8zo6OsLV1VXlx5Tq/Z3rRJ87nD/e0S+nyjPt1Nd3xnO6DSJ2tLPBhKdaGtTcHdaiPgZ1bITjM9VfKAwxVc1deEUQqe0LypA6VOgTWN7KJ9qq6EbGbv6edcQpRxW6tHg66Nl1pC6njyazB7bVab+KgHBUmO6pLkzRCPPDa110GnvUsJ6T8vMOAPXVrCdZ+WaonpMd+rb21nlcU9W6jf77//L6E83U7F2dtrFbjTxM835TR1PXUSP3Oghp+s+YSjEafpt51UXLhi54v3+gqAk+NaUjEMOLnXWbEflBlVmoFQa0Uz9M4e2+j+H4DOO/q59q1UDlvVz1dfJ1++e9FN7aG96ujib9f2kjm4DJwcEBISEhKgO2KwZwh4VpvlOYO3cuZs+ejZiYGHTurJrkysHBAV26dEFqaqrK9vPnz6Np0/IviZCQENjb26ucNzU1FWlpaTWeVyotGrjg2fa+Or1pWlUas6GLqq0Jn7zQFmc+jcDrPXT7IjW0O2rPlKeUv7vVsGTBtGe0d0NUbt2qX9dBmSPm372aY0B7H2yb+ESNz98/rQ+2vNUdj3n/09zdooELHv/773aNXNVewCr7engwPhMx6drEp4xbCuSNJ/+5I1YXB1R+n+gz5bxzU/EH+Vdunq9YeDq8ygDiineZvjMVK7qrG4t0QQ+rlKemprFh/301BO/2fUzv/D8VNzBje+r2+dPF7v9UH/cS3sYbx2f2wwyJZ1eOq6Ge+g7S1iVx5vDO/mji6YxjHz+NdTokaaycl0jdEIHKgWg9R93Hx2nrhjXGZ4PaY0qE9rFX7s4OysC5spK/e2nUcTNDt137xm74bFA7rHq9K5aOCsGBaX11ayk2EVmNeoyMjMTo0aPRuXNndO3aFQsWLMCDBw8wZswYAMCoUaPQqFEjREVFAQDmzJmDGTNmYO3atQgICFCOOXJxcYGLS/kFbsqUKRg+fDh69eqFp556CjExMfjtt98QHx8PAHBzc8PYsWMRGRkJT09PuLq64u2330ZYWBi6dTNPplN9KBQKLBpZPtYmNSMPEQv2GHSciqDr/U0najhXzeN1KhsY7IcGBiyAG+TvjiZVmvB/HtcNI5ZWn6H4xpMtEP3nuRqPp9IypCjP4F01izeguQWhkXsdNHKvo3KHb2ujUOla0nahdqtjj1e6NUVhSWmN++nKkP9r9xb1ceDS3Rov5Jvf6o7NyTcwpV8ggj7dAQB4toMvNibdMLisxqqcQDR+Sm/cvP8QrUW6oLg7O+DkrH6izTB7roMv7G1t0K6RK2b+elrjfhFtfTRO5f98cDt8uOWU2se+HdERqRl5aOPrirkx5Td96hb09XbVfVJA8wYuOJ+Zp7LNzsamxhuVClXHgemj6piwj55tjc+2n1XZ9n7/QCzde0Xt8wd1bIT0nIfoEuCJ4f8TZ/bynGEdIAgCFAoFujWvj7nDOuD9XzR/H+rjhWA/rDmUVuM+i0d2wlOBDfV+P+rT+OlgZ4PQZrrd2Ewf0BqrEq+pbCspNa6ptX0jN9jaKJByPVvHZ1T/bn2lUhe2gUPIRCObFiYAGD58OObNm4cZM2YgODgYKSkpiImJUQ4ET0tLQ3p6unL/xYsXo6ioCMOGDYOvr6/yZ968ecp9Bg8ejCVLlmDu3Llo3749li1bhk2bNqFHj39aIr7++ms899xzGDp0KHr16gUfHx9s3rzZfBU3kL4tSGc+jVD+Xt/Fodpg5qoe99bt+K19XfHNS+ItMhrWor543VkGatnQBTsm9ULyx08DKA+SKn7EUHV82cY3wtTe4QGGdScsHhmCucM6YH6VcWGVB/x2auKBzwa1r/FO8d+91I9hALTn5flUxy6zyoZ38UfT+s54/YlmcHWyVx8s/f3/qDpwXxf1nOxh//eAb2OnlisUCvRv56N34kbXOv/chKgL5iuUB2NusLFR4PCHfRE/uTfqV1pGYsVrnfHhgNboquMFsULlm4U+gQ21XlCXjuqMpvWdsXJMF52OX9EFWjlfUNVg6189m6NFlQkB9jUMxLe1UWBin8cMzj6taZxe5Vbxbs10P7a2LtvKN27qzjx3aAc8095XbbAU815PvPT3d3NTLePBlrwSgnpOdnilm/HjgNSVpaTMuIDJ1kZh8LqOciSrFiYAmDhxIiZOnKj2sYpWoQpXr17V6Zivv/46Xn/9dY2POzk5YdGiRVi0aJGuxbRIurYWVeim45eTttxDQHmiwCt3HiDvUYnK9rEauvv0CUwMCSh0ScKna8BY2fNBuqVf6NDYDXsv3FH+3dTTGZ8MbFftDq+qJa90whs/JSv/ntzvcczbcb7afm7O9nixc/mXrjED2Pu19cHHz7XB7N/PVHus8kWjkXsd3Mx+qPL4yzXMBgOAQJ968HFzQnzqbeW2ek72iJ/cW6fuXX9PZwT7u6u9e9XlPbHw5Y6I/vMcbmY/xLG06sdQ562nWmLHmUyj1v5q7OGMTwe21alVp0LDek5Albdjn0Bv9DFgslzld/6K17QHQU+38cbTBgx21/YSfDuiI579dp/Gx+s62Kp0JxtDl/dDk/rO+HlcN+w8k4nerRpg1ArNq0xosv2dHjh85R5e7OyPG/cL8GvKLfyrZ/WbDp8qqUIqd/MH+rgiemgHTHr6cbg726PVRzEaz9e/nQ/6tfFGUWkZfjqovkVL2zedurGAFd8ZhgRMV6IGoNn0P5R/V83Jpc67fR/DuiNpeFfkfFtik13ARJbl+5GdtM4c6t/WB0teDcGryw+pBAkJU3obtHaVGOz1yVSph8ppHWq6C7Wt8g3uWsPFs3LA07+dr0pwMrHPY1h98Boycwt1Kp8h94sjQ5uoBEwzn2+DhvWcVI5lyFT1lg1dsPDlTgiYtl1lu6ZgqblXXVy+8wBBjd2V2xqq6a5s4+uqUzbrxh7OWPhyJyzbe1nngCnY3x2nPomoNuhX39qPCgvQ8xniEXNAe03dpb5udZD76J/uv/8LaazS3dvWr+ZW5BOzIgye6WvoIO+wFvURZkQ29rZ+bsp6TYkIxOR+rXQK/t/u+xiu3SvA4I7/BOK6drXa2CjgZGN4d+mrRrwXPx/cDn+ezMC+i+Xf610CPAwayzrp6cfxXvhjsk/KKasuOTIfdW9LfWc0AcCA9r5ac9poIlWwBACR/R5H0/rOOg0k1+SDAYEqA3/dne0N+sDvm/qUrDI3D+mo2nriZG+rcuEa80QzjUk+a2Lsoqc7JvXCudn9UbeGAbXrx3fDH+/2VPl/antJKgcQEW18qm2rysXRTvZf7DXRJwaJGlJzCgG3OtVfi4p/zeJXOqHnY17KAdUV2bp9dAwEdAmWdF00W6pXS9P7pOpmtzr2WDqqMwa0ly5BsDrdW9RHSFMPjNQwM3JkaFP89C9xkgpbwmeKARMp/V+Ifsk3xcy+bW7erk5ImPKUUU3+43u1wM/ju8Hr73ElT7QwLLmmujEwNS2RUTFmxZDFPnVpXVDXaqDuq6xyq5J9lWC7XSNX5QXvy2EdMKRjI6NnDdrZ2mgNLA0Z41K5e1asmT89RUy0aihNgUmf1g0R6FNPOU6mJjUl2dSmeQMXrB4bquza9/d0xpEPw5Hwfm/lPhVpAfRJ+VDZm71b6JQJ35CLsZzywhnDmBZFe1sbbHqzOz7XknurYibr2B6axzyqM8rCFgpnl5yF2zrhCfxr1RHcyS8y6jgrx3TROl1zxnNtEPXnWRT/PXNC3+zbviIu8dGwniOy8mruhmproszjVW2d0B2/HU/Xa22xsBZe+Hb3RY2P19RCMuuFtmjuVRfPB5WPHWvfyA2ZueoXqNaXQqHQOY9O5e/hrgGeuHa3QPn3tgk9lBep/+vsj//rbLpM+JXLYAgxuqiqJv4TaxkMY/i6OyEj91G17Y52tvjz3Z4ag4gvBrfHB1tOYt7/BWk8trODLQqKStFTz4H3VWd8fvxcGwzt1NjgVQIa1HPE/ml9VMbMiGVQx0aI3JACI8c9m820ZwIxf+d5rBzTBWsPpeG5DtrHlorlv6+GID3nocYJEHvffwqFJaVo5O6MXWcz8URLLzwsLkUjd/Pl7BIDAyYLF+zvjle6NcWCXRfUPu7l4qA2mKqePVn7HdjrPZqhqLRM69R+TWrqStHXguHBeHnZIbWPJUzpjVvZj7SOkRBLYw/nGhdVVSesRX2sH99Np+zdVfMGudWxx9uVBkfOGdoBi+MvaZ31COgWtPZv64NJ4Y+rLAUS0c4H20+kq7Y+VbqQVO0a0WXQvthflrrk0lFH3YxMXSYFVNa/nQ9WHrgKAFj1elfZdy/UVL6XQ5toDf53RT6JxEt38YIOEz5qYmujQPvGxn1Odflf1zRGUCwd/KWd2fvGky0wrmdz2Noo0L1Sa7dzDTfCYq1TaWujqHG2qH+lG4qKGz1LxIDJCozr2RynbubgGTVZWYMauyP2XJbZl0mprGJdojd7t8CvKbdw70ERFr5ccxqCt3q3wL9XJ+FZA/r0m9avK+n4KF3V1H2kAHB8Zj+UlJZpnd1Y38URH+mYjX1q/0DkPSrBsBoyANvYKPBulSze0UPao3uL+iqDqfUNKqp6W4QZMe/3D8SeC7cxrmdzg1M+dG/phSWvhBi1NlflgNHQTMSPN9R/VmZVCkV5i5khubv04edeB0P1SHJqbhUTJf73aggWxV3E1y9qbi0Tw0fPttZrIL8+M5ZHhzVVmT1bU4Z9dd/zbf1cMSqsKY6lZePkzRwMaO+DsT2a4eudFzQmK1UXgwb61MO5jLzqD9QiDJisQF1HOywbrX568NxhHfDfPZd1TpEvpm9HdMSGI9eVmWYb1nNC0kfhOt0RRrT1wcHpfdXOgqoNFAqFXtPOdeVR10GZ+FQf9Zzsa8wZpG/w1KCeI1xEaHFs2dAFpz/pX+MNgS7jkvprWBbIHH5/uwdO38pB3xqSjOpq24QemL8zFdMHtMY3sRd0nv1nrfq19UE/HWZM6qvq4rTqUgeo8/FzbXDt7gN00mMh5/FPtsCqxGsI9KmHrROe0HuCiEKhwKcDy1c8uHG/AH5udWBjo9B7sPbj3gyYGDBZufoujhrXCaqJITPmqnohyA8vVGl+1aeromquEtUD/fOrPuuPyd2Qjo1w4/5DdJA4cacugv3Ll9Kpqcm/qiaezki7V1BtuROgfByEITQFSwuGB2Pb8VuYYMDSMg6VZn6+2bsFWjYw3crw7Rq5GbV2YWXtG7vhhzHl46dmPd8WgiDgj5PlKyAY0lprCDG6I9VlNJfCq93KW3cq1oes8H7/QGw4qn9GfE1552rSyL0Ojs/sh7oOtgbPSK6gb5LVyma90BZO9jYYrkPXf1jz+ki8fFfj7DpLxYCpluqgYdzAlre64/PtZ/Gxjl08Upo7rAMu335QbQ08S1Y1M7cYXgj2w8LdF9C9pbgztzzrOiD546dRx94WM7f9s7xH5fX8qvrlzTDEns3CQDVjXzy1rNGnr0EdG2FQR8MSTNrZ2mBX5JMoLRP0zqgvFw3qOeL7kSHKPFeO9pYzKfrptvolytz8VncM+f6A8m9vV3Fapj94tjWeCmxYLZu6l4sjPOs64N4D4ybb6MoUrc368qzrgLnDdOvaXPl6F1y58wCtDEj+K2cMmGqZHZN6IfnafQwKVn8h6djEA7+82V3j871c5NFFVtfBTpnJmmrm4miHfVP76DTGR9vCwlVVBDlDOjXGhqM3EOhTr8bWkob1nDROVZfbMGl9xjQF+bvB180J/kbcwZPhOjXxwOUvBuDg5btYuveysgvKWI52tujdyviu0trG0c4WgT7mmaVsTgyYapnHvesZtORHhUHBfkhOu6/zsili++jZ1uVdVkbOrJFCdyMyCBtLW7C0eGQn3Mx+aHDXULfm9ZEwpXfN3ahWzNHOtjwolVvUJwGp/gU2Ngp0b+klektqbaTv+oS1BQMm0oudrQ2+0JLEzJR0HVwpB5WHcmyb+ITZ0hwY4hkRxrcYOjPxqVYNcO1eAYL83Y0ug5SknIlam/w0NhTTt5zAnCEdJC3HuJ7NMSfmHPqbYFC5VA5O74srdx5IdkMsdwyYiEzE0c4WUyJa4WFRKTpUWv+MVK14rQsEQb8Fl0l/DWTSna6LmtZ67PGYF/a+38eMpVHv372ao0dLL4sd46aOj5tTrW0l1oXljAIkk+K1yjQmPNUSk/9Oq0DqKRQKBksmtOSVEAxo74OJffSfLairymlLasoTpM2UiFZo5V0P4yygJdnm76SbYswoJsugEAxZZpyqyc3NhZubG3JycuDqajmD3aZvPonzmXlYP76b0VNWiah2KiwpxdGr99HOz0209fiIzEXX6zcDJpFYasBERERUm+l6/WaTAhEREZEWDJiIiIiItGDARERERKQFAyYiIiIiLRgwEREREWnBgImIiIhICwZMRERERFowYCIiIiLSggETERERkRYMmIiIiIi0YMBEREREpAUDJiIiIiItGDARERERacGAiYiIiEgLO6kLYC0EQQAA5ObmSlwSIiIi0lXFdbviOq4JAyaR5OXlAQD8/f0lLgkRERHpKy8vD25ubhofVwjaQirSSVlZGW7duoV69epBoVCIdtzc3Fz4+/vj+vXrcHV1Fe24cmLtdWT9LJ+119Ha6wdYfx1ZP8MJgoC8vDz4+fnBxkbzSCW2MInExsYGjRs3NtnxXV1drfJDUJm115H1s3zWXkdrrx9g/XVk/QxTU8tSBQ76JiIiItKCARMRERGRFgyYZM7R0REzZ86Eo6Oj1EUxGWuvI+tn+ay9jtZeP8D668j6mR4HfRMRERFpwRYmIiIiIi0YMBERERFpwYCJiIiISAsGTERERERaMGCSuUWLFiEgIABOTk4IDQ3F4cOHpS5SNbNmzYJCoVD5CQwMVD7+6NEjTJgwAfXr14eLiwuGDh2KzMxMlWOkpaXh2WefhbOzMxo2bIgpU6agpKREZZ/4+Hh06tQJjo6OaNmyJVauXGmyOu3ZswfPP/88/Pz8oFAosHXrVpXHBUHAjBkz4Ovrizp16iA8PBwXLlxQ2efevXsYOXIkXF1d4e7ujrFjxyI/P19lnxMnTqBnz55wcnKCv78/5s6dW60sGzduRGBgIJycnNC+fXv88ccfJq/fa6+9Vu017d+/v8XULyoqCl26dEG9evXQsGFDDBo0CKmpqSr7mPN9KfbnWJf69e7du9pr+MYbb1hE/QBg8eLF6NChgzJRYVhYGP7880/l45b8+ulSP0t//aqKjo6GQqHAe++9p9xmca+hQLK1bt06wcHBQVixYoVw+vRpYdy4cYK7u7uQmZkpddFUzJw5U2jbtq2Qnp6u/Ll9+7by8TfeeEPw9/cXYmNjhaNHjwrdunUTunfvrny8pKREaNeunRAeHi4cO3ZM+OOPPwQvLy9h+vTpyn0uX74sODs7C5GRkcKZM2eE7777TrC1tRViYmJMUqc//vhD+PDDD4XNmzcLAIQtW7aoPB4dHS24ubkJW7duFY4fPy688MILQrNmzYSHDx8q9+nfv78QFBQkHDx4UNi7d6/QsmVLYcSIEcrHc3JyBG9vb2HkyJHCqVOnhJ9//lmoU6eO8N///le5z/79+wVbW1th7ty5wpkzZ4SPPvpIsLe3F06ePGnS+o0ePVro37+/ymt67949lX3kXL+IiAjhhx9+EE6dOiWkpKQIAwYMEJo0aSLk5+cr9zHX+9IUn2Nd6vfkk08K48aNU3kNc3JyLKJ+giAI27ZtE7Zv3y6cP39eSE1NFT744APB3t5eOHXqlCAIlv366VI/S3/9Kjt8+LAQEBAgdOjQQXj33XeV2y3tNWTAJGNdu3YVJkyYoPy7tLRU8PPzE6KioiQsVXUzZ84UgoKC1D6WnZ0t2NvbCxs3blRuO3v2rABASExMFASh/OJtY2MjZGRkKPdZvHix4OrqKhQWFgqCIAjvv/++0LZtW5VjDx8+XIiIiBC5NtVVDSjKysoEHx8f4csvv1Ruy87OFhwdHYWff/5ZEARBOHPmjABAOHLkiHKfP//8U1AoFMLNmzcFQRCE77//XvDw8FDWURAEYerUqUKrVq2Uf7/44ovCs88+q1Ke0NBQ4d///rfJ6icI5QHTwIEDNT7HkuonCIKQlZUlABASEhIEQTDv+9Icn+Oq9ROE8gtu5YtTVZZUvwoeHh7CsmXLrO71q1o/QbCe1y8vL0947LHHhJ07d6rUyRJfQ3bJyVRRURGSkpIQHh6u3GZjY4Pw8HAkJiZKWDL1Lly4AD8/PzRv3hwjR45EWloaACApKQnFxcUq9QgMDESTJk2U9UhMTET79u3h7e2t3CciIgK5ubk4ffq0cp/Kx6jYR4r/xZUrV5CRkaFSHjc3N4SGhqrUyd3dHZ07d1buEx4eDhsbGxw6dEi5T69eveDg4KDcJyIiAqmpqbh//75yH6nqHR8fj4YNG6JVq1Z48803cffuXeVjlla/nJwcAICnpycA870vzfU5rlq/CmvWrIGXlxfatWuH6dOno6CgQPmYJdWvtLQU69atw4MHDxAWFmZ1r1/V+lWwhtdvwoQJePbZZ6uVwxJfQy6+K1N37txBaWmpyhsFALy9vXHu3DmJSqVeaGgoVq5ciVatWiE9PR2ffPIJevbsiVOnTiEjIwMODg5wd3dXeY63tzcyMjIAABkZGWrrWfFYTfvk5ubi4cOHqFOnjolqV11FmdSVp3J5GzZsqPK4nZ0dPD09VfZp1qxZtWNUPObh4aGx3hXHMJX+/ftjyJAhaNasGS5duoQPPvgAzzzzDBITE2Fra2tR9SsrK8N7772HJ554Au3atVOe3xzvy/v375v8c6yufgDw8ssvo2nTpvDz88OJEycwdepUpKamYvPmzRZTv5MnTyIsLAyPHj2Ci4sLtmzZgjZt2iAlJcUqXj9N9QOs4/Vbt24dkpOTceTIkWqPWeJnkAETGe2ZZ55R/t6hQweEhoaiadOm2LBhg1kDGRLPSy+9pPy9ffv26NChA1q0aIH4+Hj07dtXwpLpb8KECTh16hT27dsndVFMQlP9xo8fr/y9ffv28PX1Rd++fXHp0iW0aNHC3MU0SKtWrZCSkoKcnBz88ssvGD16NBISEqQulmg01a9NmzYW//pdv34d7777Lnbu3AknJyepiyMKdsnJlJeXF2xtbavNGMjMzISPj49EpdKNu7s7Hn/8cVy8eBE+Pj4oKipCdna2yj6V6+Hj46O2nhWP1bSPq6ur2YOyijLV9Nr4+PggKytL5fGSkhLcu3dPlHqb+z3QvHlzeHl54eLFi8pyWUL9Jk6ciN9//x1xcXFo3Lixcru53pem/hxrqp86oaGhAKDyGsq9fg4ODmjZsiVCQkIQFRWFoKAgfPPNN1bz+mmqnzqW9volJSUhKysLnTp1gp2dHezs7JCQkIBvv/0WdnZ28Pb2trjXkAGTTDk4OCAkJASxsbHKbWVlZYiNjVXp45aj/Px8XLp0Cb6+vggJCYG9vb1KPVJTU5GWlqasR1hYGE6ePKlyAd65cydcXV2VzdNhYWEqx6jYR4r/RbNmzeDj46NSntzcXBw6dEilTtnZ2UhKSlLus3v3bpSVlSm/+MLCwrBnzx4UFxcr99m5cydatWoFDw8P5T5yqPeNGzdw9+5d+Pr6Kssl5/oJgoCJEydiy5Yt2L17d7WuQXO9L031OdZWP3VSUlIAQOU1lGv9NCkrK0NhYaHFv37a6qeOpb1+ffv2xcmTJ5GSkqL86dy5M0aOHKn83eJeQ72GiJNZrVu3TnB0dBRWrlwpnDlzRhg/frzg7u6uMmNADv7zn/8I8fHxwpUrV4T9+/cL4eHhgpeXl5CVlSUIQvnU0SZNmgi7d+8Wjh49KoSFhQlhYWHK51dMHe3Xr5+QkpIixMTECA0aNFA7dXTKlCnC2bNnhUWLFpk0rUBeXp5w7Ngx4dixYwIAYf78+cKxY8eEa9euCYJQnlbA3d1d+PXXX4UTJ04IAwcOVJtWoGPHjsKhQ4eEffv2CY899pjKtPvs7GzB29tbePXVV4VTp04J69atE5ydnatNu7ezsxPmzZsnnD17Vpg5c6Yo0+5rql9eXp4wefJkITExUbhy5Yqwa9cuoVOnTsJjjz0mPHr0yCLq9+abbwpubm5CfHy8yrTsgoIC5T7mel+a4nOsrX4XL14UPv30U+Ho0aPClStXhF9//VVo3ry50KtXL4uonyAIwrRp04SEhAThypUrwokTJ4Rp06YJCoVC2LFjhyAIlv36aaufNbx+6lSd+WdpryEDJpn77rvvhCZNmggODg5C165dhYMHD0pdpGqGDx8u+Pr6Cg4ODkKjRo2E4cOHCxcvXlQ+/vDhQ+Gtt94SPDw8BGdnZ2Hw4MFCenq6yjGuXr0qPPPMM0KdOnUELy8v4T//+Y9QXFyssk9cXJwQHBwsODg4CM2bNxd++OEHk9UpLi5OAFDtZ/To0YIglKcW+PjjjwVvb2/B0dFR6Nu3r5CamqpyjLt37wojRowQXFxcBFdXV2HMmDFCXl6eyj7Hjx8XevToITg6OgqNGjUSoqOjq5Vlw4YNwuOPPy44ODgIbdu2FbZv327S+hUUFAj9+vUTGjRoINjb2wtNmzYVxo0bV+3LRc71U1c3ACrvGXO+L8X+HGurX1pamtCrVy/B09NTcHR0FFq2bClMmTJFJY+PnOsnCILw+uuvC02bNhUcHByEBg0aCH379lUGS4Jg2a+ftvpZw+unTtWAydJeQ4UgCIJ+bVJEREREtQvHMBERERFpwYCJiIiISAsGTERERERaMGAiIiIi0oIBExEREZEWDJiIiIiItGDARERERKQFAyYiIiIiLRgwERGJJCAgAAsWLJC6GERkAgyYiMgivfbaaxg0aBAAoHfv3njvvffMdu6VK1fC3d292vYjR45g/PjxZisHEZmPndQFICKSi6KiIjg4OBj8/AYNGohYGiKSE7YwEZFFe+2115CQkIBvvvkGCoUCCoUCV69eBQCcOnUKzzzzDFxcXODt7Y1XX30Vd+7cUT63d+/emDhxIt577z14eXkhIiICADB//ny0b98edevWhb+/P9566y3k5+cDAOLj4zFmzBjk5OQozzdr1iwA1bvk0tLSMHDgQLi4uMDV1RUvvvgiMjMzlY/PmjULwcHBWL16NQICAuDm5oaXXnoJeXl5pv2nEZHeGDARkUX75ptvEBYWhnHjxiE9PR3p6enw9/dHdnY2+vTpg44dO+Lo0aOIiYlBZmYmXnzxRZXnr1q1Cg4ODti/fz+WLFkCALCxscG3336L06dPY9WqVdi9ezfef/99AED37t2xYMECuLq6Ks83efLkauUqKyvDwIEDce/ePSQkJGDnzp24fPkyhg8frrLfpUuXsHXrVvz+++/4/fffkZCQgOjoaBP9t4jIUOySIyKL5ubmBgcHBzg7O8PHx0e5feHChejYsSO++OIL5bYVK1bA398f58+fx+OPPw4AeOyxxzB37lyVY1YeDxUQEIDPPvsMb7zxBr7//ns4ODjAzc0NCoVC5XxVxcbG4uTJk7hy5Qr8/f0BAD/++CPatm2LI0eOoEuXLgDKA6uVK1eiXr16AIBXX30VsbGx+Pzzz437xxCRqNjCRERW6fjx44iLi4OLi4vyJzAwEEB5q06FkJCQas/dtWsX+vbti0aNGqFevXp49dVXcffuXRQUFOh8/rNnz8Lf318ZLAFAmzZt4O7ujrNnzyq3BQQEKIMlAPD19UVWVpZedSUi02MLExFZpfz8fDz//POYM2dOtcd8fX2Vv9etW1flsatXr+K5557Dm2++ic8//xyenp7Yt28fxo4di6KiIjg7O4taTnt7e5W/FQoFysrKRD0HERmPARMRWTwHBweUlpaqbOvUqRM2bdqEgIAA2Nnp/lWXlJSEsrIyfPXVV7CxKW+E37Bhg9bzVdW6dWtcv34d169fV7YynTlzBtnZ2WjTpo3O5SEieWCXHBFZvICAABw6dAhXr17FnTt3UFZWhgkTJuDevXsYMWIEjhw5gkuXLuGvv/7CmDFjagx2WrZsieLiYnz33Xe4fPkyVq9erRwMXvl8+fn5iI2NxZ07d9R21YWHh6N9+/YYOXIkkpOTcfjwYYwaNQpPPvkkOnfuLPr/gIhMiwETEVm8yZMnw9bWFm3atEGDBg2QlpYGPz8/7N+/H6WlpejXrx/at2+P9957D+7u7sqWI3WCgoIwf/58zJkzB+3atcOaNWsQFRWlsk/37t3xxhtvYPjw4WjQoEG1QeNAedfar7/+Cg8PD/Tq1Qvh4eFo3rw51q9fL3r9icj0FIIgCFIXgoiIiEjO2MJEREREpAUDJiIiIiItGDARERERacGAiYiIiEgLBkxEREREWjBgIiIiItKCARMRERGRFgyYiIiIiLRgwERERESkBQMmIiIiIi0YMBERERFp8f9aAFzCnP0TrgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.plot(np.arange(len(t_arr)),t_arr)\n", "plt.xlabel(\"Iteration\")\n", "plt.ylabel(r\"Stepsize $t$\")"]}, {"cell_type": "code", "execution_count": 162, "id": "115721c3-bdbd-4d8f-bef0-a792864e0bcb", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'density')"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.hist(t_arr, bins=100, density=True, alpha=0.7, edgecolor='black')\n", "plt.axvline(x=np.mean(t_arr), color='red', label=f\"mean ∆t: {np.mean(t_arr):.4f}\")\n", "plt.xlabel(\"stepsize ∆t\")\n", "plt.ylabel(\"density\")"]}, {"cell_type": "code", "execution_count": 164, "id": "2be913da-8c28-4a24-b5ff-b98939a5f458", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "The size of tensor a (40000) must match the size of tensor b (32000) at non-singleton dimension 0", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[164], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mWass\u001b[49m\u001b[43m(\u001b[49m\u001b[43mV_arr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtarget_samples\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[156], line 39\u001b[0m, in \u001b[0;36mWass\u001b[0;34m(V1, V2)\u001b[0m\n\u001b[1;32m     37\u001b[0m V1s,_\u001b[38;5;241m=\u001b[39mtorch\u001b[38;5;241m.\u001b[39msort(V1\u001b[38;5;241m.\u001b[39mflatten())\n\u001b[1;32m     38\u001b[0m V2s,_\u001b[38;5;241m=\u001b[39mtorch\u001b[38;5;241m.\u001b[39msort(V2\u001b[38;5;241m.\u001b[39mflatten())\n\u001b[0;32m---> 39\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[43mV1s\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43mV2s\u001b[49m)\u001b[38;5;241m.\u001b[39mabs()\u001b[38;5;241m.\u001b[39mmean()\n", "\u001b[0;31mRuntimeError\u001b[0m: The size of tensor a (40000) must match the size of tensor b (32000) at non-singleton dimension 0"]}], "source": ["Wass(samples_to_use, target_samples)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}