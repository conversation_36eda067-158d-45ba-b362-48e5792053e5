#include "zbaoabz_core.hpp"
#include <cmath>
#include <random>
#include <numeric>

// Constants
static std::random_device rd;
// Use mt19937 for the original BAOAB noise
static std::mt19937 gen(rd()); 
static std::normal_distribution<double> normal_dist(0.0, 1.0);

// Vector2D class (as in header) - with L1 and L2 norms, dot product, etc.
struct Vector2D {
    double x, y;
    Vector2D() : x(0.0), y(0.0) {}
    Vector2D(double x_, double y_) : x(x_), y(y_) {}
    Vector2D operator+(const Vector2D& other) const { return Vector2D(x + other.x, y + other.y); }
    Vector2D operator-(const Vector2D& other) const { return Vector2D(x - other.x, y - other.y); }
    Vector2D operator*(double scalar)     const { return Vector2D(x * scalar, y * scalar); }
    Vector2D operator*(const Vector2D& other) const { return Vector2D(x * other.x, y * other.y); }
    double norm1() const { return std::abs(x) + std::abs(y); }
    double norm2() const { return std::sqrt(x*x + y*y); }
    double dot(const Vector2D& other) const { return x * other.x + y * other.y; }
};

double monitor_g(const Vector2D& gradU, double scale_g) {
    double norm = gradU.norm2();
    return norm / scale_g;
}

// Potentials
inline double U_funnel_2d(const Vector2D& p, double eps) {
    double x = p.x, t = p.y;
    return 0.5 * x*x * std::exp(-t) + 0.5 * eps * (x*x + t*t);
}

inline double U_channel_2d(const Vector2D& p) {
    double x = p.x, y = p.y;
    double x2 = x*x, x4 = x2*x2;
    return 100.0 * y*y/(1+10*x4) + 0.001 * (x2-9)*(x2-9);
}

inline double U_beale_2d(const Vector2D& p) {
    double x = p.x, y = p.y;
    constexpr double c1=1.5, c2=2.25, c3=2.625;
    double xy=y*x;
    double t1=c1 - x + x*y;
    double t2=c2 - x + x*y*y;
    double t3=c3 - x + x*y*y*y;
    return t1*t1 + t2*t2 + t3*t3;
}

// Standard Gradients
inline Vector2D grad_funnel_2d(const Vector2D& p, double eps) {
    double x=p.x, t=p.y;
    double gx = x*std::exp(-t) + eps*x;
    double gy = -0.5*x*x*std::exp(-t) + eps*t;
    return Vector2D(gx, gy);
}

inline Vector2D grad_channel_2d(const Vector2D& p) {
    double x=p.x, y=p.y;
    double x2=x*x, x4=x2*x2;
    double den = (1+10*x4);
    double gx = -4000.0*x*x2*y*y/(den*den) + 0.004*x*(x2-9.0);
    double gy = 200.0*y/den;
    return Vector2D(gx, gy);
}

inline Vector2D grad_beale_2d(const Vector2D& p) {
    double x=p.x, y=p.y;
    constexpr double c1=1.5, c2=2.25, c3=2.625;
    double t1=c1 - x + x*y;
    double t2=c2 - x + x*y*y;
    double t3=c3 - x + x*y*y*y;
    double gx = 2*t1*(-1+y) + 2*t2*(-1+y*y) + 2*t3*(-1+y*y*y);
    double gy = 2*t1*x   + 2*t2*2*x*y   + 2*t3*3*x*y*y;
    return Vector2D(gx, gy);
}

// New: Perturbed Gradients
Vector2D perturbed_grad_funnel(const Vector2D& p, double sigma, std::mt19937_64& gen) {
    std::normal_distribution<> N(0.0,1.0);
    double random_scale = N(gen) * sigma;
    double x = p.x, y = p.y;
    double gx = (1.0 + random_scale) * x*std::exp(-y) + (1.0 - random_scale) * 0.1*x;
    double gy = (1.0 + random_scale) * (-0.5*x*x*std::exp(-y)) + (1.0 - random_scale) * 0.1*y;
    return {gx, gy};
}

Vector2D perturbed_grad_channel(const Vector2D& p, double sigma, std::mt19937_64& gen) {
    std::normal_distribution<> N(0.0,1.0);
    double random_scale = N(gen) * sigma;
    double x = p.x, y = p.y;
    double denom = 1.0 + 10.0*std::pow(x,4);
    double gx = - (1.0 + random_scale) * 4000.0*std::pow(x,3)*y*y / std::pow(denom,2)
                + (1.0 - random_scale) * 0.004*x*(x*x - 9.0);
    double gy = (1.0 + random_scale) * 200.0*y / denom;
    return {gx, gy};
}


// Sundman kernels
double psi_of_zeta_k1(double z, double m, double M, double r) {
    double zr = std::pow(z, r);
    return m * (zr + M) / (zr + m);
}

double psi_of_zeta_k2(double z, double m, double M, double r) {
    double zr = std::pow(z, r);
    return m * (zr + M/m) / (zr + 1.0);
}

// Half-step for zeta
double z_half_step(double z_old, double alpha, double half_dtau, double gval) {
    double rho = std::exp(-alpha * half_dtau);
    return rho * z_old + (1.0 - rho)/alpha * gval;
}

// BAOAB Constants
struct BAOAB_Constants {
    double eta, xc1, xc2, xc3, vc1, vc2, vc3;
    BAOAB_Constants(double dt, double gamma, double T=1.0) {
        double gh = gamma * dt;
        eta = std::exp(-gh);
        xc1 = 0.5*dt*(1+eta);
        xc2 = 0.25*dt*dt*(1+eta);
        double tmp = -std::expm1(-2.0*gh);
        tmp = std::max(tmp, 1e-16);
        xc3 = 0.5*dt*std::sqrt(tmp * T);
        vc1 = 0.5*dt*eta;
        vc2 = 0.5*dt;
        vc3 = std::sqrt(tmp * T);
    }
};

// Single BAOAB step
template<typename GradFunc>
void BAOAB_single_step(Vector2D& x, Vector2D& v, Vector2D& gradU_prev,
                       const BAOAB_Constants& C,
                       GradFunc grad_func)
{
    // A-step
    Vector2D xi(normal_dist(gen), normal_dist(gen));
    Vector2D x_new = x + v*C.xc1 - gradU_prev*C.xc2 + xi*C.xc3;
    Vector2D gradU_new = grad_func(x_new);
    // B+O step
    Vector2D v_new = v*C.eta - gradU_prev*C.vc1 - gradU_new*C.vc2 + xi*C.vc3;
    // update
    x = x_new;
    v = v_new;
    gradU_prev = gradU_new;
}

// Core run function
SimulationResult runZBAOABZ(
    Potential pot, Kernel kern, GradientType grad_type,
    int nmax, int nmeas,
    double dtau, double gamma, double alpha, double eps, double sigma,
    double scale_g, double T, double m, double M, double r,
    std::pair<double,double> x0p,
    std::pair<double,double> v0p,
    double z0)
{
    // Unpack
    Vector2D x(x0p.first, x0p.second);
    Vector2D v(v0p.first, v0p.second);
    
    // New: Create a 64-bit generator for the perturbed gradients
    std::mt19937_64 gen64(rd());

    // Lambdas
    // Updated: grad_func now handles the choice between standard and perturbed
    auto grad_func = [&](const Vector2D& p) {
        if (grad_type == GradientType::Perturbed) {
            switch(pot) {
                case Potential::Funnel:   return perturbed_grad_funnel(p, sigma, gen64);
                case Potential::Channel:  return perturbed_grad_channel(p, sigma, gen64);
                case Potential::Beale:    // Fallback for Beale as no perturbed version was provided
                                          return grad_beale_2d(p);
            }
        }
        // Standard gradient case
        switch(pot) {
            case Potential::Funnel:   return grad_funnel_2d(p, eps);
            case Potential::Channel:  return grad_channel_2d(p);
            case Potential::Beale:    return grad_beale_2d(p);
        }
        // Should not be reached, but as a fallback
        return grad_channel_2d(p);
    };

    auto Uval = [&](const Vector2D& p) {
        switch(pot) {
            case Potential::Funnel:   return U_funnel_2d(p, eps);
            case Potential::Channel:  return U_channel_2d(p);
            case Potential::Beale:    return U_beale_2d(p);
        }
        return 0.0;
    };
    auto psi = [&](double z) {
        return (kern==Kernel::K1)
            ? psi_of_zeta_k1(z, m, M, r)
            : psi_of_zeta_k2(z, m, M, r);
    };

    SimulationResult res;
    res.x.reserve(nmax+1);
    res.y.reserve(nmax+1);
    res.dt.reserve(nmax+1);
    res.psi.reserve(nmax+1);
    res.Umean.reserve((nmax+1)/nmeas + 1);
    res.Unorm.reserve(nmax+1);

    // Initial
    Vector2D gradU = grad_func(x);
    double gval = monitor_g(gradU, scale_g);
    double zeta = z0;
    double half_dt = 0.5*dtau;
    double sum_psi = 0.0;
    // double sum_psi = 0.0, sum_Upsi = 0.0;

    // record initial
    res.x.push_back(x.x);
    res.y.push_back(x.y);
    double dt0 = psi(zeta)*dtau;
    res.dt.push_back(dt0);
    double p0 = psi(zeta);
    res.psi.push_back(p0);
    res.Unorm.push_back(gradU.norm2());

    for(int i=0; i<nmax; ++i) {
        // z half
        double zhalf = z_half_step(zeta, alpha, half_dt, gval);
        // dt
        double real_dt = psi(zhalf)*dtau;
        // step
        BAOAB_single_step(x, v, gradU, BAOAB_Constants(real_dt, gamma, T), grad_func);
        // z update
        gval = monitor_g(gradU, scale_g);
        zeta = z_half_step(zhalf, alpha, half_dt, gval);
        // record
        res.x.push_back(x.x);
        res.y.push_back(x.y);
        res.dt.push_back(real_dt);
        double pi = psi(zeta);
        res.psi.push_back(pi);
        res.Unorm.push_back(gradU.norm2());
        // sum_psi    += pi;
        // sum_Upsi   += pi * Uval(x);
        if(((i+1)%nmeas)==0) {
            double current_U = Uval(x);
            double current_Tkin = 0.5 * v.norm2() * v.norm2();
            double current_Tconf = 0.5 * x.dot(gradU);

            if(i+1==nmeas) {
                sum_psi = pi;
                res.Umean.push_back(current_U);
                res.Tkin.push_back(current_Tkin);
                res.Tconf.push_back(current_Tconf);
            } else {
                double S_old = sum_psi;
                sum_psi += pi;
                double new_Umean = res.Umean.back() * (S_old / sum_psi) 
                                 + current_U * (pi / sum_psi);
                res.Umean.push_back(new_Umean);
                double new_Tkin = res.Tkin.back() * (S_old / sum_psi) 
                                 + current_Tkin * (pi / sum_psi);
                res.Tkin.push_back(new_Tkin);
                double new_Tconf = res.Tconf.back() * (S_old / sum_psi) 
                                 + current_Tconf * (pi / sum_psi);
                res.Tconf.push_back(new_Tconf);
            }
            // res.Umean.push_back(sum_Upsi / sum_psi);
        }
    }

    return res;
}



// #include "zbaoabz_core.hpp"
// #include <cmath>
// #include <random>
// #include <numeric>

// // Constants
// static std::random_device rd;
// static std::mt19937 gen(rd());
// static std::normal_distribution<double> normal_dist(0.0, 1.0);

// // Vector2D class (as in header) - with L1 and L2 norms, dot product, etc.
// struct Vector2D {
//     double x, y;
//     Vector2D() : x(0.0), y(0.0) {}
//     Vector2D(double x_, double y_) : x(x_), y(y_) {}
//     Vector2D operator+(const Vector2D& other) const { return Vector2D(x + other.x, y + other.y); }
//     Vector2D operator-(const Vector2D& other) const { return Vector2D(x - other.x, y - other.y); }
//     Vector2D operator*(double scalar)    const { return Vector2D(x * scalar, y * scalar); }
//     Vector2D operator*(const Vector2D& other) const { return Vector2D(x * other.x, y * other.y); }
//     double norm1() const { return std::abs(x) + std::abs(y); }
//     double norm2() const { return std::sqrt(x*x + y*y); }
//     double dot(const Vector2D& other) const { return x * other.x + y * other.y; }
// };

// double monitor_g(const Vector2D& gradU, double scale_g) {
//     double norm = gradU.norm2();
//     return norm / scale_g;
// }

// // Potentials
// inline double U_funnel_2d(const Vector2D& p, double eps) {
//     double x = p.x, t = p.y;
//     return 0.5 * x*x * std::exp(-t) + 0.5 * eps * (x*x + t*t);
// }

// inline double U_channel_2d(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     double x2 = x*x, x4 = x2*x2;
//     return 100.0 * y*y/(1+10*x4) + 0.001 * (x2-9)*(x2-9);
// }

// inline double U_beale_2d(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     constexpr double c1=1.5, c2=2.25, c3=2.625;
//     double xy=y*x;
//     double t1=c1 - x + x*y;
//     double t2=c2 - x + x*y*y;
//     double t3=c3 - x + x*y*y*y;
//     return t1*t1 + t2*t2 + t3*t3;
// }

// // Gradients
// inline Vector2D grad_funnel_2d(const Vector2D& p, double eps) {
//     double x=p.x, t=p.y;
//     double gx = x*std::exp(-t) + eps*x;
//     double gy = -0.5*x*x*std::exp(-t) + eps*t;
//     return Vector2D(gx, gy);
// }

// inline Vector2D grad_channel_2d(const Vector2D& p) {
//     double x=p.x, y=p.y;
//     double x2=x*x, x4=x2*x2;
//     double den = (1+10*x4);
//     double gx = -4000.0*x*x2*y*y/(den*den) + 0.004*x*(x2-9.0);
//     double gy = 200.0*y/den;
//     return Vector2D(gx, gy);
// }

// inline Vector2D grad_beale_2d(const Vector2D& p) {
//     double x=p.x, y=p.y;
//     constexpr double c1=1.5, c2=2.25, c3=2.625;
//     double t1=c1 - x + x*y;
//     double t2=c2 - x + x*y*y;
//     double t3=c3 - x + x*y*y*y;
//     double gx = 2*t1*(-1+y) + 2*t2*(-1+y*y) + 2*t3*(-1+y*y*y);
//     double gy = 2*t1*x   + 2*t2*2*x*y   + 2*t3*3*x*y*y;
//     return Vector2D(gx, gy);
// }

// // Sundman kernels
// double psi_of_zeta_k1(double z, double m, double M, double r) {
//     double zr = std::pow(z, r);
//     return m * (zr + M) / (zr + m);
// }

// double psi_of_zeta_k2(double z, double m, double M, double r) {
//     double zr = std::pow(z, r);
//     return m * (zr + M/m) / (zr + 1.0);
// }

// // Half-step for zeta
// double z_half_step(double z_old, double alpha, double half_dtau, double gval) {
//     double rho = std::exp(-alpha * half_dtau);
//     return rho * z_old + (1.0 - rho)/alpha * gval;
// }

// // BAOAB Constants
// struct BAOAB_Constants {
//     double eta, xc1, xc2, xc3, vc1, vc2, vc3;
//     BAOAB_Constants(double dt, double gamma, double T=1.0) {
//         double gh = gamma * dt;
//         eta = std::exp(-gh);
//         xc1 = 0.5*dt*(1+eta);
//         xc2 = 0.25*dt*dt*(1+eta);
//         double tmp = -std::expm1(-2.0*gh);
//         tmp = std::max(tmp, 1e-16);
//         xc3 = 0.5*dt*std::sqrt(tmp * T);
//         vc1 = 0.5*dt*eta;
//         vc2 = 0.5*dt;
//         vc3 = std::sqrt(tmp * T);
//     }
// };

// // Single BAOAB step
// template<typename GradFunc>
// void BAOAB_single_step(Vector2D& x, Vector2D& v, Vector2D& gradU_prev,
//                        const BAOAB_Constants& C, double eps,
//                        GradFunc grad_func)
// {
//     // A-step
//     Vector2D xi(normal_dist(gen), normal_dist(gen));
//     Vector2D x_new = x + v*C.xc1 - gradU_prev*C.xc2 + xi*C.xc3;
//     Vector2D gradU_new = grad_func(x_new);
//     // B+O step
//     Vector2D v_new = v*C.eta - gradU_prev*C.vc1 - gradU_new*C.vc2 + xi*C.vc3;
//     // update
//     x = x_new;
//     v = v_new;
//     gradU_prev = gradU_new;
// }

// // Core run function
// SimulationResult runZBAOABZ(
//     Potential pot, Kernel kern,
//     int nmax, int nmeas,
//     double dtau, double gamma, double alpha, double eps,
//     double scale_g, double T, double m, double M, double r,
//     std::pair<double,double> x0p,
//     std::pair<double,double> v0p,
//     double z0)
// {
//     // Unpack
//     Vector2D x(x0p.first, x0p.second);
//     Vector2D v(v0p.first, v0p.second);
//     // Lambdas
//     auto grad_func = [&](const Vector2D& p) {
//         switch(pot) {
//             case Potential::Funnel:  return grad_funnel_2d(p, eps);
//             case Potential::Channel: return grad_channel_2d(p);
//             case Potential::Beale:   return grad_beale_2d(p);
//         }
//         return grad_channel_2d(p);
//     };
//     auto Uval = [&](const Vector2D& p) {
//         switch(pot) {
//             case Potential::Funnel:  return U_funnel_2d(p, eps);
//             case Potential::Channel: return U_channel_2d(p);
//             case Potential::Beale:   return U_beale_2d(p);
//         }
//         return 0.0;
//     };
//     auto psi = [&](double z) {
//         return (kern==Kernel::K1)
//             ? psi_of_zeta_k1(z, m, M, r)
//             : psi_of_zeta_k2(z, m, M, r);
//     };

//     SimulationResult res;
//     res.x.reserve(nmax+1);
//     res.y.reserve(nmax+1);
//     res.dt.reserve(nmax+1);
//     res.psi.reserve(nmax+1);
//     res.Umean.reserve((nmax+1)/nmeas + 1);

//     // Initial
//     Vector2D gradU = grad_func(x);
//     double gval = monitor_g(gradU, scale_g);
//     double zeta = z0;
//     double half_dt = 0.5*dtau;
//     double sum_psi = 0.0, sum_Upsi = 0.0;

//     // record initial
//     res.x.push_back(x.x);
//     res.y.push_back(x.y);
//     double dt0 = psi(zeta)*dtau;
//     res.dt.push_back(dt0);
//     double p0 = psi(zeta);
//     res.psi.push_back(p0);

//     for(int i=0; i<nmax; ++i) {
//         // z half
//         double zhalf = z_half_step(zeta, alpha, half_dt, gval);
//         // dt
//         double real_dt = psi(zhalf)*dtau;
//         // step
//         BAOAB_single_step(x, v, gradU, BAOAB_Constants(real_dt, gamma, T), eps, grad_func);
//         // z update
//         gval = monitor_g(gradU, scale_g);
//         zeta = z_half_step(zhalf, alpha, half_dt, gval);
//         // record
//         res.x.push_back(x.x);
//         res.y.push_back(x.y);
//         res.dt.push_back(real_dt);
//         double pi = psi(zeta);
//         res.psi.push_back(pi);
//         sum_psi    += pi;
//         sum_Upsi   += pi * Uval(x);
//         if(((i+1)%nmeas)==0) {
//             res.Umean.push_back(sum_Upsi / sum_psi);
//         }
//     }

//     return res;
// }
