{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from typing import TypeVar, Dict\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import copy\n", "from numpy import random\n", "import numpy as np\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["no_batches=torch.tensor(2,device=device)\n", "batch_size=1\n", "par_runs=20000\n", "X=torch.tensor([[-1, 1], [0.5, 2.0]],device=device) #X[0,:] denotes mean, X[1, :] denotes standard deviation\n", "target_mean=(X[0,:]*(X[1,:].pow(-2))).sum()/(X[1,:].pow(-2).sum())\n", "target_sd=(X[1,:].pow(-2)).sum().pow(-0.5)\n", "l2regconst=torch.tensor(1,device=device).detach()\n", "gam=torch.sqrt(l2regconst)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 2])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "@dataclass\n", "class hclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    etam1g: Tensor\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "@dataclass\n", "class BAOABhclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh/2))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-gh))\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)\n", "\n", "def U(x,v,hc,xi1,xi2):\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n", "\n", "# def func(x):\n", "#     return ((x**2)/((1+x**2).sqrt())/2)\n", "# def funcder(x):\n", "#     return (x*(x**2+2)/(2*((1+x**2)**(3/2))))\n", "\n", "def func(x):\n", "    #return ((x**2)/((1+x**2)**(0.25))/2)\n", "    return ((x**2)/2)\n", "def funcder(x):\n", "    #return (x*(3*x**2+4)/(4*((1+x**2)**(1.25))))\n", "    return (x)\n", "\n", "def V(x,batch_it):\n", "    #return((x-X[0,batch_it])**2/(2*X[1,batch_it]))\n", "    return(func((x-X[0,batch_it])/X[1,batch_it]))\n", "\n", "def grad(x,batch_it):\n", "    # res=V(x,batch_it)\n", "    # return (x-X[0,batch_it])/(X[1,batch_it]), res\n", "    return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "\n", "\n", "\n", "def UBU_step(p,hper2c,batch_it):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "    p.v-=hper2c.h*grads\n", "\n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        xi2=torch.randn_like(p.data,device=device)\n", "        p.data,p.v=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "def BAOAB_step(p,hc,batch_it,last_grad):   \n", "    \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        p.data=p.data+hc.xc1*p.v-hc.xc2*last_grad+hc.xc3*xi1\n", "\n", "    grads=grad(p,batch_it)*no_batches \n", "\n", "    with torch.no_grad():\n", "        p.v=hc.eta*p.v-hc.vc1*last_grad-hc.vc2*grads+hc.vc3*xi1\n", "\n", "    return(grads)         \n", "\n", "\n", "def EM_step(p,h,batch_it):   \n", "    with torch.no_grad():\n", "        xi1=torch.randn_like(p.data,device=device)\n", "        \n", "    grads=grad(p,batch_it)*no_batches \n", "    p.data+=p.v*h\n", "    p.v-=h*grads+gam*(h)*p.v-torch.sqrt(2*gam*h)*xi1\n", "\n", "# def UBU_step2(p, q, hper4c,batch_it_list):   \n", "#     with torch.no_grad():\n", "#         xi1=torch.randn_like(p.data,device=device)\n", "#         xi2=torch.randn_like(p.data,device=device)\n", "#         [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "#         [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "        \n", "#     grads2=grad(q,batch_it_list[0])  \n", "    \n", "#     with torch.no_grad():\n", "\n", "#         q.v-=hper4c.h*grads2\n", "#         xi1=torch.randn_like(p.data,device=device)\n", "#         xi2=torch.randn_like(p.data,device=device)\n", "#         [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "#         [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "        \n", "#         grads=grad(p, batch_it_list[2])\n", "\n", "#         p.v-=2*hper4c.h*grads\n", "\n", "#         xi1=torch.randn_like(p.data,device=device)\n", "#         xi2=torch.randn_like(p.data,device=device)\n", "#         [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "#         [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "\n", "#         grads2=grad(q, batch_it_list[1])\n", "\n", "\n", "#         #for q,grad in zip(net2.parameters(), grads2):              \n", "#         q.v-=hper4c.h*grads2\n", "\n", "#         #for p,q in zip(net.parameters(),net2.parameters()):\n", "#         xi1=torch.randn_like(p.data,device=device)\n", "#         xi2=torch.randn_like(p.data,device=device)\n", "#         [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "#         [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "    \n", "\n", "\n", "# def EM_step2(net, net2, h, gam, batch_it_list):   \n", "#     grads2,_=grad(net2, batch_it_list[0])\n", "#     grads,loss_likelihood_data=grad(net, batch_it_list[2])   \n", "#     sqrt2=torch.tensor(2).sqrt().detach()\n", "\n", "#     with torch.no_grad():\n", "#         for p,gradp in zip(net2.parameters(), grads2):              \n", "#             p.xi=torch.randn_like(p.data,device=device)\n", "#             p.data+=p.v*h/2\n", "#             p.v-=(h/2)*gradp+gam*(h/2)*p.v-torch.sqrt(2*gam*h/2)*p.xi\n", "\n", "#     grads2,_=grad(net2, batch_it_list[1])\n", "\n", "#     with torch.no_grad():\n", "#         for p,gradp in zip(net2.parameters(), grads2):              \n", "#             p.xi2=torch.randn_like(p.data,device=device)\n", "#             p.data+=p.v*h/2\n", "#             p.v-=(h/2)*gradp+gam*(h/2)*p.v-torch.sqrt(2*gam*h/2)*p.xi2\n", "\n", "#         for p,q,gradp in zip(net.parameters(),net2.parameters(), grads):              \n", "#             p.data+=p.v*h\n", "#             p.v-=(h)*gradp+gam*(h)*p.v-torch.sqrt(2*gam*h)*(q.xi+q.xi2)/sqrt2\n", "\n", "\n", "#     return(loss_likelihood_data)\n", "\n", "\n", "\n", "\n", "\n", "\n", "# def BAOAB_step2(net, net2, hc, hper2c, batch_it_list,last_grad,last_grad2):   \n", "\n", "#     with torch.no_grad():\n", "#         for p,grad in zip(net2.parameters(), last_grad2):\n", "#             p.xi=torch.randn_like(p.data,device=device)\n", "#             p.data=p.data+hper2c.xc1*p.v-hper2c.xc2*grad+hper2c.xc3*p.xi\n", "\n", "#     grads2,_=grad(net2, batch_it_list[0])\n", "\n", "#     with torch.no_grad():\n", "#         for p,grad,gradn in zip(net2.parameters(), last_grad2,grads2):              \n", "#             p.v=hper2c.eta*p.v-hper2c.vc1*grad-hper2c.vc2*gradn+hper2c.vc3*p.xi\n", "\n", "#     with torch.no_grad():\n", "#         for p,grad in zip(net2.parameters(), grads2):\n", "#             p.xi2=torch.randn_like(p.data,device=device)\n", "#             p.data=p.data+hper2c.xc1*p.v-hper2c.xc2*grad+hper2c.xc3*p.xi2\n", "\n", "#     grads2n,_=grad(net2, batch_it_list[1])\n", "\n", "#     with torch.no_grad():\n", "#         for p,grad,gradn in zip(net2.parameters(), grads2,grads2n):              \n", "#             p.v=hper2c.eta*p.v-hper2c.vc1*grad-hper2c.vc2*gradn+hper2c.vc3*p.xi2\n", "\n", "#     sqrt2=torch.tensor(2).sqrt().detach()\n", "#     with torch.no_grad():\n", "#         for p,q,grad in zip(net.parameters(),net2.parameters(), last_grad):\n", "#             p.data=p.data+hc.xc1*p.v-hc.xc2*grad+hc.xc3*(q.xi+q.xi2)/sqrt2\n", "\n", "#     grads,loss_likelihood_data=grad(net, batch_it_list[2])\n", "\n", "#     with torch.no_grad():\n", "#         for p,q,grad,gradn in zip(net.parameters(),net2.parameters(), last_grad,grads):              \n", "#             p.v=hc.eta*p.v-hc.vc1*grad-hc.vc2*gradn+hc.vc3*(q.xi+q.xi2)/sqrt2\n", "\n", "#     return(loss_likelihood_data,grads,grads2n)\n", "\n", "    # eta=to_data_type(exp(-h*gam/2));\n", "    # h2=to_data_type(h/2);\n", "    # eta2=to_data_type(exp(-h2*gam/2));\n", "\n", "    # grad=gradp;\n", "    # xip=R;\n", "\n", "    # #xn=x+(h/2*(1+eta))*v-((h^2/4)*(1+eta))*grad+((h/2)*realsqrt(1-eta^2))*xip;\n", "    # #xn=x+xc1*v-xc2*grad+xc3*xip\n", "\n", "    # gradpn=grad_lpost(xn);\n", "    # vn=eta*(v-(h/2)*grad)+(realsqrt(1-eta^2))*xip-(h/2)*gradpn;    \n", "    # #vn=eta*v-vc1*grad-vc2*gradpn+vc3*xip\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)   # batch_it modulo 2*no_batches\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# no_batches=torch.tensor(2,device=device) # Nm in paper\n", "# batch_size=1                             # Nb in paper\n", "# par_runs=20000\n", "# X=torch.tensor([[-1, 1], [0.5, 2.0]],device=device) #X[0,:] denotes mean, X[1, :] denotes standard deviation\n", "# target_mean=(X[0,:]*(X[1,:].pow(-2))).sum()/(X[1,:].pow(-2).sum())\n", "# target_sd=(X[1,:].pow(-2)).sum().pow(-0.5)\n", "# l2regconst=torch.tensor(1,device=device).detach()\n", "# gam=torch.sqrt(l2regconst)\n", "\n", "def SMS_UBU(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)   # par_runs=20000\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    if(epoch%2==0):\n", "      # np.title transform the [0, 1, ..., no_batches-1] to shape (par_runs, no_batches)\n", "      # rng.permuted(, axis=1) permute the columns of the array in each row\n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*no_batches+b\n", "      ind=ind_create(it)\n", "      UBU_step(p,hper2c,rperm[:,ind])\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "  \n", "\n", "\n", "\n", "def SG_UBU(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    for i in range(no_batches):\n", "\n", "      ind=torch.randint(high=no_batches,size=(par_runs,)).int()\n", "\n", "      UBU_step(p,hper2c,ind)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "\n", "def SG_UBU_without_replacement(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=hper2const(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      ind=rperm[:,i]\n", "      UBU_step(p,hper2c,ind)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "\n", "def SMS_BAOAB(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)   # (par_runs,)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  \n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()  # random ind from [0, no_batches-1] for each par_runs\n", "  grads=grad(p.data,ind)       # grad(x,batch_it): return funcder((x-X[0,batch_it])/(X[1,batch_it]))/X[1,batch_it]\n", "  for epoch in range(num_epochs):\n", "    if(epoch%2==0):\n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*no_batches+b\n", "      ind=ind_create(it)\n", "      grads=BAOAB_step(p,hper2c,ind,grads)     \n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "\n", "def SG_BAOAB(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "    \n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    for i in range(no_batches):\n", "      ind=torch.randint(high=no_batches,size=(par_runs,)).int()     \n", "      grads=BAOAB_step(p,hper2c,ind,grads)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def SG_BAOAB_without_replacement(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    hper2c=BAOAB_hconst(h,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "  ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "  grads=grad(p.data,ind)\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      ind=rperm[:,i]\n", "      grads=BAOAB_step(p,hper2c,ind,grads)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "\n", "def SMS_EM(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    if(epoch%2==0):\n", "      rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*no_batches+b\n", "      ind=ind_create(it)\n", "      EM_step(p,h,rperm[:,ind])\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "\n", "def SG_EM(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    for i in range(no_batches):\n", "      ind=torch.randint(high=no_batches,size=(par_runs,)).int()    \n", "      EM_step(p,h,ind)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def SG_EM_without_replacement(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    for i in range(no_batches):\n", "      ind=rperm[:,i]\n", "\n", "      EM_step(p,h,ind)\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data\n", "    \n", "  return(V_arr)\n", "\n", "def SG_UBU2(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  q=torch.zeros(par_runs,device=device)\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    V_arr2=torch.zeros([par_runs,num_epochs],device=device).detach()  \n", "    hper4c=hper2const(h/2,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "    q.v=copy.deepcopy(p.v).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    for i in range(no_batches):\n", "      ind=torch.randint(high=no_batches,size=(par_runs,)).int()\n", "      ind2=torch.randint(high=no_batches,size=(par_runs,)).int()\n", "      rflip=(torch.rand((par_runs,))<0.5).int()\n", "      indc=ind*rflip+ind2*(1-rflip)\n", "      \n", "      batch_it_list=[ind.numpy(), ind2.numpy(), indc.numpy()]\n", "      UBU_step2(p,q,hper4c,batch_it_list)\n", "\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data#V(p.data,0)+V(p.data,1)\n", "      V_arr2[:,epoch]=q.data#V(q.data,0)+V(q.data,1)\n", "    \n", "    #       \n", "  return(V_arr,V_arr2)\n", "\n", "def SG_UBU2_without_replacement(num_epochs,h,gam):\n", "\n", "  p=torch.zeros(par_runs,device=device)\n", "  q=torch.zeros(par_runs,device=device)\n", "  rng = np.random.default_rng()\n", "\n", "  with torch.no_grad():\n", "    V_arr=torch.zeros([par_runs,num_epochs],device=device).detach()\n", "    V_arr2=torch.zeros([par_runs,num_epochs],device=device).detach()  \n", "    hper4c=hper2const(h/2,gam)\n", "  #Initialise velocities\n", "    p.v = torch.randn_like(p,device=device).detach()\n", "    q.v=copy.deepcopy(p.v).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    rperm=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    rperm2=rng.permuted(np.tile(np.arange(no_batches,dtype=int),(par_runs,1)),axis=1)\n", "    rperm2=np.concatenate((rperm,rperm2),axis=1)\n", "    #print(rperm2.shape)\n", "    #rperm=random.permutation(list(range(no_batches)))      \n", "    #rperm2=np.concatenate((rperm,random.permutation(list(range(no_batches)))))\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*no_batches+b\n", "      ind=(2*it)%no_batches\n", "      ind2=(2*it+1)%no_batches            \n", "      indc=it%no_batches          \n", "      batch_it_list=[rperm2[:,ind], rperm2[:,ind2], rperm[:,indc]]\n", "      UBU_step2(p,q,hper4c,batch_it_list)\n", "\n", "\n", "    with torch.no_grad():\n", "      V_arr[:,epoch]=p.data#V(p.data,0)+V(p.data,1)\n", "      V_arr2[:,epoch]=q.data#V(q.data,0)+V(q.data,1)\n", "    \n", "  return(V_arr,V_arr2)\n", "\n", "def Wass(V1,V2):\n", "  V1s,_=torch.sort(V1.flatten())\n", "  V2s,_=torch.sort(V2.flatten())\n", "  return (V1s-V2s).abs().mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04120809\n", "Wasserstein distance: 0.11456731\n", "Wasserstein distance: 0.02731412\n", "Wasserstein distance: 0.04081383\n", "Wasserstein distance: 0.48034728\n", "Wasserstein distance: 0.08763517\n", "Wasserstein distance: nan\n", "Wasserstein distance: nan\n", "Wasserstein distance: 1021263343469042073600.00000000\n", "Wasserstein distance: 0.00704511\n", "Wasserstein distance: 0.04804970\n", "Wasserstein distance: 0.00608082\n", "Wasserstein distance: 0.00726487\n", "Wasserstein distance: 0.12248087\n", "Wasserstein distance: 0.00859114\n", "Wasserstein distance: 0.18261121\n", "Wasserstein distance: 0.36469629\n", "Wasserstein distance: 0.19017397\n", "Wasserstein distance: 0.00215159\n", "Wasserstein distance: 0.02288182\n", "Wasserstein distance: 0.00164659\n", "Wasserstein distance: 0.00192498\n", "Wasserstein distance: 0.05160223\n", "Wasserstein distance: 0.00166304\n", "Wasserstein distance: 0.06506592\n", "Wasserstein distance: 0.10277259\n", "Wasserstein distance: 0.06616536\n", "Wasserstein distance: 0.00043933\n", "Wasserstein distance: 0.01132009\n", "Wasserstein distance: 0.00040026\n", "Wasserstein distance: 0.00047019\n", "Wasserstein distance: 0.02408546\n", "Wasserstein distance: 0.00039642\n", "Wasserstein distance: 0.02874113\n", "Wasserstein distance: 0.04274442\n", "Wasserstein distance: 0.02872578\n"]}], "source": ["Wass_arr=torch.zeros(4,9).detach()\n", "methods_list=[SMS_UBU,SG_UBU,SG_UBU_without_replacement,SMS_BAOAB,SG_BAOAB,SG_BAOAB_without_replacement, SMS_EM,SG_EM,SG_EM_without_replacement]\n", "for it in range(4):\n", "    for mit in range(9):\n", "        rat=pow(2,it)\n", "        num_epochs=int(2000*rat)\n", "        h=torch.tensor(0.25)/rat\n", "        V_arr=methods_list[mit](num_epochs,h,gam)\n", "\n", "        V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "        diff=Wass(V_arr[:,50*rat:],V2_arr[:,50*rat:])\n", "        print(\"Wasserstein distance:\",f'{diff:.8f}')\n", "        Wass_arr[it,mit]=diff"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[4.1208e-02, 1.1457e-01, 2.7314e-02, 4.0814e-02, 4.8035e-01, 8.7635e-02,\n", "                nan,        nan, 1.0213e+21],\n", "        [7.0451e-03, 4.8050e-02, 6.0808e-03, 7.2649e-03, 1.2248e-01, 8.5911e-03,\n", "         1.8261e-01, 3.6470e-01, 1.9017e-01],\n", "        [2.1516e-03, 2.2882e-02, 1.6466e-03, 1.9250e-03, 5.1602e-02, 1.6630e-03,\n", "         6.5066e-02, 1.0277e-01, 6.6165e-02],\n", "        [4.3933e-04, 1.1320e-02, 4.0026e-04, 4.7019e-04, 2.4085e-02, 3.9642e-04,\n", "         2.8741e-02, 4.2744e-02, 2.8726e-02]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0412, 0.1147, 0.0273, 0.0408, 0.4789, 0.0879,    nan,    nan,    nan],\n", "        [0.0071, 0.0483, 0.0060, 0.0071, 0.1226, 0.0086, 0.1826, 0.3648, 0.1903],\n", "        [0.0021, 0.0231, 0.0017, 0.0018, 0.0514, 0.0016, 0.0652, 0.1025, 0.0659],\n", "        [0.0005, 0.0113, 0.0005, 0.0006, 0.0240, 0.0005, 0.0288, 0.0427, 0.0289]])"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["Wass_arr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'pickle' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[73], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m filepath\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWass_distance.pickle\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(filepath,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwb\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[0;32m----> 3\u001b[0m     \u001b[43mpickle\u001b[49m\u001b[38;5;241m.\u001b[39mdump(Wass_arr\u001b[38;5;241m.\u001b[39mnumpy(),file)\n", "\u001b[0;31mNameError\u001b[0m: name 'pickle' is not defined"]}], "source": ["import pickle\n", "filepath=\"Wass_distance.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "    pickle.dump(Wass_arr.numpy(),file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.11451876\n"]}], "source": ["rat=2\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.02744237\n"]}], "source": ["rat=2\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU_without_replacement(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.47818235\n"]}], "source": ["rat=2\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_BAOAB(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.08776037\n"]}], "source": ["rat=2\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_BAOAB_without_replacement(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04085172\n"]}], "source": ["rat=2\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_BAOAB(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.18276775\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_EM(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.36453941\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_EM(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.19027656\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_EM_without_replacement(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.10278181\n"]}], "source": ["rat=8\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_EM(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04270706\n"]}], "source": ["rat=16\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_EM(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00708137\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04815945\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00609905\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU_without_replacement(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00719593\n"]}], "source": ["rat=4\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_BAOAB(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00203311\n"]}], "source": ["rat=8\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=8\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00168177\n"]}], "source": ["rat=8\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU_without_replacement(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00185550\n"]}], "source": ["rat=8\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_BAOAB(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00051567\n"]}], "source": ["rat=16\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SMS_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.01136027\n"]}], "source": ["rat=16\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00044837\n"]}], "source": ["rat=16\n", "num_epochs=int(2000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU_without_replacement(num_epochs,h,gam)\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00047875\n"]}], "source": ["rat=16\n", "num_epochs=int(4000*rat)\n", "h=torch.tensor(0.5)/rat\n", "V_arr=SG_UBU_without_replacement(num_epochs,h,gam)\n", "\n", "V2_arr=torch.randn_like(V_arr,device=device)*target_sd+target_mean\n", "diff=Wass(V_arr[:,200*rat:],V2_arr[:,200*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-0.7080, -1.3768, -1.2743,  ..., -0.8878, -1.2427, -1.3419],\n", "        [-0.6147, -1.2203, -1.1726,  ..., -0.8398, -1.0877, -0.9577],\n", "        [-0.2342, -1.2017, -1.4464,  ..., -0.7463, -0.7329, -0.8420],\n", "        ...,\n", "        [-0.2312, -0.5815, -0.8356,  ..., -0.9800, -0.3326, -0.6403],\n", "        [-0.8650, -1.5125, -1.7752,  ..., -1.2877, -1.0383, -1.1100],\n", "        [-0.5952, -1.1166, -1.0417,  ..., -0.4319, -1.0199, -1.5974]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["V_arr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'SMS_UBU2' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m num_epochs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mint\u001b[39m(\u001b[38;5;241m2000\u001b[39m\u001b[38;5;241m*\u001b[39mrat)\n\u001b[1;32m      3\u001b[0m h\u001b[38;5;241m=\u001b[39m torch\u001b[38;5;241m.\u001b[39mtensor(\u001b[38;5;241m0.5\u001b[39m)\u001b[38;5;241m/\u001b[39mrat\n\u001b[0;32m----> 4\u001b[0m V_arr,V_arr2\u001b[38;5;241m=\u001b[39mSMS_UBU2(num_epochs,h,gam)\n\u001b[1;32m      5\u001b[0m diff\u001b[38;5;241m=\u001b[39mWass(V_arr[:,\u001b[38;5;241m100\u001b[39m\u001b[38;5;241m*\u001b[39mrat:],V_arr2[:,\u001b[38;5;241m100\u001b[39m\u001b[38;5;241m*\u001b[39mrat:])\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m#diff=Wass(V_arr[:,range(1000*rat+1,5000,2)],V_arr2[:,range(1000*rat+1,5000,2)])\u001b[39;00m\n", "\u001b[0;31mNameError\u001b[0m: name 'SMS_UBU2' is not defined"]}], "source": ["rat=2\n", "num_epochs=int(2000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SMS_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "#diff=Wass(V_arr[:,range(1000*rat+1,5000,2)],V_arr2[:,range(1000*rat+1,5000,2)])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00687544\n"]}], "source": ["rat=4\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SMS_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00088318\n"]}], "source": ["rat=8\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SMS_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00027838\n"]}], "source": ["rat=16\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SMS_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00007748\n"]}], "source": ["rat=32\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SMS_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00001989\n"]}], "source": ["rat=64\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SMS_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.23515014\n"]}], "source": ["rat=1\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04478037\n"]}], "source": ["rat=2\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00606991\n"]}], "source": ["rat=4\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00092218\n"]}], "source": ["rat=8\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00021774\n"]}], "source": ["rat=16\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00006514\n"]}], "source": ["rat=32\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00001757\n"]}], "source": ["rat=64\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2_without_replacement(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.04525045\n"]}], "source": ["rat=1\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[:,100*rat:],V_arr2[:,100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.07995587\n"]}], "source": ["rat=2\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[100*rat:],V_arr2[100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.01214406\n"]}], "source": ["rat=4\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[100*rat:],V_arr2[100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00608285\n"]}], "source": ["rat=8\n", "num_epochs=int(1000*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[100*rat:],V_arr2[100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00311218\n"]}], "source": ["rat=16\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[100*rat:],V_arr2[100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00155235\n"]}], "source": ["rat=32\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[100*rat:],V_arr2[100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wasserstein distance: 0.00084320\n"]}], "source": ["rat=64\n", "num_epochs=int(500*rat)\n", "h= torch.tensor(0.5)/rat\n", "V_arr,V_arr2=SG_UBU2(num_epochs,h,gam)\n", "diff=Wass(V_arr[100*rat:],V_arr2[100*rat:])\n", "print(\"Wasserstein distance:\",f'{diff:.8f}')"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Pytorch MNIST.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}