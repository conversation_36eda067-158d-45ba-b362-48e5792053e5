#pragma once
#include <vector>
#include <random>
#include <cmath>

enum class GradientType { Standard=0, Perturbed=1 };

struct SimResult9D {
    // Trajectory slices for plotting θ vs x1
    std::vector<double> theta, x1, dt, psi;
    // Running means (reweighted by psi)
    std::vector<double> Umean, Tkin, Tconf;
    // Noise-control traces
    std::vector<double> sigmas, sigma_zetas;
};

SimResult9D runZBAOABZ_funnel9d(
    // Integrator/system
    int         nmax,
    int         nmeas,
    double      dtau,
    double      gamma,
    double      T,               // temperature
    GradientType grad_type,

    // SamAdams step-size control (dt)
    double      alpha_dt,
    double      scale_g,
    double      m_dt,
    double      M_dt,
    double      r_dt,
    double      z0_dt,

    // Funnel specifics
    int         n_sat,           // satellites (use 8 for 9D total)
    double      sigma_x2,        // confining variance on x (20 in paper)
    const std::vector<double>& q0,  // initial position [θ, x1..xn]
    const std::vector<double>& v0,  // initial velocity

    // Noise control (sigma)
    int         burn_in_noise,
    double      initial_sigma,
    double      sigma_min,
    double      sigma_max,
    double      alpha_sigma,
    double      threshold_sigma,
    double      steepness_sigma
);
