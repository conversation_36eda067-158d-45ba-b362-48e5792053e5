# Partitioned Integrators for Thermodynamic Parameterization Of Neural Networks
Supplement code to our Foundations of Data Science 2019 paper. <br>
See: https://www.aimsciences.org/article/doi/10.3934/fods.2019019 <br>
Or for the arXiv version: https://arxiv.org/abs/1908.11843

Run the Jupyter notebook ```AdLaLa.ipynb``` to see the gap in test accuracy between AdLaLa and Adam for training a fully connected feed-forward network on a binary classification spiral dataset.

![testacc_AdLaLa.pdf](https://github.com/TiffanyVlaar/ThermodynamicParameterizationOfNNs/files/7739550/testacc_AdLaLa.pdf)


