{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "xx = np.arange(-20, 20, 0.1)\n", "k = 1\n", "yy = 1/(1+np.exp(k * xx))\n", "\n", "plt.plot(xx, yy)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%% Imports\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import time\n", "import arviz as az\n", "\n", "\n", "import torchvision\n", "from torchvision import datasets, transforms\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_auc_score\n", "\n", "#%% ZBAOABZ\n", "\n", "class ZBAOABZ(nn.Module):\n", "\n", "    def __init__(self, model, train_loader, test_loader, ood_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq, lr_schedule=None, T_schedule=None):\n", "        super(ZBAOABZ, self).__init__()\n", "        self.model = model\n", "        self.train_loader = train_loader\n", "        self.test_loader = test_loader\n", "        self.ood_loader = ood_loader\n", "        self.criterion = criterion\n", "        self.lr = None                      # stepsize dt\n", "        self.weight_decay = weight_decay    # weight decay for L2 regularization\n", "        self.gamma = gamma\n", "        self.alpha = alpha\n", "        self.alpha2 = alpha2                # 1/Omega\n", "        self.T = temperature\n", "        self.epochs = epochs\n", "        self.device = device\n", "        self.meas_freq = meas_freq\n", "        self.running_loss = None\n", "        \n", "        self.dtau = dtau\n", "        self.zeta = None\n", "        self.a_gamma = np.exp(-self.gamma)  # exp(-γ)\n", "        self.a = None\n", "        self.sqrt_aT = None\n", "        self.gradnorm = None\n", "        self.alpha_inv = 1/self.alpha\n", "        self.exptau_half = np.exp(-0.5*self.alpha*self.dtau)\n", "        self.r=0.25\n", "        self.m=m\n", "        self.M=M\n", "        self.lr_schedule = lr_schedule\n", "        self.T_schedule = T_schedule\n", "        self.T_idx = 0\n", "        \n", "        # Lists to store metrics over time\n", "        self.dt_raw = []\n", "        self.zeta_raw = []\n", "        self.ess_raw = []\n", "        self.gradnorms = []\n", "        self.loss_history_for_ess = [] # For ESS calculation\n", "        \n", "        self.results_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \n", "                               \"Tkin\", \"Tconf\", \"NLL\", \"Brier\", \"ECE\", \n", "                               \"OOD AUC\", \"ESS\", \"dt\", \"zeta\"]\n", "        \n", "    def train(self):\n", "        \"\"\"\n", "        Main training function. Will return results array.\n", "        \"\"\"\n", "                \n", "        datasize = len(self.train_loader.dataset)\n", "        \n", "        squeeze = True if type(self.criterion) == torch.nn.modules.loss.BCELoss else False\n", "\n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\\n\")\n", "        \n", "        # create momentum buffers\n", "        for p in self.model.parameters():              \n", "            p.buf = torch.normal(torch.zeros(p.size()), np.sqrt(0.5*self.T)*torch.ones(p.size())).to(self.device, non_blocking=True) # N(0, √(0.5T))\n", "        \n", "        # compute initial gradients\n", "        (data, target) = next(iter(self.train_loader))      \n", "        data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)\n", "        self.fill_gradients(data, target, squeeze, datasize)\n", "        \n", "        # Store initial results\n", "        results = [] \n", "        initial_metrics = evaluate(self.model, self.train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "        results.append(initial_metrics)\n", "        \n", "        start_time = time.time()\n", "        print(\"Starting ZBAOABZ sampling...\")     \n", "        \n", "        # initial zeta and stepsize\n", "        self.set_gradnorm()\n", "        self.zeta = self.gradnorm\n", "        self.Sundman_transform()   # intial dt = psi * dtau\n", "        \n", "        self.dt_raw.append(self.lr)\n", "        self.zeta_raw.append(self.zeta)\n", "        self.ess_raw.append(np.nan) # ESS is not defined for a single point\n", "\n", "        for epoch in range(1, self.epochs+1):               # sampling loop\n", "            self.model.train()\n", "            \n", "            # adjust stepsize and temperature (optional)\n", "            if self.lr_schedule is not None and epoch % self.lr_schedule[0]==0:\n", "                self.dtau *= self.lr_schedule[1]\n", "\n", "            if self.T_schedule is not None and epoch % self.T_schedule[self.T_idx][0]==0:\n", "                new_T = self.T_schedule[self.T_idx][1]\n", "                print(\"Adjusting temperature to \", new_T)\n", "                self.change_temperature(new_T)\n", "                self.T_idx += 1\n", "                if self.T_idx == len(self.T_schedule):\n", "                    self.T_schedule = None\n", "            \n", "            for batch_idx, (data, target) in enumerate(self.train_loader):\n", "                \n", "                self.Z_step()\n", "                self.Sundman_transform()   # dt\n", "                self.a = self.a_gamma**(self.lr)   # a = exp(-γ Δt)\n", "                self.sqrt_aT = torch.sqrt( (1 - self.a**2)*self.T )\n", "                \n", "                self.update_params_BAOA()     # BAOA-steps\n", "                \n", "                data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)   # compute new gradients\n", "                self.fill_gradients(data, target, squeeze, datasize)\n", "                \n", "                self.update_params_B()     # B-step\n", "                \n", "                self.set_gradnorm()\n", "                self.Z_step()       \n", "                \n", "                self.Sundman_transform()\n", "\n", "                # Store loss for ESS calculation (at every step for higher resolution)\n", "                self.loss_history_for_ess.append(self.running_loss.item() / datasize)\n", "\n", "            if epoch % self.meas_freq == 0:\n", "                metrics = evaluate(self.model, self.train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "                results.append(metrics)\n", "                \n", "                # Calculate and store metrics for this epoch\n", "                self.dt_raw.append(self.lr)\n", "                self.zeta_raw.append(self.zeta)\n", "                ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "                self.ess_raw.append(ess_value)\n", "\n", "\n", "            print(f\"ZBAOABZ EPOCH {epoch} DONE! | Test Accu: {results[-1][2]:.2f}% | dt: {self.lr.item():.6f}\")              \n", "        \n", "        end_time = time.time()\n", "        print(\"Training took {} seconds, i.e {} minutes, with {} seconds per epoch!\"\n", "              .format(end_time-start_time, (end_time-start_time)/60, (end_time-start_time)/self.epochs))\n", "        \n", "        # Finalize results processing\n", "        results = np.array(results)\n", "\n", "        # Ensure all metric lists have the same length\n", "        dt_np = np.array([i.cpu().item() for i in self.dt_raw])\n", "        zeta_np = np.array([i.cpu().item() for i in self.zeta_raw])\n", "        ess_np = np.array(self.ess_raw)\n", "\n", "        epoch_axis = np.arange(0, len(results[:,0])) * self.meas_freq\n", "        results = np.column_stack((epoch_axis, results, ess_np, dt_np, zeta_np))\n", "\n", "        # Update header to match the order in column_stack\n", "        final_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \"Tkin\", \"Tconf\", \n", "                        \"NLL\", \"Brier\", \"ECE\", \"OOD AUC\", \"ESS\", \"dt\", \"zeta\"]\n", "\n", "        return results, final_header\n", "\n", "    def update_params_BAOA(self):\n", "        \"\"\"\n", "        Performs B-, A-, O-, and A-step on model. Forces are assumed to be stored in parameter gradients.\n", "        \"\"\"     \n", "        for p in self.model.parameters():\n", "            p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                  # B-step: p.buf = p.buf - 0.5*Δt*∇U(x)\n", "            p.data.add_(p.buf, alpha=0.5*self.lr)                        # A-step: x = x + 0.5*Δt*p.buf\n", "            \n", "            eps = torch.randn(p.size()).to(self.device, non_blocking=True)       # O-step, ε ~ N(0,I)\n", "            p.buf.mul_(self.a)   \n", "            p.buf.add_(eps, alpha=self.sqrt_aT)                          # p.buf = a * p.buf + √[(1-a²)T] * ε\n", "            \n", "            p.data.add_(p.buf, alpha=0.5*self.lr)                        # A-step                                    \n", "\n", "    def update_params_B(self):\n", "        \"\"\"\n", "        Performs B-step on model. Forces are assumed to be stored in parameter gradients.\n", "        \"\"\"\n", "        for p in self.model.parameters():\n", "            p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                  # B-step                                 \n", "\n", "    def fill_gradients(self, data, target, squeeze, datasize):\n", "        \"\"\"\n", "        Fills gradients of the model on batch (data, target).\n", "        \"\"\"\n", "        self.model.zero_grad()\n", "        output = self.model(data)\n", "        if squeeze: output=output.squeeze()\n", "        self.running_loss = self.criterion(output, target)*datasize\n", "        self.running_loss.backward()\n", "        \n", "        for p in list(self.model.parameters()):\n", "            p.grad.data.add_(p.data, alpha=self.weight_decay)   # ∇U = ∇U + weight_decay * x\n", "\n", "    def set_gradnorm(self):\n", "        self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters())\n", "        self.gradnorm *= self.alpha2   # g(x,p) = Ω^{-1}||∇U(x)||^s (s=2)\n", "    \n", "    def Z_step(self):  # ζ = exp(-0.5αΔτ)ζ + α^{-1}(1-exp(-0.5αΔτ))g(x,p)\n", "        self.zeta = self.exptau_half * self.zeta + self.alpha_inv * (1-self.exptau_half) * self.gradnorm\n", "    \n", "    def Sundman_transform(self):\n", "        zeta_r = self.zeta**self.r\n", "        self.lr = self.dtau * self.m * (zeta_r + self.M) / (zeta_r + self.m)   # Δt = Δτ * m * (ζ^r + M)/(ζ^r + m)\n", "        \n", "    def change_temperature(self, T):\n", "        \"\"\"\n", "        Changes the temperature of the sampler.\n", "        \"\"\"\n", "        self.T = T\n", "\n", "#%% Helper functions for metrics\n", "# Expected Calibration Error (ECE): Measures how well-calibrated the model's confidence scores are.\n", "\n", "# Negative Log-Likelihood (NLL) & Brier Score: Proper scoring rules that assess the quality of the predicted probability distributions.\n", "\n", "# OOD AUC: Evaluates the model's ability to distinguish between in-distribution (MNIST) and out-of-distribution (Fashion-MNIST) data \n", "# based on predictive uncertainty (entropy). An AUC of 1.0 would be perfect.\n", "\n", "# Effective Sample Size (ESS): Calculated for the training loss to measure the sampling efficiency. \n", "# A higher ESS indicates less correlation between samples and more efficient exploration.\n", "\n", "def calculate_ess(chain):\n", "    \"\"\"Calculate Effective Sample Size (ESS) for a chain.\"\"\"\n", "    if len(chain) < 10: # ESS is unreliable for very short chains\n", "        return np.nan\n", "    return az.ess(chain).item()\n", "    \n", "def calculate_ece(preds, labels, n_bins=15):\n", "    \"\"\"\n", "    Calculate Expected Calibration Error.\n", "    ECE=\\\\sum_{b=1}^B\\\\frac{n_b}{N}|acc(b)-conf(b)|\n", "    b: b-th bin; n_b: number of samples in b-th bin; N: total number of samples\n", "    acc(b): accuracy of samples in b-th bin; conf(b): confidence of samples in b-th bin\n", "    \"\"\"\n", "    bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "    bin_lowers = bin_boundaries[:-1]\n", "    bin_uppers = bin_boundaries[1:]\n", "\n", "    confidences, predictions = torch.max(preds, 1)\n", "    accuracies = predictions.eq(labels)\n", "\n", "    ece = torch.zeros(1, device=preds.device)\n", "    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "        in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "        prop_in_bin = in_bin.float().mean()\n", "        if prop_in_bin.item() > 0:\n", "            accuracy_in_bin = accuracies[in_bin].float().mean()\n", "            avg_confidence_in_bin = confidences[in_bin].mean()\n", "            ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "    return ece.item()\n", "\n", "def calculate_brier_score(preds, labels):\n", "    \"\"\"\n", "    Calculate Brier Score.\n", "    BS=\\\\frac{1}{N}\\\\sum_{t=1}^N\\\\sum_{i=1}^R\\\\left(f_{ti}-o_{ti}\\\\right)^2\n", "    N: number of samples; R: number of classes\n", "    f_{ti}: predicted probability of t-th sample being in i-th class\n", "    o_{ti}: one-hot label of t-th sample being in i-th class (predicted label)\n", "    \"\"\"\n", "    one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "    return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "def get_predictive_entropy(preds):\n", "    \"\"\"\n", "    Calculate predictive entropy for given predictions.\n", "    H(p)=-\\\\sum_{i=1}^R p_i * log(p_i)\n", "    \"\"\"\n", "    return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "\n", "#%% external evaluate function\n", "\n", "def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "    \"\"\"\n", "    Returns a list containing:\n", "    [train loss, train accuracy, test accuracy, Tkin, Tconf, NLL, Brier Score, ECE, OOD AUC]\n", "    \"\"\"\n", "    model.eval()\n", "    \n", "    all_test_preds = []\n", "    all_test_labels = []\n", "    all_ood_preds = []\n", "\n", "    # --- In-Distribution (Test Set) Evaluation ---\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            probs = torch.exp(output) # Convert log-probabilities to probabilities\n", "            all_test_preds.append(probs)\n", "            all_test_labels.append(target)\n", "    \n", "    all_test_preds = torch.cat(all_test_preds, dim=0)\n", "    all_test_labels = torch.cat(all_test_labels, dim=0)\n", "\n", "    # --- Out-of-Distribution (OOD Set) Evaluation ---\n", "    with torch.no_grad():\n", "        for data, _ in ood_loader:\n", "            data = data.to(device, non_blocking=True)\n", "            output = model(data)\n", "            all_ood_preds.append(torch.exp(output))\n", "    \n", "    all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "\n", "    # --- Calculate Metrics ---\n", "    # Test Metrics\n", "    nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "    brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "    ece = calculate_ece(all_test_preds, all_test_labels)\n", "    test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "    # OOD AUC\n", "    entropy_id = get_predictive_entropy(all_test_preds)\n", "    entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    \n", "    ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "    entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "    ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "    # --- Train Loss and Accuracy ---\n", "    correct = 0\n", "    loss = 0\n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "    loss /= len(train_loader)\n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "\n", "    # --- Temperatures ---\n", "    Tkin = 0\n", "    Tconf = 0\n", "    param_count = 0\n", "    with torch.no_grad():\n", "        for param in model.parameters():\n", "            if hasattr(param, 'buf'):\n", "                Tkin += (param.buf**2).sum().item()\n", "                Tconf += (param.data * param.grad.data).sum().item()\n", "                param_count += param.numel()\n", "    \n", "    if param_count > 0:\n", "        Tkin /= param_count\n", "        Tconf /= param_count\n", "    \n", "    return [loss, train_accu, test_accu, <PERSON><PERSON>, Tconf, nll, brier, ece, ood_auc]\n", "\n", "#%% Model \n", "\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "\n", "        self.fc_input_size = 128 * 3 * 3 # Based on MNIST image dimension after pooling\n", "\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = F.log_softmax(x, dim=1)\n", "        return x\n", "    \n", "#%% Parameters\n", "\n", "epochs = 25\n", "B_train = 10000   # batch size\n", "seed = 2\n", "meas_freq = 5     # evaluate network every meas_freq epochs\n", "\n", "gamma = 1\n", "temperature = 1\n", "T_schedule = None\n", "weight_decay = 1e-5\n", "\n", "alpha = 50\n", "alpha2 = 1/60000\n", "dtau = 4e-4\n", "m = 0.1\n", "M = 10\n", "\n", "cuda_idx = \"0\"\n", "dev_ids = [0]\n", "\n", "num_workers = 10\n", "criterion = nn.NLLLoss(reduction=\"mean\")\n", "\n", "torch.cuda.empty_cache()\n", "device = torch.device('cuda:'+str(cuda_idx) if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "\n", "# LOAD DATA\n", "transform = transforms.Compose([\n", "    transforms.To<PERSON><PERSON><PERSON>(), \n", "    transforms.Normalize((0.5,), (0.5,)) # MNIST normalization\n", "])\n", "\n", "train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "# OOD dataset\n", "ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "\n", "    \n", "#%% TRAIN MODEL - SINGLE RUN \n", "\n", "torch.manual_seed(seed)\n", "\n", "model = SimpleCNN()\n", "\n", "if device.type == \"cuda\" and torch.cuda.device_count() > 1:\n", "    print(f\"Using {torch.cuda.device_count()} GPUs!\")\n", "    model = nn.DataParallel(model, device_ids = dev_ids)   \n", "model.to(device)\n", "\n", "# Create data loaders\n", "train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=B_train, shuffle=True, num_workers=num_workers, pin_memory=True)\n", "test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "\n", "sampler = ZBAOABZ(model, train_loader, test_loader, ood_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq)\n", "results, header = sampler.train()\n", "       \n", "# Unpack results for plotting\n", "results_dict = {h: results[:, i] for i, h in enumerate(header)}\n", "\n", "#%% Plotting\n", "\n", "# Plot Loss and Accuracies\n", "fig, ax = plt.subplots(3, 1, figsize=(10, 12))\n", "ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Train Loss\"], marker='o')\n", "ax[0].set_title(\"Training Loss\")\n", "ax[0].set_ylabel(\"NLL Loss\")\n", "ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "ax[1].set_title(\"Train and Test Accuracy\")\n", "ax[1].set_ylabel(\"Accuracy (%)\")\n", "ax[1].legend()\n", "ax[2].plot(results_dict[\"Epoch\"], results_dict[\"dt\"], marker='o', color='purple')\n", "ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "ax[2].set_ylabel(\"Stepsize\")\n", "for a in ax:\n", "    a.set_xlabel(\"Epochs\")\n", "    a.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Plot new metrics\n", "fig, ax = plt.subplots(4, 1, figsize=(10, 16))\n", "ax[0].plot(results_dict[\"Epoch\"], results_dict[\"NLL\"], marker='o', label=\"NLL\")\n", "ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Brier\"], marker='o', label=\"Brier Score\")\n", "ax[0].set_title(\"NLL and Brier Score on Test Set\")\n", "ax[0].set_ylabel(\"Score\")\n", "ax[0].legend()\n", "\n", "ax[1].plot(results_dict[\"Epoch\"], results_dict[\"ECE\"], marker='o', color='green', label=\"ECE\")\n", "ax[1].set_title(\"Expected Calibration Error (ECE)\")\n", "ax[1].set_ylabel(\"ECE\")\n", "ax[1].legend()\n", "\n", "ax[2].plot(results_dict[\"Epoch\"], results_dict[\"OOD AUC\"], marker='o', color='red', label=\"OOD AUC (FashionMNIST)\")\n", "ax[2].set_title(\"Out-of-Distribution Detection AUC\")\n", "ax[2].set_ylabel(\"AUC\")\n", "ax[2].set_ylim(0.4, 1.05)\n", "ax[2].axhline(0.5, color='black', linestyle='--', label='Random Guess')\n", "ax[2].legend()\n", "\n", "ax[3].plot(results_dict[\"Epoch\"], results_dict[\"ESS\"], marker='o', color='orange', label=\"ESS of Loss\")\n", "ax[3].set_title(\"Effective Sample Size (ESS) of Training Loss\")\n", "ax[3].set_ylabel(\"ESS\")\n", "ax[3].set_yscale('log')\n", "ax[3].legend()\n", "\n", "\n", "for a in ax:\n", "    a.set_xlabel(\"Epochs\")\n", "    a.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Final ESS for training loss: {results_dict['ESS'][-1]:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# #%% Imports\n", "\n", "# import torch\n", "# import torch.nn as nn\n", "# import torch.nn.functional as F\n", "# import numpy as np\n", "# import time\n", "# import arviz as az\n", "\n", "\n", "# import torchvision\n", "# from torchvision import datasets, transforms\n", "# import matplotlib.pyplot as plt\n", "# from sklearn.metrics import roc_auc_score\n", "\n", "# #%% ZBAOABZ\n", "\n", "# class ZBAOABZ(nn.Module):\n", "\n", "#     def __init__(self, model, train_loader, test_loader, ood_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq, lr_schedule=None, T_schedule=None):\n", "#         super(ZBAOAB<PERSON>, self).__init__()\n", "#         self.model = model\n", "#         self.train_loader = train_loader\n", "#         self.test_loader = test_loader\n", "#         self.ood_loader = ood_loader\n", "#         self.criterion = criterion\n", "#         self.lr = None                      # stepsize dt\n", "#         self.weight_decay = weight_decay    # weight decay for L2 regularization\n", "#         self.gamma = gamma\n", "#         self.alpha = alpha\n", "#         self.alpha2 = alpha2                # 1/Omega\n", "#         self.T = temperature\n", "#         self.epochs = epochs\n", "#         self.device = device\n", "#         self.meas_freq = meas_freq\n", "#         self.running_loss = None\n", "        \n", "#         self.dtau = dtau\n", "#         self.zeta = None\n", "#         self.a_gamma = np.exp(-self.gamma)  # exp(-γ)\n", "#         self.a = None\n", "#         self.sqrt_aT = None\n", "#         self.gradnorm = None\n", "#         self.alpha_inv = 1/self.alpha\n", "#         self.exptau_half = np.exp(-0.5*self.alpha*self.dtau)\n", "#         self.r=0.25\n", "#         self.m=m\n", "#         self.M=M\n", "#         self.lr_schedule = lr_schedule\n", "#         self.T_schedule = T_schedule\n", "#         self.T_idx = 0\n", "        \n", "#         self.dt_raw = []\n", "#         self.zeta_raw = []\n", "#         self.gradnorms = []\n", "#         self.loss_history_for_ess = [] # For ESS calculation\n", "        \n", "#         self.results_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \n", "#                                \"Tkin\", \"Tconf\", \"NLL\", \"Brier\", \"ECE\", \n", "#                                \"OOD AUC\", \"ESS\", \"dt\", \"zeta\"]\n", "        \n", "#     def train(self):\n", "#         \"\"\"\n", "#         Main training function. Will return results array.\n", "#         \"\"\"\n", "                \n", "#         datasize = len(self.train_loader.dataset)\n", "        \n", "#         squeeze = True if type(self.criterion) == torch.nn.modules.loss.BCELoss else False\n", "\n", "#         if self.criterion.reduction != \"mean\":\n", "#             raise ValueError(\"Criterion reduction mode must be 'mean'.\\n\")\n", "        \n", "#         # create momentum buffers\n", "#         for p in self.model.parameters():              \n", "#             p.buf = torch.normal(torch.zeros(p.size()), np.sqrt(0.5*self.T)*torch.ones(p.size())).to(self.device, non_blocking=True) # N(0, √(0.5T))\n", "        \n", "#         # compute initial gradients\n", "#         (data, target) = next(iter(self.train_loader))   # get first batch of data\n", "#         data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True) # save data on GPU\n", "#         self.fill_gradients(data, target, squeeze, datasize)\n", "        \n", "#         results = [] # store results.\n", "#         initial_metrics = evaluate(self.model, self.train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "#         results.append(initial_metrics)\n", "        \n", "#         start_time = time.time()\n", "#         print(\"Starting ZBAOABZ sampling...\")     \n", "        \n", "#         # initial zeta and stepsize\n", "#         self.set_gradnorm()\n", "#         self.zeta = self.gradnorm\n", "#         self.Sundman_transform()   # intial dt = psi * dtau\n", "        \n", "#         self.dt_raw.append(self.lr)\n", "#         self.zeta_raw.append(self.zeta)\n", "\n", "#         for epoch in range(1, self.epochs+1):               # sampling loop\n", "#             self.model.train()\n", "            \n", "#             # adjust stepsize and temperature (optional)\n", "#             if self.lr_schedule is not None and epoch % self.lr_schedule[0]==0:\n", "#                 self.dtau *= self.lr_schedule[1]\n", "\n", "#             if self.T_schedule is not None and epoch % self.T_schedule[self.T_idx][0]==0:\n", "#                 new_T = self.T_schedule[self.T_idx][1]\n", "#                 print(\"Adjusting temperature to \", new_T)\n", "#                 self.change_temperature(new_T)\n", "#                 self.T_idx += 1\n", "#                 if self.T_idx == len(self.T_schedule):\n", "#                     self.T_schedule = None\n", "            \n", "#             for batch_idx, (data, target) in enumerate(self.train_loader):\n", "                \n", "#                 self.Z_step()\n", "#                 self.<PERSON><PERSON>_transform()   # dt\n", "#                 self.a = self.a_gamma**(self.lr)   # a = exp(-γ Δt)\n", "#                 self.sqrt_aT = torch.sqrt( (1 - self.a**2)*self.T )\n", "                \n", "#                 self.update_params_BAOA()     # BAOA-steps\n", "                \n", "#                 data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)   # compute new gradients\n", "#                 self.fill_gradients(data, target, squeeze, datasize)\n", "                \n", "#                 self.update_params_B()     # B-step\n", "                \n", "#                 self.set_gradnorm()\n", "#                 self.Z_step()       \n", "                \n", "#                 self.<PERSON><PERSON>_transform()\n", "\n", "#                 # Store loss for ESS calculation\n", "#                 self.loss_history_for_ess.append(self.running_loss.item() / datasize)\n", "\n", "#             if epoch % self.meas_freq == 0:\n", "#                 metrics = evaluate(self.model, self.train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "#                 results.append(metrics)\n", "#                 self.dt_raw.append(self.lr)\n", "#                 self.zeta_raw.append(self.zeta)\n", "\n", "#             print(f\"ZBAOABZ EPOCH {epoch} DONE! | Test Accu: {results[-1][2]:.2f}% | dt: {self.lr.item():.6f}\")              \n", "        \n", "#         end_time = time.time()\n", "#         print(\"Training took {} seconds, i.e {} minutes, with {} seconds per epoch!\"\n", "#               .format(end_time-start_time, (end_time-start_time)/60, (end_time-start_time)/self.epochs))\n", "        \n", "#         # Finalize results processing\n", "#         results = np.array(results)\n", "#         ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "#         ess_column = np.full((results.shape[0], 1), ess_value)\n", "\n", "#         # Ensure dt_raw and zeta_raw are numpy arrays\n", "#         dt_np = np.array([i.cpu().item() for i in self.dt_raw])\n", "#         zeta_np = np.array([i.cpu().item() for i in self.zeta_raw])\n", "\n", "#         epoch_axis = np.arange(0, len(results[:,0])) * self.meas_freq\n", "#         results = np.column_stack((epoch_axis, results, ess_column, dt_np, zeta_np))\n", "\n", "#         return results, self.results_header\n", "\n", "#     def update_params_BAOA(self):\n", "#         \"\"\"\n", "#         Performs B-, A-, O-, and A-step on model. Forces are assumed to be stored in parameter gradients.\n", "#         \"\"\"     \n", "#         for p in self.model.parameters():\n", "#             p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                  # B-step: p.buf = p.buf - 0.5*Δt*∇U(x)\n", "#             p.data.add_(p.buf, alpha=0.5*self.lr)                        # A-step: x = x + 0.5*Δt*p.buf\n", "            \n", "#             eps = torch.randn(p.size()).to(self.device, non_blocking=True)       # O-step, ε ~ N(0,I)\n", "#             p.buf.mul_(self.a)   \n", "#             p.buf.add_(eps, alpha=self.sqrt_aT)                          # p.buf = a * p.buf + √[(1-a²)T] * ε\n", "            \n", "#             p.data.add_(p.buf, alpha=0.5*self.lr)                        # A-step                                    \n", "\n", "#     def update_params_B(self):\n", "#         \"\"\"\n", "#         Performs B-step on model. Forces are assumed to be stored in parameter gradients.\n", "#         \"\"\"\n", "#         for p in self.model.parameters():\n", "#             p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                  # B-step                                 \n", "\n", "#     def fill_gradients(self, data, target, squeeze, datasize):\n", "#         \"\"\"\n", "#         Fills gradients of the model on batch (data, target).\n", "#         \"\"\"\n", "#         self.model.zero_grad()\n", "#         output = self.model(data)\n", "#         if squeeze: output=output.squeeze()\n", "#         self.running_loss = self.criterion(output, target)*datasize\n", "#         self.running_loss.backward()\n", "        \n", "#         for p in list(self.model.parameters()):\n", "#             p.grad.data.add_(p.data, alpha=self.weight_decay)   # ∇U = ∇U + weight_decay * x\n", "\n", "#     def set_gradnorm(self):\n", "#         self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters())\n", "#         self.gradnorm *= self.alpha2   # g(x,p) = Ω^{-1}||∇U(x)||^s (s=2)\n", "    \n", "#     def Z_step(self):  # ζ = exp(-0.5αΔτ)ζ + α^{-1}(1-exp(-0.5αΔτ))g(x,p)\n", "#         self.zeta = self.exptau_half * self.zeta + self.alpha_inv * (1-self.exptau_half) * self.gradnorm\n", "    \n", "#     def Sundman_transform(self):\n", "#         zeta_r = self.zeta**self.r\n", "#         self.lr = self.dtau * self.m * (zeta_r + self.M) / (zeta_r + self.m)   # Δt = Δτ * m * (ζ^r + M)/(ζ^r + m)\n", "        \n", "#     def change_temperature(self, T):\n", "#         \"\"\"\n", "#         Changes the temperature of the sampler.\n", "#         \"\"\"\n", "#         self.T = T\n", "\n", "# #%% Helper functions for metrics\n", "# # Expected Calibration Error (ECE): Measures how well-calibrated the model's confidence scores are.\n", "\n", "# # Negative Log-Likelihood (NLL) & Brier Score: Proper scoring rules that assess the quality of the predicted probability distributions.\n", "\n", "# # OOD AUC: Evaluates the model's ability to distinguish between in-distribution (MNIST) and out-of-distribution (Fashion-MNIST) data \n", "# # based on predictive uncertainty (entropy). An AUC of 1.0 would be perfect.\n", "\n", "# # Effective Sample Size (ESS): Calculated for the training loss to measure the sampling efficiency. \n", "# # A higher ESS indicates less correlation between samples and more efficient exploration.\n", "\n", "# def calculate_ess(chain):\n", "#     \"\"\"Calculate Effective Sample Size (ESS) for a chain.\"\"\"\n", "#     if len(chain) < 2:\n", "#         return np.nan\n", "#     return az.ess(chain).item()\n", "    \n", "# def calculate_ece(preds, labels, n_bins=15):\n", "#     \"\"\"Calculate Expected Calibration Error.\"\"\"\n", "#     bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "#     bin_lowers = bin_boundaries[:-1]\n", "#     bin_uppers = bin_boundaries[1:]\n", "\n", "#     confidences, predictions = torch.max(preds, 1)\n", "#     accuracies = predictions.eq(labels)\n", "\n", "#     ece = torch.zeros(1, device=preds.device)\n", "#     for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "#         in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "#         prop_in_bin = in_bin.float().mean()\n", "#         if prop_in_bin.item() > 0:\n", "#             accuracy_in_bin = accuracies[in_bin].float().mean()\n", "#             avg_confidence_in_bin = confidences[in_bin].mean()\n", "#             ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "#     return ece.item()\n", "\n", "# def calculate_brier_score(preds, labels):\n", "#     \"\"\"Calculate Brier Score.\"\"\"\n", "#     one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "#     return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "# def get_predictive_entropy(preds):\n", "#     \"\"\"Calculate predictive entropy for given predictions.\"\"\"\n", "#     return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "\n", "# #%% external evaluate function\n", "\n", "# def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "#     \"\"\"\n", "#     Returns a list containing:\n", "#     [train loss, train accuracy, test accuracy, Tkin, Tconf, NLL, Brier Score, ECE, OOD AUC]\n", "#     \"\"\"\n", "#     model.eval()\n", "    \n", "#     all_test_preds = []\n", "#     all_test_labels = []\n", "#     all_ood_preds = []\n", "\n", "#     # --- In-Distribution (Test Set) Evaluation ---\n", "#     with torch.no_grad():\n", "#         for data, target in test_loader:\n", "#             data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "#             output = model(data)\n", "#             probs = torch.exp(output) # Convert log-probabilities to probabilities\n", "#             all_test_preds.append(probs)\n", "#             all_test_labels.append(target)\n", "    \n", "#     all_test_preds = torch.cat(all_test_preds, dim=0)\n", "#     all_test_labels = torch.cat(all_test_labels, dim=0)\n", "\n", "#     # --- Out-of-Distribution (OOD Set) Evaluation ---\n", "#     with torch.no_grad():\n", "#         for data, _ in ood_loader:\n", "#             data = data.to(device, non_blocking=True)\n", "#             output = model(data)\n", "#             all_ood_preds.append(torch.exp(output))\n", "    \n", "#     all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "\n", "#     # --- Calculate Metrics ---\n", "#     # Test Metrics\n", "#     nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "#     brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "#     ece = calculate_ece(all_test_preds, all_test_labels)\n", "#     test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "#     # OOD AUC\n", "#     entropy_id = get_predictive_entropy(all_test_preds)\n", "#     entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    \n", "#     ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "#     entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "#     ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "#     # --- Train Loss and Accuracy ---\n", "#     correct = 0\n", "#     loss = 0\n", "#     with torch.no_grad():\n", "#         for data, target in train_loader:\n", "#             data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "#             output = model(data)\n", "#             loss += criterion(output, target).item()\n", "#             correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "#     loss /= len(train_loader)\n", "#     train_accu = 100. * correct / len(train_loader.dataset)\n", "\n", "#     # --- Temperatures ---\n", "#     Tkin = 0\n", "#     Tconf = 0\n", "#     param_count = 0\n", "#     with torch.no_grad():\n", "#         for param in model.parameters():\n", "#             if hasattr(param, 'buf'):\n", "#                 Tkin += (param.buf**2).sum().item()\n", "#                 Tconf += (param.data * param.grad.data).sum().item()\n", "#                 param_count += param.numel()\n", "    \n", "#     if param_count > 0:\n", "#         Tkin /= param_count\n", "#         Tconf /= param_count\n", "    \n", "#     return [loss, train_accu, test_accu, <PERSON><PERSON>, Tconf, nll, brier, ece, ood_auc]\n", "\n", "# #%% Model \n", "\n", "# class SimpleCNN(nn.Module):\n", "#     def __init__(self):\n", "#         super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "#         self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "#         self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "#         self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "#         self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "\n", "#         self.fc_input_size = 128 * 3 * 3 # Based on MNIST image dimension after pooling\n", "\n", "#         self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "#         self.fc2 = nn.Linear(512, 256)\n", "#         self.fc3 = nn.<PERSON><PERSON>(256, 10)\n", "\n", "#     def forward(self, x):\n", "#         x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "#         x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "#         x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "#         x = x.view(x.size(0), -1)\n", "#         x = <PERSON>.relu(self.fc1(x))\n", "#         x = <PERSON>.relu(self.fc2(x))\n", "#         x = self.fc3(x)\n", "#         x = F.log_softmax(x, dim=1)\n", "#         return x\n", "    \n", "# #%% Parameters\n", "\n", "# epochs = 25\n", "# B_train = 10000   # batch size\n", "# seed = 2\n", "# meas_freq = 5     # evaluate network every meas_freq epochs\n", "\n", "# gamma = 1\n", "# temperature = 1\n", "# T_schedule = None\n", "# weight_decay = 1e-5\n", "\n", "# alpha = 50\n", "# alpha2 = 1/60000\n", "# dtau = 4e-4\n", "# m = 0.1\n", "# M = 10\n", "\n", "# cuda_idx = \"0\"\n", "# dev_ids = [0]\n", "\n", "# num_workers = 10\n", "# criterion = nn.NLLLoss(reduction=\"mean\")\n", "\n", "# torch.cuda.empty_cache()\n", "# device = torch.device('cuda:'+str(cuda_idx) if torch.cuda.is_available() else 'cpu')\n", "# print(f\"Using device: {device}\")\n", "\n", "\n", "# # LOAD DATA\n", "# transform = transforms.Compose([\n", "#     transforms.To<PERSON><PERSON>or(), \n", "#     transforms.Normalize((0.5,), (0.5,)) # MNIST normalization\n", "# ])\n", "\n", "# train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "# test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "# # OOD dataset\n", "# ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "\n", "    \n", "# #%% TRAIN MODEL - SINGLE RUN \n", "\n", "# torch.manual_seed(seed)\n", "\n", "# model = SimpleCNN()\n", "\n", "# if device.type == \"cuda\" and torch.cuda.device_count() > 1:\n", "#     print(f\"Using {torch.cuda.device_count()} GPUs!\")\n", "#     model = nn.DataParallel(model, device_ids = dev_ids)   \n", "# model.to(device)\n", "\n", "# # Create data loaders\n", "# train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=B_train, shuffle=True, num_workers=num_workers, pin_memory=True)\n", "# test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "# ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "\n", "# sampler = ZBAOABZ(model, train_loader, test_loader, ood_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq)\n", "# results, header = sampler.train()\n", "       \n", "# # Unpack results for plotting\n", "# results_dict = {h: results[:, i] for i, h in enumerate(header)}\n", "\n", "# #%% Plotting\n", "\n", "# # Plot Loss and Accuracies\n", "# fig, ax = plt.subplots(3, 1, figsize=(10, 12))\n", "# ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Train Loss\"], marker='o')\n", "# ax[0].set_title(\"Training Loss\")\n", "# ax[0].set_ylabel(\"NLL Loss\")\n", "# ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "# ax[1].plot(results_dict[\"Epoch\"], results_dict[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "# ax[1].set_title(\"Train and Test Accuracy\")\n", "# ax[1].set_ylabel(\"Accuracy (%)\")\n", "# ax[1].legend()\n", "# ax[2].plot(results_dict[\"Epoch\"], results_dict[\"dt\"], marker='o', color='purple')\n", "# ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "# ax[2].set_ylabel(\"Stepsize\")\n", "# for a in ax:\n", "#     a.set_xlabel(\"Epochs\")\n", "#     a.grid(True)\n", "# plt.tight_layout()\n", "# plt.show()\n", "\n", "# # Plot new metrics\n", "# fig, ax = plt.subplots(3, 1, figsize=(10, 12))\n", "# ax[0].plot(results_dict[\"Epoch\"], results_dict[\"NLL\"], marker='o', label=\"NLL\")\n", "# ax[0].plot(results_dict[\"Epoch\"], results_dict[\"Brier\"], marker='o', label=\"Brier Score\")\n", "# ax[0].set_title(\"NLL and Brier Score on Test Set\")\n", "# ax[0].set_ylabel(\"Score\")\n", "# ax[0].legend()\n", "\n", "# ax[1].plot(results_dict[\"Epoch\"], results_dict[\"ECE\"], marker='o', color='green', label=\"ECE\")\n", "# ax[1].set_title(\"Expected Calibration Error (ECE)\")\n", "# ax[1].set_ylabel(\"ECE\")\n", "# ax[1].legend()\n", "\n", "# ax[2].plot(results_dict[\"Epoch\"], results_dict[\"OOD AUC\"], marker='o', color='red', label=\"OOD AUC (FashionMNIST)\")\n", "# ax[2].set_title(\"Out-of-Distribution Detection AUC\")\n", "# ax[2].set_ylabel(\"AUC\")\n", "# ax[2].set_ylim(0.4, 1.05)\n", "# ax[2].axhline(0.5, color='black', linestyle='--', label='Random Guess')\n", "# ax[2].legend()\n", "\n", "# for a in ax:\n", "#     a.set_xlabel(\"Epochs\")\n", "#     a.grid(True)\n", "# plt.tight_layout()\n", "# plt.show()\n", "\n", "# print(f\"Final ESS for training loss: {results_dict['ESS'][-1]:.2f}\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-06-28T12:43:28.732276Z", "iopub.status.busy": "2025-06-28T12:43:28.732063Z", "iopub.status.idle": "2025-06-28T12:47:23.883670Z", "shell.execute_reply": "2025-06-28T12:47:23.882788Z", "shell.execute_reply.started": "2025-06-28T12:43:28.732259Z"}, "trusted": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 9.91M/9.91M [00:00<00:00, 59.5MB/s]\n", "100%|██████████| 28.9k/28.9k [00:00<00:00, 1.68MB/s]\n", "100%|██████████| 1.65M/1.65M [00:00<00:00, 14.7MB/s]\n", "100%|██████████| 4.54k/4.54k [00:00<00:00, 6.94MB/s]\n", "/usr/local/lib/python3.11/dist-packages/torch/utils/data/dataloader.py:624: UserWarning: This DataLoader will create 10 worker processes in total. Our suggested max number of worker in current system is 4, which is smaller than what this DataLoader is going to create. Please be aware that excessive worker creation might get DataLoader running slow or even freeze, lower the worker number to avoid potential slowness/freeze if necessary.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting ZBAOABZ sampling...\n", "ZBAOABZ EPOCH 1 DONE!\n", "ZBAOABZ EPOCH 2 DONE!\n", "ZBAOABZ EPOCH 3 DONE!\n", "ZBAOABZ EPOCH 4 DONE!\n", "ZBAOABZ EPOCH 5 DONE!\n", "ZBAOABZ EPOCH 6 DONE!\n", "ZBAOABZ EPOCH 7 DONE!\n", "ZBAOABZ EPOCH 8 DONE!\n", "ZBAOABZ EPOCH 9 DONE!\n", "ZBAOABZ EPOCH 10 DONE!\n", "ZBAOABZ EPOCH 11 DONE!\n", "ZBAOABZ EPOCH 12 DONE!\n", "ZBAOABZ EPOCH 13 DONE!\n", "ZBAOABZ EPOCH 14 DONE!\n", "ZBAOABZ EPOCH 15 DONE!\n", "ZBAOABZ EPOCH 16 DONE!\n", "ZBAOABZ EPOCH 17 DONE!\n", "ZBAOABZ EPOCH 18 DONE!\n", "ZBAOABZ EPOCH 19 DONE!\n", "ZBAOABZ EPOCH 20 DONE!\n", "ZBAOABZ EPOCH 21 DONE!\n", "ZBAOABZ EPOCH 22 DONE!\n", "ZBAOABZ EPOCH 23 DONE!\n", "ZBAOABZ EPOCH 24 DONE!\n", "ZBAOABZ EPOCH 25 DONE!\n", "Training took 208.2896113395691 seconds, i.e 3.4714935223261514 minutes, with 8.331584453582764 seconds per epoch!\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#%% Imports\n", "\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import time\n", "\n", "import torchvision\n", "from torchvision import  transforms\n", "import matplotlib.pyplot as plt\n", "\n", "#%% ZBAOABZ\n", "\n", "class ZBAOABZ(nn.Module):\n", "    \n", "    def __init__(self, model, train_loader, test_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq, lr_schedule=None, T_schedule=None):\n", "        super(ZBAOABZ, self).__init__()\n", "        self.model = model\n", "        self.train_loader = train_loader\n", "        self.test_loader = test_loader\n", "        self.criterion = criterion\n", "        self.lr = None                      # stepsize dt\n", "        self.weight_decay = weight_decay    # weight decay for L2 regularization\n", "        self.gamma = gamma\n", "        self.alpha = alpha\n", "        self.alpha2 = alpha2                # 1/Omega\n", "        self.T = temperature\n", "        self.epochs = epochs\n", "        self.device = device\n", "        self.meas_freq = meas_freq\n", "        self.running_loss = None\n", "        \n", "        self.dtau = dtau\n", "        self.zeta = None\n", "        self.a_gamma = np.exp(-self.gamma)  # exp(-γ)\n", "        self.a = None\n", "        self.sqrt_aT = None\n", "        self.gradnorm = None\n", "        self.alpha_inv = 1/self.alpha\n", "        self.exptau_half = np.exp(-0.5*self.alpha*self.dtau)\n", "        self.r=0.25\n", "        self.m=m\n", "        self.M=M\n", "        self.lr_schedule = lr_schedule\n", "        self.T_schedule = T_schedule\n", "        self.T_idx = 0\n", "        \n", "        self.dt_raw = []\n", "        self.zeta_raw = []\n", "        self.gradnorms = []\n", "        \n", "        \n", "    def train(self):\n", "        \"\"\"\n", "        Main training function. Will return results array holding\n", "        \n", "        Epochs | Train loss | Train accu | Test accu | Tkin | Tconf | dt | zeta  \n", "        \n", "        in columns\n", "        \"\"\"\n", "                \n", "        datasize = len(self.train_loader.dataset)\n", "        \n", "        squeeze = True if type(self.criterion) == torch.nn.modules.loss.BCELoss else False   # squeeze network output\n", "                                                                                             # for BCELoss (not required\n", "                                                                                             # for NLLLoss)\n", "        \n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\\n\")\n", "        \n", "        # create momentum buffers\n", "        for p in self.model.parameters():                      \n", "            p.buf = torch.normal(torch.zeros(p.size()), np.sqrt(0.5*self.T)*torch.ones(p.size())).to(self.device, non_blocking=True) # N(0, √(0.5T))\n", "        \n", "        # compute initial gradients\n", "        (data, target) = next(iter(self.train_loader))          \n", "        data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)\n", "        self.fill_gradients(data, target, squeeze, datasize)\n", "        \n", "        results = [] # store results.\n", "        results += [evaluate(self.model, self.train_loader, self.test_loader, self.device, self.criterion)]\n", "        \n", "        start_time = time.time()\n", "        print(\"Starting ZBAOABZ sampling...\")     \n", "        \n", "        # initial zeta and stepsize\n", "        self.set_gradnorm()\n", "        self.zeta = self.gradnorm\n", "        self.Sundman_transform()   # intial dt = psi * dtau\n", "        \n", "        self.dt_raw += [self.lr]\n", "        self.zeta_raw += [self.zeta]\n", "    \n", "        \n", "        \n", "        for epoch in range(1, self.epochs+1):                   # sampling loop\n", "            self.model.train()\n", "            \n", "            # adjust stepsize and temperature (optional)\n", "            if self.lr_schedule != None and epoch % self.lr_schedule[0]==0:\n", "                self.dtau *= self.lr_schedule[1]\n", "\n", "            if self.T_schedule != None and epoch % self.T_schedule[self.T_idx][0]==0:\n", "                new_T = self.T_schedule[self.T_idx][1]\n", "                print(\"Adjusting temperature to \", new_T)\n", "                self.change_temperature(new_T)\n", "                self.T_idx += 1\n", "                if self.T_idx == len(self.T_schedule):\n", "                    self.T_schedule = None\n", "            \n", "            for batch_idx, (data, target) in enumerate(self.train_loader):\n", "                \n", "                self.Z_step()\n", "                self.Sundman_transform()   # dt\n", "                self.a = self.a_gamma**(self.lr)   # a = exp(-γ Δt)\n", "                self.sqrt_aT = torch.sqrt( (1 - self.a**2)*self.T )\n", "                \n", "                self.update_params_BAOA()    # BAOA-steps\n", "                \n", "                data, target = data.to(self.device, non_blocking=True), target.to(self.device, non_blocking=True)   # compute new gradients\n", "                self.fill_gradients(data, target, squeeze, datasize)\n", "                \n", "                self.update_params_B()     # B-step\n", "                \n", "                self.set_gradnorm()\n", "                self.Z_step()        \n", "                \n", "                self.Sundman_transform()\n", "           \n", "\n", "\n", "            if epoch % self.meas_freq == 0:\n", "                results += [ evaluate(self.model, self.train_loader, self.test_loader, self.device, self.criterion) ]\n", "                self.dt_raw += [self.lr]\n", "                self.zeta_raw += [self.zeta]\n", "\n", "\n", "            print(\"ZBAOABZ EPOCH {} DONE!\".format(epoch))                \n", "        \n", "        end_time = time.time()\n", "        print(\"Training took {} seconds, i.e {} minutes, with {} seconds per epoch!\"\n", "              .format(end_time-start_time, (end_time-start_time)/60, (end_time-start_time)/self.epochs))\n", "                      \n", "\n", "        results = np.array(results)\n", "        self.dt_raw = np.array([i.cpu() for i in self.dt_raw])\n", "        self.zeta_raw = np.array([i.cpu() for i in self.zeta_raw])\n", "        \n", "        epoch_axis = np.arange(0,len(results[:,0]))*self.meas_freq\n", "        results = np.column_stack((epoch_axis, results, self.dt_raw, self.zeta_raw))\n", "\n", "        return results   # [epoch, loss, train_acc, test_acc, Tkin, Tconf, dt, zeta]\n", "\n", "    \n", "\n", "    \n", "    \n", "    def update_params_BAOA(self):\n", "        \"\"\"\n", "        Performs B-, A-, O-, and A-step on model. Forces are assumed to be stored in parameter gradients.\n", "        \"\"\"      \n", "       \n", "        for p in self.model.parameters():\n", "\n", "            p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                             # B-step: p.buf = p.buf - 0.5*Δt*∇U(x)\n", "\n", "            p.data.add_(p.buf, alpha=0.5*self.lr)                                   # A-step: x = x + 0.5*Δt*p.buf\n", "            \n", "            eps = torch.randn(p.size()).to(self.device, non_blocking=True)          # O-step, ε ~ N(0,I)\n", "            p.buf.mul_(self.a)               \n", "            p.buf.add_(eps, alpha=self.sqrt_aT)                                     # p.buf = a * p.buf + √[(1-a²)T] * ε\n", "            \n", "            p.data.add_(p.buf, alpha=0.5*self.lr)                                   # A-step                               \n", "\n", "\n", "\n", "\n", "    def update_params_B(self):\n", "        \"\"\"\n", "        Performs B-step on model. Forces are assumed to be stored in parameter gradients.\n", "        \"\"\"\n", "\n", "        for p in self.model.parameters():\n", "            \n", "            p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                             # B-step                                \n", "  \n", "\n", "\n", "    def fill_gradients(self, data, target, squeeze, datasize):\n", "        \"\"\"\n", "        Fills gradients of the model on batch (data, target).\n", "        \"\"\"\n", "        \n", "        self.model.zero_grad()\n", "        output = self.model(data)  # forward\n", "        if squeeze: output=output.squeeze()  # squeeze network output for BCELoss\n", "        self.running_loss = self.criterion(output, target)*datasize  # calculate loss (used mean)\n", "        self.running_loss.backward()  # backward for gradient calculation\n", "        \n", "        for p in list(self.model.parameters()):                 # weight decay / prior component\n", "            p.grad.data.add_(p.data, alpha=self.weight_decay)   # ∇U = ∇U + weight_decay * x\n", "\n", "\n", "\n", "    def set_gradnorm(self):\n", "        \n", "        self.gradnorm = 0\n", "        for p in list(self.model.parameters()):\n", "            self.gradnorm += torch.sum(p.grad.data**2)\n", "        \n", "        self.gradnorm *= self.alpha2   # g(x,p) = Ω^{-1}||∇U(x)||^s (s=2)\n", "    \n", "    def Z_step(self):  # ζ = exp(-0.5αΔτ)ζ + α^{-1}(1-exp(-0.5αΔτ))g(x,p)\n", "        self.zeta = self.exptau_half * self.zeta + self.alpha_inv * (1-self.exptau_half) * self.gradnorm\n", "\n", "\n", "    \n", "    def Sundman_transform(self):\n", "        zeta_r  =self.zeta**self.r\n", "        self.lr = self.dtau * self.m * (zeta_r + self.M) / (zeta_r + self.m)  # Δt = Δτ * m * (ζ^r + M)/(ζ^r + m)\n", "\n", "        \n", "    def change_temperature(self, T):\n", "        \"\"\"\n", "        Changes the temperature of the sampler.\n", "        \"\"\"\n", "        self.T = T\n", "        \n", "        \n", "#%% external evaluate function\n", "\n", "def evaluate(model, train_loader, test_loader, device, criterion):\n", "    \"\"\"\n", "    Returns train loss, train accuracy, test accuracy, Tkin and Tconf\n", "\n", "    TODO: ECE, NLL, Brier Score, ESS, OOD AUC\n", "    \"\"\"\n", "\n", "    model.eval()\n", "\n", "    # Train loss and accu\n", "    \n", "    correct = 0\n", "    loss = 0\n", "    \n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()  # sum up batch loss\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()  # sum of correct predictions\n", "\n", "    # Average loss per data point.\n", "    loss /= len(train_loader)\n", "    \n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "\n", "    # Test accuracy\n", "    correct = 0\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()  # sum of correct predictions\n", "\n", "    test_accu = 100. * correct / len(test_loader.dataset)\n", "\n", "    # Temperatures\n", "    Tkin = 0\n", "    Tconf = 0\n", "    param_count = 0\n", "    with torch.no_grad():\n", "        for param in model.parameters():\n", "            Tkin += (param.buf**2).sum().item()   # Σ p_i^2\n", "            Tconf += (param*param.grad).sum().item()  # Σ x_i·∇U_i\n", "            param_count += param.numel()   # N_dof\n", "        \n", "        Tkin /=param_count\n", "        Tconf /= param_count\n", "    \n", "    return [loss, train_accu, test_accu, <PERSON><PERSON>, Tconf]\n", "\n", "\n", "#%% Model \n", "\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "\n", "        self.fc_input_size = 128 * 3 * 3  # Adjusted for MNIST images\n", "\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(nn.ReLU()(self.conv1(x)))\n", "        x = self.pool(nn.ReLU()(self.conv2(x)))\n", "        x = self.pool(nn.ReLU()(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = nn.ReLU()(self.fc1(x))\n", "        x = nn.ReLU()(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = torch.log_softmax(x, dim=1)\n", "        return x\n", "    \n", "\n", "#%% Parameters\n", "\n", "epochs = 25\n", "B_train = 10000  # batch size\n", "seed = 2\n", "meas_freq = 5    # evaluate network every meas_freq epochs\n", "\n", "gamma = 1\n", "temperature = 1\n", "T_schedule = None\n", "weight_decay = 1e-5\n", "\n", "alpha = 50\n", "alpha2 = 1/60000\n", "dtau = 4e-4\n", "m = 0.1\n", "M = 10\n", "\n", "cuda_idx = \"0\"\n", "dev_ids = [0]\n", "\n", "num_workers = 10\n", "criterion = nn.NLLLoss(reduction=\"mean\")\n", "\n", "torch.cuda.empty_cache()\n", "device = torch.device('cuda:'+str(cuda_idx) if torch.cuda.is_available() else 'cpu')\n", "\n", "\n", "# LOAD DATA\n", "\n", "transform = transforms.Compose([\n", "    transforms.To<PERSON><PERSON><PERSON>(), \n", "    transforms.Normalize((0.5,), (0.5,))  # MNIST normalization\n", "])\n", "\n", "train_dataset = torchvision.datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "test_dataset = torchvision.datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "\n", "    \n", "#%% TRAIN MODEL - SINGLE RUN \n", "\n", "torch.manual_seed(seed)\n", "\n", "model = SimpleCNN()\n", "\n", "if device.type == \"cuda\":\n", "    model = nn.DataParallel(model, device_ids = dev_ids)  \n", "    model.to(device)\n", "\n", "# Create data loaders\n", "train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=B_train, shuffle=True, num_workers=num_workers, pin_memory=True)\n", "test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "\n", "sampler = ZBAOABZ(model, train_loader, test_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq)\n", "results = sampler.train()\n", "\n", "        \n", "epochs = results[:,0]\n", "loss = results[:,1]\n", "train_accu = results[:,2]\n", "test_accu = results[:,3]\n", "Tkin = results[:,4]\n", "Tconf = results[:,5]\n", "dt = results[:,6]\n", "zeta = results[:,7]\n", "\n", "\n", "\n", "#%% plot loss accuracies\n", "\n", "fig, ax = plt.subplots(3, 1)\n", "\n", "ax[0].plot(epochs, loss)\n", "ax[1].plot(epochs, train_accu)\n", "ax[2].plot(epochs, test_accu)\n", "\n", "ax[0].set_title(\"Loss\")\n", "ax[1].set_title(\"Train Accuracy\")\n", "ax[2].set_title(\"Test Accuracy\")\n", "\n", "for a in ax:\n", "    a.set_xlabel(\"Epochs\")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import DataLoader\n", "\n", "class SMS_ZBAOABZ(ZBAOABZ):\n", "    def __init__(self, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "        # number of mini-batches per SMS-epoch\n", "        self.no_batches = len(self.train_loader)\n", "        # we’ll need an un-shuffled loader to index into:\n", "        self.base_loader = DataLoader(\n", "            self.train_loader.dataset,\n", "            batch_size=self.train_loader.batch_size,\n", "            shuffle=False,\n", "            num_workers=self.train_loader.num_workers,\n", "            pin_memory=self.train_loader.pin_memory,\n", "        )\n", "\n", "    def train(self):\n", "        datasize = len(self.train_loader.dataset)\n", "        squeeze = isinstance(self.criterion, torch.nn.BCELoss)\n", "        assert self.criterion.reduction==\"mean\"\n", "\n", "        # — initialize momentum buffers & first gradient exactly as before —\n", "        for p in self.model.parameters():              \n", "            p.buf = torch.normal(0, np.sqrt(0.5*self.T), size=p.size(), device=self.device)\n", "        data0, target0 = next(iter(self.train_loader))\n", "        data0, target0 = data0.to(self.device), target0.to(self.device)\n", "        self.fill_gradients(data0, target0, squeeze, datasize)\n", "\n", "        # initial zeta & dt\n", "        self.set_gradnorm()\n", "        self.zeta = self.gradnorm.clone()\n", "        self.Sundman_transform()\n", "        self.dt_raw.append(self.lr.item())\n", "        self.zeta_raw.append(self.zeta.item())\n", "\n", "        results = []\n", "        results.append(evaluate(...))      # initial metrics\n", "\n", "        for epoch in range(1, self.epochs+1):\n", "            # optional lr / T schedules at epoch‐start\n", "            if self.lr_schedule and epoch % self.lr_schedule[0]==0:\n", "                self.dtau *= self.lr_schedule[1]\n", "            if self.T_schedule and epoch % self.T_schedule[self.T_idx][0]==0:\n", "                self.change_temperature(self.T_schedule[self.T_idx][1])\n", "                self.T_idx += 1\n", "\n", "            # 1) build a single random permutation of batch‐indices\n", "            perm = np.random.default_rng().permutation(self.no_batches)\n", "\n", "            # 2) forward sweep\n", "            for j in perm:\n", "                data, target = next(\n", "                    iter(DataLoader(self.base_loader.dataset,\n", "                                    batch_size=self.base_loader.batch_size,\n", "                                    sampler=torch.utils.data.SubsetRandomSampler([j])))\n", "                )\n", "                data, target = data.to(self.device), target.to(self.device)\n", "\n", "                self.Z_step()\n", "                self.Sundman_transform()\n", "                self.a = self.a_gamma**(self.lr)\n", "                self.sqrt_aT = torch.sqrt((1-self.a**2)*self.T)\n", "                self.update_params_BAOA()\n", "                self.fill_gradients(data, target, squeeze, datasize)\n", "                self.update_params_B()\n", "                self.set_gradnorm()\n", "                self.Z_step()\n", "                self.Sundman_transform()\n", "                self.loss_history_for_ess.append(self.running_loss.item()/datasize)\n", "\n", "            # 3) backward sweep (exact same but reversed perm)\n", "            for j in perm[::-1]:\n", "                data, target = next(\n", "                    iter(DataLoader(self.base_loader.dataset,\n", "                                    batch_size=self.base_loader.batch_size,\n", "                                    sampler=torch.utils.data.SubsetRandomSampler([j])))\n", "                )\n", "                data, target = data.to(self.device), target.to(self.device)\n", "\n", "                self.Z_step()\n", "                self.Sundman_transform()\n", "                self.a = self.a_gamma**(self.lr)\n", "                self.sqrt_aT = torch.sqrt((1-self.a**2)*self.T)\n", "                self.update_params_BAOA()\n", "                self.fill_gradients(data, target, squeeze, datasize)\n", "                self.update_params_B()\n", "                self.set_gradnorm()\n", "                self.Z_step()\n", "                self.Sundman_transform()\n", "                self.loss_history_for_ess.append(self.running_loss.item()/datasize)\n", "\n", "            # measure\n", "            if epoch % self.meas_freq == 0:\n", "                results.append(evaluate(...))\n", "                self.dt_raw.append(self.lr.item())\n", "                self.zeta_raw.append(self.zeta.item())\n", "                print(f\"SMS–ZBAOABZ E{epoch:2d} | dt={self.lr:.4e}\")\n", "\n", "        # pack up results exactly as in base class…\n", "        # (compute ESS, build numpy arrays, return results, header)\n", "        return super().finalize_results(results, self.dt_raw, self.zeta_raw)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%% Imports\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import time\n", "import arviz as az\n", "\n", "import torchvision\n", "from torchvision import datasets, transforms\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_auc_score\n", "\n", "#%% ZBAOABZ with SMS\n", "class SMS_ZBAOABZ(nn.Module):\n", "    def __init__(self, model, train_loader, test_loader, ood_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq, lr_schedule=None, T_schedule=None):\n", "        super(SMS_ZBAOABZ, self).__init__()\n", "        self.model        = model\n", "        self.train_loader = train_loader\n", "        self.test_loader  = test_loader\n", "        self.ood_loader   = ood_loader\n", "        self.criterion    = criterion\n", "\n", "        self.dtau         = dtau\n", "        self.weight_decay = weight_decay   # weight decay for L2 regularization\n", "        self.gamma        = gamma\n", "        self.alpha        = alpha\n", "        self.alpha2       = alpha2         # 1/Omega\n", "        self.T            = temperature\n", "        self.epochs       = epochs\n", "        self.device       = device\n", "        self.meas_freq    = meas_freq\n", "\n", "        self.lr_schedule  = lr_schedule\n", "        self.T_schedule   = T_schedule\n", "        self.T_idx        = 0\n", "\n", "        # sundman parameters\n", "        self.r = 0.25\n", "        self.m = m\n", "        self.M = M\n", "\n", "        # state vars\n", "        self.lr       = None      # stepsize Δt\n", "        self.zeta     = None\n", "        self.gradnorm = None\n", "\n", "        # precompute\n", "        self.a_gamma      = np.exp(-self.gamma)   # exp(-γ)\n", "        self.alpha_inv    = 1.0/self.alpha\n", "        self.exptau_half  = np.exp(-0.5*self.alpha*self.dtau)\n", "\n", "        # logs\n", "        self.dt_raw     = []\n", "        self.zeta_raw   = []\n", "        self.ess_raw    = []\n", "        self.loss_history_for_ess = []\n", "\n", "        # header\n", "        self.results_header = [\n", "            \"Epoch\",\"Train Loss\",\"Train Accu\",\"Test Accu\",\n", "            \"Tkin\",\"Tconf\",\"NLL\",\"Brier\",\"ECE\",\"OOD AUC\",\n", "            \"ESS\",\"dt\",\"zeta\"\n", "        ]\n", "\n", "    def train(self):\n", "        \"\"\"\n", "        Main training function. Will return results array.\n", "        \"\"\"\n", "        # disable <PERSON><PERSON><PERSON><PERSON> shuffling\n", "        self.train_loader = torch.utils.data.DataLoader(\n", "            self.train_loader.dataset,\n", "            batch_size=self.train_loader.batch_size,\n", "            shuffle=False,\n", "            num_workers=self.train_loader.num_workers,\n", "            pin_memory=self.train_loader.pin_memory\n", "        )\n", "\n", "        # cache all mini-batches\n", "        datasize    = len(self.train_loader.dataset)  # 60000\n", "\n", "        all_batches = list(self.train_loader)\n", "        N_batches   = len(all_batches)     # Nm\n", "\n", "        squeeze = True if type(self.criterion) == torch.nn.modules.loss.BCELoss else False\n", "\n", "        if self.criterion.reduction != \"mean\":\n", "            raise ValueError(\"Criterion reduction mode must be 'mean'.\\n\")\n", "        \n", "        # create momentum buffers\n", "        for p in self.model.parameters():              \n", "            # p.buf = torch.randn_like(p.data) * np.sqrt(0.5 * self.T)\n", "            p.buf = torch.normal(torch.zeros(p.size()), np.sqrt(0.5*self.T)*torch.ones(p.size())).to(self.device, non_blocking=True) # N(0, √(0.5T))\n", "\n", "        # compute initial gradient (pick batch 0)\n", "        data, target = all_batches[0]\n", "        data, target = data.to(self.device), target.to(self.device)  # save in GPU\n", "        self.fill_gradients(data, target, squeeze, datasize)\n", "\n", "        # Store initial metrics\n", "        results = []\n", "        initial_metrics = evaluate(self.model, self.train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "        results.append(initial_metrics)\n", "\n", "        start_time = time.time()\n", "        print(\"Starting SMS-ZBAOABZ sampling...\")\n", "\n", "        # init zeta & dt\n", "        self.set_gradnorm()\n", "        self.zeta = self.gradnorm\n", "        self.Sundman_transform()   # intial dt = psi * dtau\n", "\n", "        self.dt_raw.append(self.lr)\n", "        self.zeta_raw.append(self.zeta)\n", "        self.ess_raw.append(np.nan)   # ESS is not defined for a single point\n", "\n", "        # for sms_iter in range(1, self.epochs+1):\n", "        for epoch in range(1, self.epochs+1):\n", "            self.model.train()\n", "\n", "            # optionally adjust dtau / T schedules\n", "            if self.lr_schedule is not None and epoch % self.lr_schedule[0] == 0:\n", "                self.dtau *= self.lr_schedule[1]\n", "\n", "            if self.T_schedule is not None and epoch % self.T_schedule[self.T_idx][0] == 0:\n", "                new_T = self.T_schedule[self.T_idx][1]\n", "                print(\"Adjusting temperature to\", new_T)\n", "                self.change_temperature(new_T)\n", "                self.T_idx += 1\n", "                if self.T_idx == len(self.T_schedule):\n", "                    self.T_schedule = None\n", "\n", "            if epoch % 2 == 1:\n", "                # 1) draw random perm\n", "                perm = np.random.permutation(N_batches)\n", "                \n", "                # ---- forward sweep ----\n", "                for batch_idx in perm:\n", "                    data, target = all_batches[batch_idx]\n", "                    self.Z_step()\n", "                    self.Sundman_transform()  # dt\n", "                    self.a = self.a_gamma**(self.lr)   # a = exp(-γ Δt)\n", "                    self.sqrt_aT = torch.sqrt( (1 - self.a**2)*self.T )\n", "\n", "                    self.update_params_BAOA()   # BAOA-steps\n", "\n", "                    data, target = data.to(self.device), target.to(self.device)  # compute new gradients\n", "                    self.fill_gradients(data, target, squeeze, datasize)\n", "\n", "                    self.update_params_B()   # B-step\n", "\n", "                    self.set_gradnorm()\n", "                    self.Z_step()\n", "\n", "                    self.Sundman_transform()\n", "\n", "                    # Store loss for ESS calculation (at every step for higher resolution)\n", "                    self.loss_history_for_ess.append(self.running_loss.item() / datasize)\n", "            \n", "            else:\n", "                # ---- backward sweep ----\n", "                for batch_idx in perm[::-1]:\n", "                    data, target = all_batches[batch_idx]\n", "                    self.Z_step()\n", "                    self.Sundman_transform()   # dt\n", "                    self.a = self.a_gamma**(self.lr)   # a = exp(-γ Δt)\n", "                    self.sqrt_aT = torch.sqrt( (1 - self.a**2)*self.T )\n", "\n", "                    self.update_params_BAOA()   # BAOA-steps\n", "\n", "                    data, target = data.to(self.device), target.to(self.device)\n", "                    self.fill_gradients(data, target, squeeze, datasize)\n", "                    \n", "                    self.update_params_B()  # B-step\n", "\n", "                    self.set_gradnorm()\n", "                    self.Z_step()\n", "\n", "                    self.Sundman_transform()\n", "\n", "                    # Store loss for ESS calculation (at every step for higher resolution)\n", "                    self.loss_history_for_ess.append(self.running_loss.item() / datasize)\n", "\n", "            # end of one SMS “epoch”: record metrics\n", "            if epoch % self.meas_freq == 0:\n", "                metrics = evaluate(self.model, self.train_loader, self.test_loader, self.ood_loader, self.device, self.criterion)\n", "                results.append(metrics)\n", "\n", "                # Calculate and store metrics for this epoch\n", "                self.dt_raw.append(self.lr)\n", "                self.zeta_raw.append(self.zeta)\n", "                ess_value = calculate_ess(np.array(self.loss_history_for_ess))\n", "                self.ess_raw.append(ess_value)\n", "\n", "            print(f\"SMS iter {epoch} done | Test Accu: {results[-1][2]:.2f}% | dt: {self.lr:.6f}\")\n", "\n", "        end_time = time.time()\n", "        print(\"Training took {} seconds, i.e {} minutes, with {} seconds per epoch!\"\n", "              .format(end_time-start_time, (end_time-start_time)/60, (end_time-start_time)/self.epochs))\n", "\n", "        # stack results\n", "        results = np.array(results)\n", "        dt_np   = np.array([x.cpu().item() for x in self.dt_raw])\n", "        zeta_np = np.array([x.cpu().item() for x in self.zeta_raw])\n", "        ess_np  = np.array(self.ess_raw)\n", "\n", "        # epoch_axis = np.arange(len(results))*self.meas_freq\n", "        epoch_axis = np.arange(0, len(results[:,0])) * self.meas_freq\n", "        results = np.column_stack((epoch_axis, results, ess_np, dt_np, zeta_np))\n", "        \n", "        # Update header to match the order in column_stack\n", "        final_header = [\"Epoch\", \"Train Loss\", \"Train Accu\", \"Test Accu\", \"Tkin\", \"Tconf\", \n", "                        \"NLL\", \"Brier\", \"ECE\", \"OOD AUC\", \"ESS\", \"dt\", \"zeta\"]\n", "\n", "        return results, final_header\n", "\n", "    def update_params_BAOA(self):\n", "        \"\"\"\n", "        Performs B-, A-, O-, and A-step on model. Forces are assumed to be stored in parameter gradients.\n", "        \"\"\"     \n", "        for p in self.model.parameters():\n", "            p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                  # B-step: p.buf = p.buf - 0.5*Δt*∇U(x)\n", "            p.data.add_(p.buf, alpha=0.5*self.lr)                        # A-step: x = x + 0.5*Δt*p.buf\n", "            \n", "            eps = torch.randn(p.size()).to(self.device, non_blocking=True)       # O-step, ε ~ N(0,I)\n", "            p.buf.mul_(self.a)   \n", "            p.buf.add_(eps, alpha=self.sqrt_aT)                          # p.buf = a * p.buf + √[(1-a²)T] * ε\n", "            \n", "            p.data.add_(p.buf, alpha=0.5*self.lr) \n", "\n", "    def update_params_B(self):\n", "        \"\"\"\n", "        Performs B-step on model. Forces are assumed to be stored in parameter gradients.\n", "        \"\"\"\n", "        for p in self.model.parameters():\n", "            p.buf.add_(p.grad.data, alpha=-0.5*self.lr)                  # B-step                                 \n", "\n", "    def fill_gradients(self, data, target, squeeze, datasize):\n", "        \"\"\"\n", "        Fills gradients of the model on batch (data, target).\n", "        \"\"\"\n", "        self.model.zero_grad()\n", "        output = self.model(data)\n", "        if squeeze: output=output.squeeze()\n", "        self.running_loss = self.criterion(output, target)*datasize\n", "        self.running_loss.backward()\n", "        \n", "        for p in list(self.model.parameters()):\n", "            p.grad.data.add_(p.data, alpha=self.weight_decay)   # ∇U = ∇U + weight_decay * x\n", "\n", "    def set_gradnorm(self):\n", "        self.gradnorm = sum(torch.sum(p.grad.data**2) for p in self.model.parameters())\n", "        self.gradnorm *= self.alpha2   # g(x,p) = Ω^{-1}||∇U(x)||^s (s=2)\n", "    \n", "    def Z_step(self):  # ζ = exp(-0.5αΔτ)ζ + α^{-1}(1-exp(-0.5αΔτ))g(x,p)\n", "        self.zeta = self.exptau_half * self.zeta + self.alpha_inv * (1-self.exptau_half) * self.gradnorm\n", "    \n", "    def Sundman_transform(self):\n", "        zeta_r = self.zeta**self.r\n", "        self.lr = self.dtau * self.m * (zeta_r + self.M) / (zeta_r + self.m)   # Δt = Δτ * m * (ζ^r + M)/(ζ^r + m)\n", "        \n", "    def change_temperature(self, T):\n", "        \"\"\"\n", "        Changes the temperature of the sampler.\n", "        \"\"\"\n", "        self.T = T\n", "\n", "#%% Helper functions for metrics\n", "# Expected Calibration Error (ECE): Measures how well-calibrated the model's confidence scores are.\n", "\n", "# Negative Log-Likelihood (NLL) & Brier Score: Proper scoring rules that assess the quality of the predicted probability distributions.\n", "\n", "# OOD AUC: Evaluates the model's ability to distinguish between in-distribution (MNIST) and out-of-distribution (Fashion-MNIST) data \n", "# based on predictive uncertainty (entropy). An AUC of 1.0 would be perfect.\n", "\n", "# Effective Sample Size (ESS): Calculated for the training loss to measure the sampling efficiency. \n", "# A higher ESS indicates less correlation between samples and more efficient exploration.\n", "\n", "def calculate_ess(chain):\n", "    \"\"\"Calculate Effective Sample Size (ESS) for a chain.\"\"\"\n", "    if len(chain) < 10: # ESS is unreliable for very short chains\n", "        return np.nan\n", "    return az.ess(chain).item()\n", "    \n", "def calculate_ece(preds, labels, n_bins=15):\n", "    \"\"\"\n", "    Calculate Expected Calibration Error.\n", "    ECE=\\\\sum_{b=1}^B\\\\frac{n_b}{N}|acc(b)-conf(b)|\n", "    b: b-th bin; n_b: number of samples in b-th bin; N: total number of samples\n", "    acc(b): accuracy of samples in b-th bin; conf(b): confidence of samples in b-th bin\n", "    \"\"\"\n", "    bin_boundaries = torch.linspace(0, 1, n_bins + 1)\n", "    bin_lowers = bin_boundaries[:-1]\n", "    bin_uppers = bin_boundaries[1:]\n", "\n", "    confidences, predictions = torch.max(preds, 1)\n", "    accuracies = predictions.eq(labels)\n", "\n", "    ece = torch.zeros(1, device=preds.device)\n", "    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):\n", "        in_bin = confidences.gt(bin_lower.item()) * confidences.le(bin_upper.item())\n", "        prop_in_bin = in_bin.float().mean()\n", "        if prop_in_bin.item() > 0:\n", "            accuracy_in_bin = accuracies[in_bin].float().mean()\n", "            avg_confidence_in_bin = confidences[in_bin].mean()\n", "            ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin\n", "    return ece.item()\n", "\n", "def calculate_brier_score(preds, labels):\n", "    \"\"\"\n", "    Calculate Brier Score.\n", "    BS=\\\\frac{1}{N}\\\\sum_{t=1}^N\\\\sum_{i=1}^R\\\\left(f_{ti}-o_{ti}\\\\right)^2\n", "    N: number of samples; R: number of classes\n", "    f_{ti}: predicted probability of t-th sample being in i-th class\n", "    o_{ti}: one-hot label of t-th sample being in i-th class (predicted label)\n", "    \"\"\"\n", "    one_hot_labels = F.one_hot(labels, num_classes=preds.shape[1])\n", "    return torch.mean((preds - one_hot_labels)**2).item()\n", "\n", "def get_predictive_entropy(preds):\n", "    \"\"\"\n", "    Calculate predictive entropy for given predictions.\n", "    H(p)=-\\\\sum_{i=1}^R p_i * log(p_i)\n", "    \"\"\"\n", "    return -torch.sum(preds * torch.log(preds + 1e-9), dim=1)\n", "\n", "\n", "#%% external evaluate function\n", "\n", "def evaluate(model, train_loader, test_loader, ood_loader, device, criterion):\n", "    \"\"\"\n", "    Returns a list containing:\n", "    [train loss, train accuracy, test accuracy, Tkin, Tconf, NLL, Brier Score, ECE, OOD AUC]\n", "    \"\"\"\n", "    model.eval()\n", "    \n", "    all_test_preds = []\n", "    all_test_labels = []\n", "    all_ood_preds = []\n", "\n", "    # --- In-Distribution (Test Set) Evaluation ---\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            probs = torch.exp(output) # Convert log-probabilities to probabilities\n", "            all_test_preds.append(probs)\n", "            all_test_labels.append(target)\n", "    \n", "    all_test_preds = torch.cat(all_test_preds, dim=0)\n", "    all_test_labels = torch.cat(all_test_labels, dim=0)\n", "\n", "    # --- Out-of-Distribution (OOD Set) Evaluation ---\n", "    with torch.no_grad():\n", "        for data, _ in ood_loader:\n", "            data = data.to(device, non_blocking=True)\n", "            output = model(data)\n", "            all_ood_preds.append(torch.exp(output))\n", "    \n", "    all_ood_preds = torch.cat(all_ood_preds, dim=0)\n", "\n", "    # --- Calculate Metrics ---\n", "    # Test Metrics\n", "    nll = F.nll_loss(torch.log(all_test_preds + 1e-9), all_test_labels, reduction='mean').item()\n", "    brier = calculate_brier_score(all_test_preds, all_test_labels)\n", "    ece = calculate_ece(all_test_preds, all_test_labels)\n", "    test_accu = 100. * (all_test_preds.argmax(1) == all_test_labels).sum().item() / len(all_test_labels)\n", "\n", "    # OOD AUC\n", "    entropy_id = get_predictive_entropy(all_test_preds)\n", "    entropy_ood = get_predictive_entropy(all_ood_preds)\n", "    \n", "    ood_labels = torch.cat([torch.zeros(len(entropy_id)), torch.ones(len(entropy_ood))]).numpy()\n", "    entropies = torch.cat([entropy_id, entropy_ood]).cpu().numpy()\n", "    ood_auc = roc_auc_score(ood_labels, entropies)\n", "\n", "    # --- Train Loss and Accuracy ---\n", "    correct = 0\n", "    loss = 0\n", "    with torch.no_grad():\n", "        for data, target in train_loader:\n", "            data, target = data.to(device, non_blocking=True), target.to(device, non_blocking=True)\n", "            output = model(data)\n", "            loss += criterion(output, target).item()\n", "            correct += (target == torch.argmax(output, dim=1)).sum().item()\n", "    loss /= len(train_loader)\n", "    train_accu = 100. * correct / len(train_loader.dataset)\n", "\n", "    # --- Temperatures ---\n", "    Tkin = 0\n", "    Tconf = 0\n", "    param_count = 0\n", "    with torch.no_grad():\n", "        for param in model.parameters():\n", "            if hasattr(param, 'buf'):\n", "                Tkin += (param.buf**2).sum().item()\n", "                Tconf += (param.data * param.grad.data).sum().item()\n", "                param_count += param.numel()\n", "    \n", "    if param_count > 0:\n", "        Tkin /= param_count\n", "        Tconf /= param_count\n", "    \n", "    return [loss, train_accu, test_accu, <PERSON><PERSON>, Tconf, nll, brier, ece, ood_auc]\n", "\n", "#%% SimpleCNN\n", "class SimpleCNN(nn.Module):\n", "    def __init__(self):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, padding=1)\n", "        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)\n", "        self.conv2 = nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1)\n", "        self.conv3 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1)\n", "\n", "        self.fc_input_size = 128 * 3 * 3 # Based on MNIST image dimension after pooling\n", "\n", "        self.fc1 = nn.Linear(self.fc_input_size, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv1(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv2(x)))\n", "        x = self.pool(<PERSON><PERSON>relu(self.conv3(x)))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        x = F.log_softmax(x, dim=1)\n", "        return x\n", "\n", "#%% Parameters\n", "\n", "epochs = 25\n", "B_train = 10000   # batch size\n", "seed = 2\n", "meas_freq = 5     # evaluate network every meas_freq epochs\n", "\n", "gamma = 1\n", "temperature = 1\n", "T_schedule = None\n", "weight_decay = 1e-5\n", "\n", "alpha = 50\n", "alpha2 = 1/60000\n", "dtau = 4e-4\n", "m = 0.1\n", "M = 10\n", "\n", "cuda_idx = \"0\"\n", "dev_ids = [0]\n", "\n", "num_workers = 10\n", "criterion = nn.NLLLoss(reduction=\"mean\")\n", "\n", "torch.cuda.empty_cache()\n", "device = torch.device('cuda:'+str(cuda_idx) if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "\n", "# LOAD DATA\n", "transform = transforms.Compose([\n", "    transforms.To<PERSON><PERSON><PERSON>(), \n", "    transforms.Normalize((0.5,), (0.5,)) # MNIST normalization\n", "])\n", "\n", "train_dataset = datasets.MNIST(root='.', train=True, download=True, transform=transform)\n", "test_dataset = datasets.MNIST(root='.', train=False, download=True, transform=transform)\n", "# OOD dataset\n", "ood_dataset = datasets.FashionMNIST(root='.', train=False, download=True, transform=transform)\n", "\n", "    \n", "#%% TRAIN MODEL - SINGLE RUN \n", "\n", "torch.manual_seed(seed)\n", "\n", "model = SimpleCNN()\n", "\n", "if device.type == \"cuda\" and torch.cuda.device_count() > 1:\n", "    print(f\"Using {torch.cuda.device_count()} GPUs!\")\n", "    model = nn.DataParallel(model, device_ids = dev_ids)   \n", "model.to(device)\n", "\n", "# Create data loaders\n", "train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "ood_loader = torch.utils.data.DataLoader(ood_dataset, batch_size=B_train, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "\n", "sms_sampler = SMS_ZBAOABZ(model, train_loader, test_loader, ood_loader, criterion, dtau, weight_decay, gamma, alpha, alpha2, m, M, temperature, epochs, device, meas_freq)\n", "sms_results, sms_header = sms_sampler.train()\n", "       \n", "# Unpack results for plotting\n", "sms_results_dict = {h: sms_results[:, i] for i, h in enumerate(sms_header)}\n", "\n", "#%% Plotting\n", "\n", "# Plot Loss and Accuracies\n", "fig, ax = plt.subplots(3, 1, figsize=(10, 12))\n", "ax[0].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"Train Loss\"], marker='o')\n", "ax[0].set_title(\"Training Loss\")\n", "ax[0].set_ylabel(\"NLL Loss\")\n", "ax[1].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"Train Accu\"], marker='o', label=\"Train Accuracy\")\n", "ax[1].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"Test Accu\"], marker='o', label=\"Test Accuracy\")\n", "ax[1].set_title(\"Train and Test Accuracy\")\n", "ax[1].set_ylabel(\"Accuracy (%)\")\n", "ax[1].legend()\n", "ax[2].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"dt\"], marker='o', color='purple')\n", "ax[2].set_title(\"Adaptive Stepsize (dt)\")\n", "ax[2].set_ylabel(\"Stepsize\")\n", "for a in ax:\n", "    a.set_xlabel(\"Epochs\")\n", "    a.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Plot new metrics\n", "fig, ax = plt.subplots(4, 1, figsize=(10, 16))\n", "ax[0].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"NLL\"], marker='o', label=\"NLL\")\n", "ax[0].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"Brier\"], marker='o', label=\"Brier Score\")\n", "ax[0].set_title(\"NLL and Brier Score on Test Set\")\n", "ax[0].set_ylabel(\"Score\")\n", "ax[0].legend()\n", "\n", "ax[1].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"ECE\"], marker='o', color='green', label=\"ECE\")\n", "ax[1].set_title(\"Expected Calibration Error (ECE)\")\n", "ax[1].set_ylabel(\"ECE\")\n", "ax[1].legend()\n", "\n", "ax[2].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"OOD AUC\"], marker='o', color='red', label=\"OOD AUC (FashionMNIST)\")\n", "ax[2].set_title(\"Out-of-Distribution Detection AUC\")\n", "ax[2].set_ylabel(\"AUC\")\n", "ax[2].set_ylim(0, 1.05)\n", "ax[2].axhline(0.5, color='black', linestyle='--', label='Random Guess')\n", "ax[2].legend()\n", "\n", "ax[3].plot(sms_results_dict[\"Epoch\"], sms_results_dict[\"ESS\"], marker='o', color='orange', label=\"ESS of Loss\")\n", "ax[3].set_title(\"Effective Sample Size (ESS) of Training Loss\")\n", "ax[3].set_ylabel(\"ESS\")\n", "ax[3].set_yscale('log')\n", "ax[3].legend()\n", "\n", "\n", "for a in ax:\n", "    a.set_xlabel(\"Epochs\")\n", "    a.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Final ESS for training loss: {sms_results_dict['ESS'][-1]:.2f}\")"]}], "metadata": {"kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [], "dockerImageVersionId": 31040, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}