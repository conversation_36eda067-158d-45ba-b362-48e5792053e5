#include "zbaoabz_funnel9d_core.hpp"
#include <algorithm>
#include <numeric>

static std::random_device rd;
static std::mt19937 gen(rd());
static std::mt19937_64 gen64(rd());
static std::normal_distribution<double> N01(0.0, 1.0);
const double PI = 3.14159265358979323846;

struct Vec {
    std::vector<double> a;
    explicit Vec(size_t d=0, double v=0.0): a(d, v) {}
    size_t size() const { return a.size(); }
    double norm2() const { double s=0; for(double x:a) s+=x*x; return std::sqrt(s); }
    double dot(const Vec& b) const { double s=0; for(size_t i=0;i<a.size();++i) s+=a[i]*b.a[i]; return s; }
    Vec& operator+=(const Vec& b){ for(size_t i=0;i<a.size();++i) a[i]+=b.a[i]; return *this; }
    Vec& operator-=(const Vec& b){ for(size_t i=0;i<a.size();++i) a[i]-=b.a[i]; return *this; }
    Vec& operator*=(double s){ for(double& x:a) x*=s; return *this; }
};
inline Vec operator+(Vec x, const Vec& y){ x+=y; return x; }
inline Vec operator-(Vec x, const Vec& y){ x-=y; return x; }
inline Vec operator*(Vec x, double s){ x*=s; return x; }
inline Vec operator*(double s, Vec x){ x*=s; return x; }

// Funnel U and grad (θ, x1..xn), n = n_sat
static double U_funnel(const Vec& q, int n, double sigma_x2){
    const double theta = q.a[0];
    double sumx2 = 0.0;
    for(int i=1;i<=n;++i) sumx2 += q.a[i]*q.a[i];
    return theta*theta/6.0 + 0.5*n*theta + 0.5*std::exp(-theta)*sumx2 + 0.5*(1.0/sigma_x2)*sumx2;
}
static Vec gradU_funnel(const Vec& q, int n, double sigma_x2){
    const double theta = q.a[0];
    const double emt = std::exp(-theta);
    double sumx2 = 0.0;
    for(int i=1;i<=n;++i) sumx2 += q.a[i]*q.a[i];
    Vec g(q.size(), 0.0);
    g.a[0] = theta/3.0 + 0.5*n - 0.5*emt*sumx2;
    const double coeff = emt + 1.0/sigma_x2;
    for(int i=1;i<=n;++i) g.a[i] = coeff * q.a[i];
    return g;
}

// monitors/mappings
static double monitor_g_dt(const Vec& gradU, double scale_g){
    return gradU.norm2() / std::max(1e-12, scale_g);
}
static double psi_of_zeta_k(double z, double m, double M, double r){
    double zr = std::pow(z, r);
    return m * (zr + M/m) / (zr + 1.0);  // K2
}
static double zeta_step(double z_old, double alpha, double dtau, double gval){
    double rho = std::exp(-alpha * dtau);
    return rho * z_old + (1.0 - rho)/alpha * gval;
}
// noise-control
static double monitor_g_sigma(double Tconf_inst, double T, double threshold){
    double gap = std::abs(Tconf_inst - T)/std::max(1e-12, T);
    return gap - threshold;
}
static double psi_sigma(double sigma_zeta, double sigma_min, double sigma_max, double steepness){
    double sigma_range = sigma_max - sigma_min;
    double atan_val = std::atan(steepness * sigma_zeta);
    return (sigma_min + sigma_max)/2.0 - (sigma_range/PI) * atan_val;
}

// BAOAB constants and one step (vectorized)
struct BAOAB_Constants {
    double eta, xc1, xc2, xc3, vc1, vc2, vc3;
    BAOAB_Constants(double dt, double gamma, double T=1.0){
        double gh = gamma * dt;
        eta = std::exp(-gh);
        xc1 = 0.5*dt*(1+eta);
        xc2 = 0.25*dt*dt*(1+eta);
        double tmp = -std::expm1(-2.0*gh);
        tmp = std::max(tmp, 1e-16);
        xc3 = 0.5*dt*std::sqrt(tmp * T);
        vc1 = 0.5*dt*eta;
        vc2 = 0.5*dt;
        vc3 = std::sqrt(tmp * T);
    }
};
template<class GradFunc>
static void BAOAB_step(Vec& q, Vec& v, Vec& gradU_prev,
                       const BAOAB_Constants& C, GradFunc gradf){
    Vec xi(q.size());
    for(size_t i=0;i<q.size();++i) xi.a[i] = N01(gen);
    Vec q_new = q + v*C.xc1 - gradU_prev*C.xc2 + xi*C.xc3;
    Vec gradU_new = gradf(q_new);
    Vec v_new = v*C.eta - gradU_prev*C.vc1 - gradU_new*C.vc2 + xi*C.vc3;
    q = q_new; v = v_new; gradU_prev = gradU_new;
}

SimResult9D runZBAOABZ_funnel9d(
    int nmax, int nmeas, double dtau, double gamma, double T, GradientType grad_type,
    double alpha_dt, double scale_g, double m_dt, double M_dt, double r_dt, double z0_dt,
    int n_sat, double sigma_x2, const std::vector<double>& q0, const std::vector<double>& v0,
    int burn_in_noise, double initial_sigma, double sigma_min, double sigma_max,
    double alpha_sigma, double threshold_sigma, double steepness_sigma)
{
    const int D = n_sat + 1;
    Vec q(D), v(D);
    q.a = q0; v.a = v0;

    double zeta_dt = z0_dt;
    double sigma = initial_sigma;
    double sigma_zeta = 0.0;

    // grad function with optional additive noise
    auto gradf = [&](const Vec& qq){
        Vec g = gradU_funnel(qq, n_sat, sigma_x2);
        if (grad_type == GradientType::Perturbed){
            for(int i=0;i<D;++i) g.a[i] += sigma * N01(gen64);
        }
        return g;
    };
    auto Uval = [&](const Vec& qq){ return U_funnel(qq, n_sat, sigma_x2); };
    auto psi_dt = [&](double z){ return psi_of_zeta_k(z, m_dt, M_dt, r_dt); };

    SimResult9D R;
    R.theta.reserve(nmax+1);
    R.x1.reserve(nmax+1);
    R.dt.reserve(nmax+1);
    R.psi.reserve(nmax+1);
    R.Umean.reserve((nmax+1)/nmeas + 1);
    R.Tkin.reserve((nmax+1)/nmeas + 1);
    R.Tconf.reserve((nmax+1)/nmeas + 1);
    R.sigmas.reserve((nmax+1)/nmeas + 1);
    R.sigma_zetas.reserve((nmax+1)/nmeas + 1);

    Vec g = gradf(q);
    double gval_dt = monitor_g_dt(g, scale_g);
    double sum_psi = 0.0;

    // initial record
    R.theta.push_back(q.a[0]);
    R.x1.push_back(q.a[1]);
    double current_dt = psi_dt(zeta_dt) * dtau;
    R.dt.push_back(current_dt);
    R.psi.push_back(psi_dt(zeta_dt));

    for(int i=0;i<nmax;++i){
        // Z half-step for dt
        double zhalf_dt = zeta_step(zeta_dt, alpha_dt, 0.5*dtau, gval_dt);
        current_dt = psi_dt(zhalf_dt) * dtau;

        // BAOAB
        BAOAB_step(q, v, g, BAOAB_Constants(current_dt, gamma, T), gradf);

        // Z completion for dt
        gval_dt = monitor_g_dt(g, scale_g);
        zeta_dt = zeta_step(zhalf_dt, alpha_dt, 0.5*dtau, gval_dt);

        // record trajectory slice
        R.theta.push_back(q.a[0]);
        R.x1.push_back(q.a[1]);
        R.dt.push_back(current_dt);
        double current_psi = psi_dt(zeta_dt);
        R.psi.push_back(current_psi);

        // measurements every nmeas
        if (((i+1) % nmeas) == 0){
            double U = Uval(q);
            double Tkin = 0.5 * v.dot(v);    // 0.5 ||v||^2
            double Tconf = 0.5 * q.dot(g);   // same as your 2D code (virial-type)

            if (R.Umean.empty()){
                sum_psi = current_psi;
                R.Umean.push_back(U);
                R.Tkin.push_back(Tkin);
                R.Tconf.push_back(Tconf);
            } else {
                double S_old = sum_psi; sum_psi += current_psi;
                R.Umean.push_back((S_old*R.Umean.back() + current_psi*U)/sum_psi);
                R.Tkin.push_back((S_old*R.Tkin.back() + current_psi*Tkin)/sum_psi);
                R.Tconf.push_back((S_old*R.Tconf.back() + current_psi*Tconf)/sum_psi);
            }

            // adaptive noise after burn-in
            if (grad_type == GradientType::Perturbed && (i+1) > burn_in_noise){
                double gval_sigma = monitor_g_sigma(Tconf, T, threshold_sigma);
                sigma_zeta = zeta_step(sigma_zeta, alpha_sigma, dtau * nmeas, gval_sigma);
                sigma = psi_sigma(sigma_zeta, sigma_min, sigma_max, steepness_sigma);
            }
            R.sigmas.push_back(sigma);
            R.sigma_zetas.push_back(sigma_zeta);
        }
    }
    return R;
}
