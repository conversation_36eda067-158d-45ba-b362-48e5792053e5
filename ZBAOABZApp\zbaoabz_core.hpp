#pragma once
#include <vector>
#include <utility>

// Enumerations for potential, kernel, and gradient choices
enum class Potential { Funnel=0, Channel=1, Beale=2 };
enum class Kernel    { K1=0, K2=1 };
enum class GradientType { Standard=0, Perturbed=1 }; // New: Choice for gradient

struct SimulationResult {
    std::vector<double> x, y, dt, psi;      // trajectory slices
    std::vector<double> Umean, Tkin, Tconf; // running mean-potential, kinetic, and confinement
    std::vector<double> Unorm;             // NEW: gradient norm at each step
};

// Updated Signature: one call for ANY potential / kernel / gradient
// Added GradientType grad_type and double sigma
SimulationResult runZBAOABZ(
    Potential   pot,
    Kernel      kern,
    GradientType grad_type,
    int         nmax,
    int         nmeas,
    double      dtau,
    double      gamma,
    double      alpha,
    double      eps,
    double      sigma,      // New: noise level for perturbed gradient
    double      scale_g,
    double      T,
    double      m,
    double      M,
    double      r,
    std::pair<double,double> x0,
    std::pair<double,double> v0,
    double      z0
);

// #pragma once
// #include <vector>
// #include <utility>

// // Enumerations for potential & kernel choices
// enum class Potential { Funnel=0, Channel=1, Beale=2 };
// enum class Kernel    { K1=0, K2=1 };

// struct SimulationResult {
//     std::vector<double> x, y, dt, psi;      // trajectory slices
//     std::vector<double> Umean;              // running mean-potential
// };

// // Signature: one call for ANY potential / kernel
// SimulationResult runZBAOABZ(
//     Potential   pot,
//     Kernel      kern,
//     int         nmax,
//     int         nmeas,
//     double      dtau,
//     double      gamma,
//     double      alpha,
//     double      eps,
//     double      scale_g,
//     double      T,
//     double      m,
//     double      M,
//     double      r,
//     std::pair<double,double> x0,
//     std::pair<double,double> v0,
//     double      z0
// );
