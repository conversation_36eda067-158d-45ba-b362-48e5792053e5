#pragma once
#include <vector>
#include <utility>

// Enumerations for potential, kernel, and gradient choices
enum class Potential { Funnel=0, Channel=1, Beale=2 };
enum class Kernel    { K1=0, K2=1 };
enum class GradientType { Standard=0, Perturbed=1 };

struct SimulationResult {
    std::vector<double> x, y, dt, psi;      // Trajectory slices
    std::vector<double> Umean, Tkin, Tconf; // Running mean observables
    std::vector<double> sigmas;             // NEW: History of the adaptive noise scale sigma
    std::vector<double> sigma_zetas;        // NEW: History of the auxiliary variable for sigma
};

// --- UPDATED FUNCTION SIGNATURE ---
// This now includes parameters for the new adaptive noise control mechanism.
// The old fixed 'sigma' is replaced by 'initial_sigma'.
SimulationResult runZBAOABZ(
    // System and Integrator Parameters
    Potential   pot,
    Kernel      kern,
    GradientType grad_type,
    int         nmax,
    int         nmeas,
    double      dtau,
    double      gamma,
    double      eps,
    double      T,
    std::pair<double,double> x0,
    std::pair<double,double> v0,

    // Parameters for SamAdams Step-Size Control (dt)
    double      alpha_dt,
    double      scale_g,
    double      m_dt,
    double      M_dt,
    double      r_dt,
    double      z0_dt,

    // Parameters for Adaptive Noise Control (sigma)
    int         burn_in_noise,
    double      initial_sigma,
    double      sigma_min,
    double      sigma_max,
    double      alpha_sigma,
    double      threshold_sigma,
    double      steepness_sigma
);

// version Before 8.2
// #pragma once
// #include <vector>
// #include <utility>

// // Enumerations for potential, kernel, and gradient choices
// enum class Potential { Funnel=0, Channel=1, Beale=2 };
// enum class Kernel    { K1=0, K2=1 };
// enum class GradientType { Standard=0, Perturbed=1 }; // New: Choice for gradient

// struct SimulationResult {
//     std::vector<double> x, y, dt, psi;      // trajectory slices
//     std::vector<double> Umean, Tkin, Tconf; // running mean-potential, kinetic, and confinement
//     // std::vector<double> Unorm;             // NEW: gradient norm at each step
//     std::vector<double> sigmas;             // NEW: noise level for perturbed gradient at each step
// };

// // Updated Signature: one call for ANY potential / kernel / gradient
// // Added GradientType grad_type and double sigma
// SimulationResult runZBAOABZ(
//     Potential   pot,
//     Kernel      kern,
//     GradientType grad_type,
//     int         nmax,
//     int         nmeas,
//     double      dtau,
//     double      gamma,
//     double      alpha,
//     double      eps,
//     double      sigma,      // New: noise level for perturbed gradient
//     double      scale_g,
//     double      T,
//     double      m,
//     double      M,
//     double      r,
//     std::pair<double,double> x0,
//     std::pair<double,double> v0,
//     double      z0
// );

// Original version
// #pragma once
// #include <vector>
// #include <utility>

// // Enumerations for potential & kernel choices
// enum class Potential { Funnel=0, Channel=1, Beale=2 };
// enum class Kernel    { K1=0, K2=1 };

// struct SimulationResult {
//     std::vector<double> x, y, dt, psi;      // trajectory slices
//     std::vector<double> Umean;              // running mean-potential
// };

// // Signature: one call for ANY potential / kernel
// SimulationResult runZBAOABZ(
//     Potential   pot,
//     Kernel      kern,
//     int         nmax,
//     int         nmeas,
//     double      dtau,
//     double      gamma,
//     double      alpha,
//     double      eps,
//     double      scale_g,
//     double      T,
//     double      m,
//     double      M,
//     double      r,
//     std::pair<double,double> x0,
//     std::pair<double,double> v0,
//     double      z0
// );
