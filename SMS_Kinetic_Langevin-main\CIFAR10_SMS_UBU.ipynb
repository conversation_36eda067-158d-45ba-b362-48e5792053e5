{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "bGU6NwlsXFSt"}, "outputs": [], "source": ["#@title Import Dependencies\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.datasets as dsets\n", "import torchvision.transforms as transforms\n", "from torchvision.transforms import v2 as v2\n", "from torch.autograd import Variable\n", "import itertools\n", "import pickle\n", "import numpy as np\n", "from numpy import random\n", "#import tltorch\n", "\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "device_cpu=torch.device(\"cpu\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#from tqdm import tqdm\n", "from PIL import Image\n", "#from matplotlib import pyplot as plt\n", "#import matplotlib\n", "from typing import TypeVar, Dict\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.optim.optimizer import Optimizer\n", "\n", "# from counterfactuals.utils import make_dir, get_transforms, torch_to_image, expl_to_image\n", "# from counterfactuals.plot import plot_grid_part\n", "# from counterfactuals.generative_models.base import GenerativeModel\n", "# from counterfactuals.classifiers.base import NeuralNet\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "#import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "#from counterfactuals.classifiers.base import NeuralNet\n", "#import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "# import counterfactuals.classifiers.cnn as classifiers\n", "# import counterfactuals.classifiers.unet as unet\n", "# from counterfactuals.utils import load_checkpoint\n", "# from counterfactuals.data import get_data_info\n", "# from counterfactuals.generative_models.factory import get_generative_model\n", "import copy\n", "import gc"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["#@title Define Hyperparameters\n", "\n", "# class_names = [\"5_o_Clock_Shadow\", \"Arched_Eyebrows\", \"Attractive\", \"Bags_Under_Eyes\", \"Bald\", \"<PERSON><PERSON>\",\n", "#                 \"<PERSON>_Li<PERSON>\", \"Big_Nose\", \"Black_Hair\", \"Blond_Hair\", \"Blurry\", \"Brown_Hair\", \"Bushy_Eyebrows\",\n", "#                 \"<PERSON><PERSON>\", \"<PERSON>_<PERSON>\", \"Eyeglass<PERSON>\", \"<PERSON><PERSON>\", \"Gray_Hair\", \"Heavy_Makeup\", \"High_Cheekbones\",\n", "#                 \"Male\", \"Mouth_Slightly_Open\", \"<PERSON><PERSON>\", \"<PERSON>rrow_Eyes\", \"No_Beard\", \"Oval_Face\", \"<PERSON><PERSON>_Skin\",\n", "#                 \"<PERSON>y_Nose\", \"<PERSON><PERSON>_Hairline\", \"Rosy_Cheeks\", \"Sideburns\", \"Smiling\", \"Straight_Hair\",\n", "#                 \"Wavy_<PERSON>\", \"Wearing_Earrings\", \"Wearing_Hat\", \"Wearing_Lipstick\", \"Wearing_Necklace\",\n", "#                 \"<PERSON><PERSON>_<PERSON><PERSON>\", \"<PERSON>\"]\n", "# num_classes = 40\n", "# data_shape = [3, 64, 64]\n", "# n_bits = 5\n", "# temp = 0.7\n", "# data_mean = [0.485, 0.456, 0.406]\n", "# data_std = [0.229, 0.224, 0.225]\n", "\n", "input_size = 32*32*3 # img_size = (28,28) ---> 28*28=784 in total\n", "batch_size = 200 # the size of input data took for one iteration"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "lCsBCXMwbpH5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files already downloaded and verified\n", "Files already downloaded and verified\n"]}], "source": ["# from torchvision.transforms import v2\n", "# transform_RandomErasing=transforms.Compose([v2.RandomErasing(),\n", "#                               transforms.ToTensor()])\n", "img_size=32\n", "transform_test=transforms.Compose([transforms.ToTensor()])\n", "#transform=v2.Compose([v2.RandomResizedCrop((img_size,img_size),antialias=True),v2.RandomRotation(20),transforms.ToTensor()])\n", "            \n", "train_data = dsets.CIFAR10(root = './data', train=True, transform = transform_test, download = True)\n", "test_data = dsets.CIFAR10(root = './data', train=False, transform = transform_test, download = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "rfDPBdnYgfGp"}, "outputs": [], "source": ["#@title Loading the data\n", "\n", "train_gen = torch.utils.data.DataLoader(dataset = train_data,\n", "                                             batch_size = batch_size,\n", "                                             shuffle = True)\n", "\n", "test_gen = torch.utils.data.DataLoader(dataset = test_data,\n", "                                      batch_size = batch_size,\n", "                                      shuffle = False)\n", "\n", "no_batches=len(train_gen)\n", "test_no_batches=len(test_gen)\n", "train_data_len=len(train_data)\n", "test_data_len=len(test_data)\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#cvmx=torch.zeros([3*64*64,3*64*64],device=device)\n", "images_list=[]\n", "labels_list=[]\n", "no_batches=len(train_gen)\n", "#images_mean=torch.zeros(3,64,64,device=device)\n", "for i ,(images,labels) in enumerate(train_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    # images_mean=images_mean+images.mean(0)\n", "    # im=torch.reshape(images,[images.shape[0],3*64*64])\n", "    # cvmx+=torch.matmul(torch.transpose(im,0,1),im)\n", "    if(i<(len(train_gen))):\n", "        images_list.append(images)\n", "        labels_list.append(labels)\n", "\n", "\n", "\n", "test_images_list=[]\n", "test_labels_list=[]\n", "test_no_batches=len(test_gen)\n", "for i ,(images,labels) in enumerate(test_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    if(i<(len(test_gen))):\n", "        test_images_list.append(images)\n", "        test_labels_list.append(labels)\n", "\n", "train_data_len=len(train_data)\n", "test_data_len=len(test_data)\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "fL-Y<PERSON><PERSON><PERSON><PERSON>_"}, "outputs": [], "source": ["#@title Define model class\n", "import torch.nn as nn\n", "import torch\n", "from counterfactuals.classifiers.base import NeuralNet\n", "import torch.nn.functional as F\n", "from counterfactuals.utils import load_checkpoint\n", "from counterfactuals.utils import save_checkpoint\n", "\n", "from typing import TypeVar, <PERSON><PERSON>\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "\n", "class CNN(NeuralNet):\n", "    \"\"\"\n", "    CNN for (binary) classification for CelebA, CheXpert\n", "    \"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 3,\n", "                 num_classes: int = 2,\n", "                 flattened_size: int = 16384,\n", "                 low_rank: int = 32,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(CNN, self).__init__()\n", "        self.conv_layer = nn.Sequential(            \n", "            nn.Conv2d(in_channels=in_channels, out_channels=512, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(512,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=512, out_channels=512, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(512,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=512, out_channels=512, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(512,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=512, out_channels=512, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),            \n", "            nn.BatchNorm2d(512,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=512, out_channels=512, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "            nn.BatchNorm2d(512,momentum=batch_norm_mom),\n", "            # # Conv Layer block 2\n", "            nn.Conv2d(in_channels=512, out_channels=768, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(768,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=768, out_channels=768, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(768,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=768, out_channels=768, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(768,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=768, out_channels=768, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),            \n", "            nn.BatchNorm2d(768,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=768, out_channels=768, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "            nn.BatchNorm2d(768,momentum=batch_norm_mom),\n", "            #Conv Layer block 3\n", "            nn.Conv2d(in_channels=768, out_channels=1024, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(1024,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=1024, out_channels=1024, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),     \n", "            nn.BatchNorm2d(1024,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=1024, out_channels=1024, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),             \n", "            nn.BatchNorm2d(1024,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=1024, out_channels=1024, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm2d(1024,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=1024, out_channels=1024, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),            \n", "            nn.MaxPool2d(kernel_size=2, stride=2),            \n", "        )\n", "\n", "        self.fc_layer = nn.Sequential(\n", "            nn.<PERSON>(),\n", "            nn.BatchNorm1d(flattened_size,momentum=batch_norm_mom),\n", "            nn.Linear(flattened_size, low_rank),\n", "            nn.BatchNorm1d(low_rank,momentum=batch_norm_mom),\n", "            nn.<PERSON><PERSON>(low_rank,2048),            \n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm1d(2048,momentum=batch_norm_mom),\n", "            nn.<PERSON><PERSON>(2048,low_rank),            \n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm1d(low_rank,momentum=batch_norm_mom),\n", "            nn.<PERSON><PERSON>(low_rank,1024),            \n", "            nn.Softplus(beta=1.0),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "        )\n", "\n", "        self.last_layer=nn.Sequential(\n", "            nn.Linear(1024, num_classes)\n", "        )\n", "\n", "    def forward(self, x: Tensor) -> Tensor:\n", "        \"\"\"Perform forward.\"\"\"\n", "\n", "        # conv layers\n", "        x = self.conv_layer(x)\n", "\n", "\n", "        # fc layer\n", "        x = self.fc_layer(x)\n", "        \n", "        # last layer\n", "        x=self.last_layer(x)\n", "\n", "        return x\n", "\n", "    def classify(self, x: Tensor) -> <PERSON><PERSON>[Tensor, Tensor, Tensor]:\n", "        net_out = self.forward(x)\n", "        acc = F.softmax(net_out, dim=1)\n", "        class_idx = torch.max(net_out, 1)[1]\n", "\n", "        return acc, acc[0, class_idx], class_idx\n", "    \n", "\n", "\n", "class CIFAR10_CNN(CNN):\n", "    \"\"\"CNN.\"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 3,\n", "                 num_classes: int = 10,\n", "                 flattened_size: int = 16384,\n", "                 low_rank: int = 512,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(CIFAR10_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                         flattened_size=flattened_size,low_rank=low_rank,batch_norm_mom=batch_norm_mom)\n", "\n", "\n", "class Net(nn.Module):\n", "  def __init__(self, input_size, hidden_size, num_classes):\n", "    super(Net,self).__init__()\n", "    self.fc1 = nn.Linear(input_size, hidden_size)\n", "    self.relu = nn.ReLU()\n", "    self.fc2 = nn.Linear(hidden_size, num_classes)\n", "\n", "  def forward(self,x):\n", "    out = self.fc1(x)\n", "    out = self.relu(out)\n", "    out = self.fc2(out)\n", "    return out"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["144326410"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["net=CIFAR10_CNN()\n", "n=0\n", "for par in net.parameters():\n", "    n+=par.numel()\n", "n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "ePLIwvAFj2zH"}, "outputs": [], "source": ["#@title Define loss-function & optimizer\n", "loss_function = nn.CrossEntropyLoss()\n", "\n", "\n", "def images_regulariser(net): \n", "    li_reg_loss = 0\n", "    penalized     = [p for name,p in net.named_parameters() if 'bias' not in name]\n", "    not_penalized = [p for name,p in net.named_parameters() if 'bias' in name]\n", "    for p in penalized:\n", "        li_reg_loss += (p**2).sum()*0.5\n", "    #for p in net.parameters():\n", "#        li_reg_loss += (p**2).sum()*0.5\n", "    reg=li_reg_loss/(train_data_len)*l2regconst\n", "    return(reg)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def addnet(net,net2):\n", "    for param1, param2 in zip(net.parameters(), net2.parameters()):\n", "     param1.data += param2.data\n", "\n", "def multiplynet(net,a):\n", "   for param1 in net.parameters():\n", "     param1.data *=a"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "@dataclass\n", "class hclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    etam1g: Tensor\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc):\n", "    xi1=torch.randn(x.size(),device=device)\n", "    xi2=torch.randn(x.size(),device=device)\n", "\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n", "\n", "def bounce(x,v,xstar,width):\n", "    vsign=(((x-xstar+width)/(2*width)).floor()% 2)*(-2)+1\n", "    vn=v*vsign\n", "    xn=((x-xstar-width)% (4*width)-2*width).abs()-width+xstar\n", "    # num_outside=((xn-xstar)>width).sum()+((xstar-xn)>width).sum()\n", "    # if(num_outside>0):\n", "    #     print(num_outside)    \n", "    return([xn, vn])\n", "\n", "def bouncenet():\n", "    for p,p_star in zip(net.parameters(),net_star.parameters()):\n", "        [p.data, p.v]=bounce(p.data, p.v, p_star.data.cuda(), 6/torch.sqrt(l2regconst_extra))\n", "\n", "def UBU_step(hper2c,images,labels,batch_it):   \n", "    with torch.no_grad():\n", "        for p in list(net.parameters()):\n", "            # maxlen=20*torch.sqrt((torch.tensor(torch.numel(p.v))).float())\n", "            # if(torch.norm(p.v)>maxlen):\n", "            #    print(\"big trouble!!!!\")    \n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "        bouncenet()\n", "    #print(\"outputsU\",len(outputsU))\n", "    #print(\"labelsU\",len(labels))\n", "    #print(\"imagesU\",len(images))\n", "    outputsU = net(images)\n", "    loss_likelihood = loss_function(outputsU, labels)  \n", "\n", "\n", "    grads_reg=[torch.zeros_like(par) for par in net.parameters()]\n", "    net_pars=list(net.parameters())\n", "    with torch.no_grad():\n", "        for it in range(len_params):\n", "            if(list_no_bias[it]):\n", "                grads_reg[it]=net_pars[it].data*l2regconst\n", "\n", "    net.zero_grad()\n", "    #loss.backward()\n", "    loss_likelihood.backward()\n", "    with torch.no_grad():\n", "        grads_likelihood=[par.grad*batch_size for par in net.parameters()]\n", "    \n", "        #Normal, no variance reduction\n", "        # for p,p_star in zip(net.parameters(),net_star.parameters()):      \n", "        #     p.v-=hper2c.h*(p.grad*train_data_len+l2regconst_extra*(p.data-p_star.data))\n", "\n", "        for p,grad,grad_reg,p_star,grad_star,star_sum_grad in zip(list(net.parameters()),grads_likelihood,grads_reg,list(net_star.parameters()),net_star_grad_list[batch_it],net_star_full_grad):              \n", "            #Using variance reduction\n", "            p.v-=hper2c.h*(grad_reg+star_sum_grad+(grad-grad_star.cuda())*no_batches+l2regconst_extra*(p.data-p_star.data))\n", "\n", "            # maxlen=20*torch.sqrt((torch.tensor(torch.numel(p.v))).float())\n", "            # if(torch.norm(p.v)>maxlen):\n", "            #     print(\"trouble\")\n", "                #p.v=(p.v/torch.norm(p.v))*maxlen\n", "        # for it in range(len_params):\n", "        #     [list(net.parameters())[it].data,list(net.parameters())[it].v]=U(list(net.parameters())[it].data,list(net.parameters())[it].v,hper2c)        \n", "        for p in list(net.parameters()):\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "    #bouncenet()\n", "    return(loss_likelihood.data)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["#net = Fashion_MNIST_CNN().cuda()\n", "#net2=copy.deepcopy(net)\n", "#addnet(net2,net)\n", "#multiplynet(net2,1/10)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["filepath=\"output_CIFAR10_6.pickle\"\n", "#filepath=\"output_fashion_low_rank_long.pickle\"\n", "with open(filepath,\"rb\") as file:\n", "   [labels_arr,test_labels_arr,test_prob_arr]=pickle.load(file)\n", "labels_arr=torch.tensor(labels_arr).detach()\n", "test_labels_arr=torch.tensor(test_labels_arr).detach().cpu()\n", "test_prob_arr=torch.tensor(test_prob_arr).detach().cpu()\n", "\n", "par_runs=4\n", "num_classes=10\n", "num_epochs=90\n", "switch_to_sampling_epoch=30\n", "switch_to_swa_epoch=22"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "u75Xa5VckuTH"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["par_it: 0 \n", "\n", "Epoch [1/90], Step [250/250]\n", "Test accuracy of the model: 27.757 %\n", "Epoch [1], Average Loss: 2.3519\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-14-36df626ce7c4>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     61\u001b[0m         \u001b[0mloss_likelihood\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mloss_function\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0moutputs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mlabels\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     62\u001b[0m         \u001b[0msum_loss\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0msum_loss\u001b[0m\u001b[1;33m+\u001b[0m\u001b[0mloss_likelihood\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 63\u001b[1;33m         \u001b[0mreg\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mimages_regulariser\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnet\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     64\u001b[0m         \u001b[0mloss\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mloss_likelihood\u001b[0m\u001b[1;33m+\u001b[0m\u001b[0mreg\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     65\u001b[0m         \u001b[0moptimizer\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mzero_grad\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m<ipython-input-9-3bd80b68fe19>\u001b[0m in \u001b[0;36mimages_regulariser\u001b[1;34m(net)\u001b[0m\n\u001b[0;32m      8\u001b[0m     \u001b[0mnot_penalized\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;33m[\u001b[0m\u001b[0mp\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mname\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mp\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mnet\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnamed_parameters\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mif\u001b[0m \u001b[1;34m'bias'\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mname\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      9\u001b[0m     \u001b[1;32mfor\u001b[0m \u001b[0mp\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mpenalized\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 10\u001b[1;33m         \u001b[0mli_reg_loss\u001b[0m \u001b[1;33m+=\u001b[0m \u001b[1;33m(\u001b[0m\u001b[0mp\u001b[0m\u001b[1;33m**\u001b[0m\u001b[1;36m2\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msum\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m*\u001b[0m\u001b[1;36m0.5\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     11\u001b[0m     \u001b[1;31m#for p in net.parameters():\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     12\u001b[0m \u001b[1;31m#        li_reg_loss += (p**2).sum()*0.5\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["#@title Output arrays\n", "par_runs=64\n", "num_classes=10\n", "num_epochs=90\n", "switch_to_sampling_epoch=30\n", "switch_to_swa_epoch=22\n", "\n", "num_swag_epochs=switch_to_sampling_epoch-switch_to_swa_epoch\n", "\n", "training_size=no_batches*batch_size\n", "test_size=test_data_len\n", "labels_arr=torch.zeros(training_size)\n", "test_labels_arr=torch.zeros(test_size).cpu()\n", "test_prob_arr=torch.zeros([test_size,num_classes,num_epochs,par_runs]).cpu()\n", "\n", "lr = 1e-2\n", "lr_swag=1e-3\n", "h=5e-4\n", "l2regconst=torch.tensor(1).detach()\n", "l2regconst_extra=torch.tensor(25).detach()\n", "gam=torch.sqrt(l2regconst_extra)\n", "hper2c=hper2const(torch.tensor(h/2),gam)\n", "\n", "for par_it in range(par_runs):\n", "  print(\"par_it:\",par_it,\"\\n\")\n", "  #@title Build the model\n", "  net = CIFAR10_CNN().cuda()\n", "  net.train()\n", "  optimizer = torch.optim.Adam(net.parameters(), lr=lr)\n", "  \n", "  lr_scheduler = torch.optim.lr_scheduler.PolynomialLR(optimizer=optimizer, total_iters=switch_to_swa_epoch,power=1)\n", "\n", "\n", "  #@title Training the model\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    #l2regconst=torch.min(torch.tensor(1+epoch),torch.tensor(switch_to_swa_epoch)).detach()\n", "    net.train()\n", "    if(epoch==(switch_to_swa_epoch-1)):\n", "      net2=copy.deepcopy(net)\n", "      multiplynet(net2,0)\n", "      optimizer=torch.optim.Adam(net.parameters(),lr=lr_swag)\n", "\n", "    if(epoch>=switch_to_sampling_epoch and (epoch-switch_to_sampling_epoch)%2==0):\n", "        rperm=random.permutation(list(range(no_batches)))\n", "    \n", "    for i in range(no_batches): \n", "      if(epoch<switch_to_sampling_epoch):\n", "        b=torch.randint(high=no_batches,size=(1,1))\n", "      else:\n", "        it=(epoch-switch_to_sampling_epoch)*no_batches+i\n", "        b=rperm[ind_create(it)]\n", "\n", "      images=images_list[b]\n", "      labels=labels_list[b]\n", "\n", "\n", "      if(epoch<switch_to_sampling_epoch):\n", "        outputs = net(images)    \n", "        loss_likelihood = loss_function(outputs, labels)\n", "        sum_loss=sum_loss+loss_likelihood    \n", "        reg=images_regulariser(net)\n", "        loss=loss_likelihood+reg\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        if(epoch>=(switch_to_swa_epoch)):\n", "          addnet(net2,net)\n", "      else:\n", "        loss_likelihood=UBU_step(hper2c,images,labels,b)\n", "        sum_loss=sum_loss+loss_likelihood\n", "\n", "\n", "\n", "\n", "    #if (i+1) % (no_batches) == 0:\n", "    #print(\"Reg:\",reg)\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0\n", "    \n", "\n", "\n", "\n", "\n", "\n", "    #for imagest,labelst in eval_gen:\n", "    if epoch==(switch_to_sampling_epoch-1):\n", "      multiplynet(net2,1/(num_swag_epochs*no_batches))\n", "      multiplynet(net,0)\n", "      addnet(net,net2)\n", "      del net2\n", "\n", "      net_star=copy.deepcopy(net)\n", "      len_params=len(list(net_star.parameters()))\n", "\n", "      #Variance reduction - saving gradients at each batch at x_star\n", "      net_star_grad_list=[]\n", "      net_star_full_grad=[torch.zeros_like(par, device=device) for par in list(net_star.parameters())]\n", "      for i in range(no_batches):\n", "          images=images_list[i]\n", "          labels=labels_list[i]\n", "          outputs=net_star(images)\n", "          loss_likelihood = loss_function(outputs, labels)\n", "          reg=images_regulariser(net)\n", "          net_star.zero_grad()\n", "          loss_likelihood.backward()\n", "          grads=[par.grad*batch_size for par in list(net_star.parameters())]\n", "          grads_cpu=[par.cpu() for par in grads]\n", "          net_star_grad_list.append(grads_cpu)\n", "          for g, gi in zip(net_star_full_grad,grads):\n", "            g+=gi\n", "\n", "      with torch.no_grad():\n", "        len_params=len(list(net_star.parameters()))\n", "        list_no_bias=torch.zeros(len_params)\n", "        pit=0\n", "        for name, p in net_star.named_parameters():\n", "            if 'bias' not in name:\n", "                list_no_bias[pit]=1.0\n", "            pit+=1\n", "        #Initialise velocities\n", "        for par in list(net.parameters()):\n", "          par.v = torch.randn_like(par,device=device)      \n", "\n", "    with torch.no_grad():\n", "      net(torch.cat(images_list[0:15],dim=0).detach())\n", "      net.eval()\n", "\n", "      gc.collect()\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:,epoch,par_it]=torch.softmax(outputt,dim=1).detach().cpu()\n", "\n", "    \n", "\n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "\n", "    if(epoch<=switch_to_swa_epoch):\n", "      lr_scheduler.step()\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))  \n", "  \n", "  filepath=\"output_CIFAR10_9.pickle\"\n", "  with open(filepath,\"wb\") as file:\n", "    pickle.dump([labels_arr.numpy(),test_labels_arr.numpy(),test_prob_arr.numpy()],file)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([1165.5272], device='cuda:0')\n", "tensor([755119.0625], device='cuda:0')\n", "tensor([1553897.], device='cuda:0')\n", "tensor([1891544.7500], device='cuda:0')\n", "tensor([2051748.5000], device='cuda:0')\n", "tensor([2121906.2500], device='cuda:0')\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-58-67bb6d07e69e>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m    121\u001b[0m \u001b[0mvec\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mmultiply_par\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mvec\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mtorch\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mTensor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m/\u001b[0m\u001b[0mnorm_iter\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    122\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0miter\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m10\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 123\u001b[1;33m     \u001b[0mvec\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mhess_vec_full\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mvec\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    124\u001b[0m     \u001b[0mnorm_iter\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mnorm_par\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mvec\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    125\u001b[0m     \u001b[0mvec\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mmultiply_par\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mvec\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m/\u001b[0m\u001b[0mnorm_iter\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m<ipython-input-58-67bb6d07e69e>\u001b[0m in \u001b[0;36mhess_vec_full\u001b[1;34m(vec)\u001b[0m\n\u001b[0;32m     77\u001b[0m         \u001b[0mimages\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mimages_list\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mit\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     78\u001b[0m         \u001b[0mlabels\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mlabels_list\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mit\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 79\u001b[1;33m         \u001b[0mhv\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mhess_vec\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mvec\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mimages\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mlabels\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     80\u001b[0m         \u001b[1;32mfor\u001b[0m \u001b[0mpit\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mlen_pars\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     81\u001b[0m             \u001b[0mhvfull\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mpit\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m+=\u001b[0m\u001b[0mhv\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mpit\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m<ipython-input-58-67bb6d07e69e>\u001b[0m in \u001b[0;36mhess_vec\u001b[1;34m(vec, images, labels)\u001b[0m\n\u001b[0;32m     59\u001b[0m         \u001b[0mres\u001b[0m\u001b[1;33m+=\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mgrad\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mit\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0mvec\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mit\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msum\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     60\u001b[0m     \u001b[0mnet_star\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mzero_grad\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 61\u001b[1;33m     \u001b[0mhvp\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mlist\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mtorch\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mautograd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgrad\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mres\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mlist\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnet_star\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mparameters\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mcreate_graph\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mFalse\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     62\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     63\u001b[0m     \u001b[1;32mreturn\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mhvp\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\torchenv39\\lib\\site-packages\\torch\\autograd\\__init__.py\u001b[0m in \u001b[0;36mgrad\u001b[1;34m(outputs, inputs, grad_outputs, retain_graph, create_graph, only_inputs, allow_unused, is_grads_batched, materialize_grads)\u001b[0m\n\u001b[0;32m    410\u001b[0m         )\n\u001b[0;32m    411\u001b[0m     \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 412\u001b[1;33m         result = _engine_run_backward(\n\u001b[0m\u001b[0;32m    413\u001b[0m             \u001b[0mt_outputs\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    414\u001b[0m             \u001b[0mgrad_outputs_\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\torchenv39\\lib\\site-packages\\torch\\autograd\\graph.py\u001b[0m in \u001b[0;36m_engine_run_backward\u001b[1;34m(t_outputs, *args, **kwargs)\u001b[0m\n\u001b[0;32m    742\u001b[0m         \u001b[0munregister_hooks\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0m_register_logging_hooks_on_whole_graph\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mt_outputs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    743\u001b[0m     \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 744\u001b[1;33m         return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass\n\u001b[0m\u001b[0;32m    745\u001b[0m             \u001b[0mt_outputs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    746\u001b[0m         )  # Calls into the C++ engine to run the backward pass\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "      # for mod in list(net.named_modules()):\n", "      #   if(hasattr(mod[1],\"momentum\")):\n", "      #     mod[1].momentum=None\n", "      # for i in range(no_batches):\n", "      #   net(images_list[i].detach())\n", "\n", "#net_star=Fashion_MNIST_CNN().cuda()\n", "#net_star=net\n", "# net_star=copy.deepcopy(net_star_backup)\n", "# net_star.train()\n", "# with torch.no_grad():\n", "#     for par in net_star.parameters():\n", "#         par+=(torch.rand_like(par)*2-1)*6/torch.sqrt(l2regconst_extra)\n", "\n", "# len_params=len(list(net_star.parameters()))\n", "# list_no_bias=torch.zeros(len_params)\n", "# it=0\n", "# for name, p in net_star.named_parameters():\n", "#     if 'bias' not in name:\n", "#         list_no_bias[it]=1.0\n", "#     it+=1\n", "\n", "# def grad_vec(vec):   \n", "#     net_star_copy=copy.deepcopy(net_star)\n", "#     for it in range(len_params):\n", "#         list(net_star_copy.parameters())[it]=vec[it]\n", "#         list(net_star_copy.parameters())[it].requires_grad=True\n", "#     out=net_star_copy(images)\n", "#     loss=loss_function(out,labels)*train_data_len    \n", "#     net_star_copy.zero_grad()\n", "#     loss.backward()\n", "#     grad=[p.grad for p in net_star_copy.parameters()]\n", "#     # pars = list(net_star_copy.parameters())\n", "#     # out=net_star_copy(images)\n", "#     # loss=loss_function(out,labels)*train_data_len    \n", "#     # net_star_copy.zero_grad()\n", "#     # grad=list(torch.autograd.grad(loss,pars,create_graph=False))\n", "#     # with torch.no_grad():\n", "#     #     for it in range(len_params):\n", "#     #         if(list_no_bias[it]):\n", "#     #             grad[it]+=vec[it]*l2regconst\n", "#     return(grad)\n", "\n", "# def grad_vec_full(vec):\n", "#     gradfull=copy.deepcopy(list(vec))\n", "#     for p in gradfull:\n", "#         p.requires_grad=False\n", "#         p*=0\n", "#     len_pars=len(list(vec))\n", "#     for it in range(no_batches):\n", "#         images=images_list[it]\n", "#         labels=labels_list[it]\n", "#         grad=grad_vec(vec)\n", "#         for pit in range(len_pars):\n", "#             gradfull[pit]+=grad[pit]\n", "#     return(gradfull)\n", "\n", "def hess_vec(vec,images,labels):   \n", "    out=net_star(images)\n", "    loss=loss_function(out,labels)*batch_size\n", "    net_star.zero_grad()\n", "    grad=list(torch.autograd.grad(loss,list(net_star.parameters()),create_graph=True))\n", "    res=torch.zeros(1).cuda()\n", "    for it in range(len_params):\n", "        res+=(grad[it]*vec[it]).sum()\n", "    net_star.zero_grad()\n", "    hvp=list(torch.autograd.grad(res,list(net_star.parameters()),create_graph=False))\n", "\n", "    return(hvp)\n", "    \n", "    # hvp_with_prior=copy.deepcopy(hvp)\n", "    # l2regnewconst=torch.tensor(1e3)\n", "    # for p,v in zip(hvp_with_prior,vec):\n", "    #     p+=l2regnewconst*v\n", "    # return(hvp_with_prior)\n", "def hess_vec_full(vec):\n", "    hvfull=copy.deepcopy(list(vec))\n", "    for p in hvfull:\n", "        p.requires_grad=False\n", "        p*=0\n", "    len_pars=len(hvfull)\n", "    for it in range(no_batches):\n", "        images=images_list[it]\n", "        labels=labels_list[it]\n", "        hv=hess_vec(vec,images,labels)\n", "        for pit in range(len_pars):\n", "            hvfull[pit]+=hv[pit]\n", "    for pit in range(len_pars):\n", "        hvfull[pit]+=list(vec)[pit]*l2regconst_extra\n", "        if(list_no_bias[pit]):\n", "            hvfull[pit]+=list(vec)[pit]*l2regconst\n", "    return(hvfull)\n", "\n", "def norm_par(vec):\n", "    n=torch.zeros(1).detach().cuda()\n", "    for p in vec:\n", "        n+=p.pow(2).sum()\n", "    return n.sqrt()\n", "\n", "def multiply_par(vec,c):\n", "    res_vec=copy.deepcopy(vec)\n", "    for it in range(len_params):\n", "        res_vec[it].requires_grad=False\n", "        res_vec[it]=res_vec[it]*c\n", "    return res_vec\n", "\n", "def scalar_prod_par(vec1,vec2):\n", "    res=torch.zeros(1).cuda()\n", "    for it in range(len_params):\n", "        res+=(vec1[it]*vec2[it]).sum()\n", "    return res\n", "\n", "def add_par(vec1,vec2):\n", "    res_vec=copy.deepcopy(vec1)\n", "    for it in range(len_params):\n", "        res_vec[it].requires_grad=False\n", "        res_vec[it]=vec1[it]+vec2[it]\n", "    return res_vec\n", "\n", "# print(norm_par(list(net_star.parameters())))\n", "# print(norm_par(multiply_par(list(net_star.parameters()),0.1)))\n", "\n", "vec=copy.deepcopy(list(net_star.parameters()))\n", "for p in vec:\n", "    p.requires_grad=False\n", "norm_iter=norm_par(vec)\n", "vec=multiply_par(vec,torch.Tensor(1/norm_iter))\n", "for iter in range(10):\n", "    vec=hess_vec_full(vec)\n", "    norm_iter=norm_par(vec)\n", "    vec=multiply_par(vec,1/norm_iter)    \n", "    print(norm_iter)\n", "\n", "max_eigen_vec=copy.deepcopy(vec)\n", "max_eigen=norm_iter\n", "\n", "vec=copy.deepcopy(list(net_star.parameters()))\n", "for p in vec:\n", "    p.requires_grad=False\n", "vec=add_par(vec,multiply_par(max_eigen_vec,-scalar_prod_par(vec,max_eigen_vec)))\n", "norm_iter=norm_par(vec)\n", "vec=multiply_par(vec,torch.Tensor(1/norm_iter))\n", "\n", "for iter in range(10):\n", "    vec=hess_vec_full(vec)\n", "    vec=add_par(vec,multiply_par(max_eigen_vec,-scalar_prod_par(vec,max_eigen_vec)))\n", "    norm_iter=norm_par(vec)\n", "    vec=multiply_par(vec,1/norm_iter)    \n", "    print(norm_iter)\n", "\n", "\n", "# hvpf=hess_vec_full(vtest)\n", "# nhvp=0\n", "# for p in hvpf:\n", "#     nhvp+=p.abs().sum()\n", "# print(nhvp)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["#filepath=\"output_fashion.pickle\"\n", "#with open(filepath,\"rb\") as file:\n", "#    [labels_arr,test_labels_arr,test_prob_arr,_,_]=pickle.load(file)\n", "#labels_arr=torch.tensor(labels_arr).detach()\n", "#test_labels_arr=torch.tensor(test_labels_arr).detach()\n", "#test_prob_arr=torch.tensor(test_prob_arr).detach()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def rps_single(probs, true_label,num_classes):\n", "    outcome=torch.zeros(num_classes)\n", "    outcome[true_label.int()]=1.0\n", "    cum_probs = torch.cumsum(probs,0)\n", "    cum_outcomes = torch.cumsum(outcome,0)\n", "    \n", "    sum_rps = 0\n", "    for i in range(len(outcome)):         \n", "        sum_rps+= (cum_probs[i] - cum_outcomes[i])**2\n", "    \n", "    return sum_rps/(num_classes-1)\n", "\n", "def rps_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    rps_vec=torch.zeros(test_data_len)\n", "    for it in range(test_data_len):\n", "        rps_vec[it]=rps_single(test_probs[it,:].reshape(num_classes),true_labels[it],num_classes)\n", "    return rps_vec\n", "\n", "def nll_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    res=0\n", "    for it in range(test_data_len):\n", "        res-=torch.max(torch.tensor([torch.log(test_probs[it,true_labels[it].int()]),-100]))\n", "    return res/test_data_len\n", "\n", "\n", "def adaptive_calibration_error(test_probs,true_labels, test_data_len, num_classes,num_bins=20):\n", "    o=torch.tensor(0.0).detach()\n", "    for k in range(num_classes):\n", "        ind=torch.argsort(test_probs[:,k],stable=True)        \n", "        testprobsk=test_probs[:,k]\n", "        sorted_probs=testprobsk[ind]\n", "        sorted_true_labels=true_labels[ind]\n", "\n", "        true_label_is_k = (sorted_true_labels==k).clone().detach().float()\n", "        bins=(torch.tensor(range(test_data_len))/torch.tensor(test_data_len/num_bins)).floor()\n", "\n", "        for b in range(num_bins):\n", "            mask = (bins == b)\n", "            if torch.any(mask):\n", "                o += (true_label_is_k[mask] - sorted_probs[mask]).mean().abs()\n", "\n", "    return o / (num_bins*num_classes)\n", "\n", "\n", "def compute_acc_ace_rps_no_bayes(es,par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_swa_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Non-Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "def compute_acc_ace_rps_swa(es,par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_sampling_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"SWA, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "#Bayesian\n", "def compute_acc_ace_rps_bayes(es,par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr): \n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,(switch_to_sampling_epoch+burnin_epochs):num_epochs,it*es:(it+1)*es]).mean(-1).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Non-Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.8734) std: tensor(0.0045)\n", "mean ace: tensor(0.0150) std: tensor(0.0011)\n", "mean nll: tensor(0.5475) std: tensor(0.0352)\n", "mean rps: tensor(0.0387) std: tensor(0.0016)\n", "Non-Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.8946) std: tensor(0.0002)\n", "mean ace: tensor(0.0066) std: tensor(0.0007)\n", "mean nll: tensor(0.3702) std: tensor(0.0092)\n", "mean rps: tensor(0.0306) std: tensor(4.2489e-06)\n", "Non-Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9103) std: tensor(nan)\n", "mean ace: tensor(0.0049) std: tensor(nan)\n", "mean nll: tensor(0.2970) std: tensor(nan)\n", "mean rps: tensor(0.0263) std: tensor(nan)\n", "Non-Bayesian, ensemble size: 8\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Non-Bayesian, ensemble size: 16\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:60: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:61: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:62: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:63: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:64: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}], "source": ["#no bayesian\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_no_bayes(pow(2,it),par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SWA, ensemble size: 1\n", "mean accuracy: tensor(0.8943) std: tensor(0.0046)\n", "mean ace: tensor(0.0138) std: tensor(0.0007)\n", "mean nll: tensor(0.5620) std: tensor(0.0507)\n", "mean rps: tensor(0.0340) std: tensor(0.0016)\n", "SWA, ensemble size: 2\n", "mean accuracy: tensor(0.9081) std: tensor(0.0042)\n", "mean ace: tensor(0.0071) std: tensor(0.0008)\n", "mean nll: tensor(0.3831) std: tensor(0.0299)\n", "mean rps: tensor(0.0275) std: tensor(0.0014)\n", "SWA, ensemble size: 4\n", "mean accuracy: tensor(0.9163) std: tensor(nan)\n", "mean ace: tensor(0.0050) std: tensor(nan)\n", "mean nll: tensor(0.2980) std: tensor(nan)\n", "mean rps: tensor(0.0242) std: tensor(nan)\n", "SWA, ensemble size: 8\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "SWA, ensemble size: 16\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:81: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:82: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:83: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:84: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:85: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}], "source": ["acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_swa(pow(2,it),par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.8979) std: tensor(0.0020)\n", "mean ace: tensor(0.0054) std: tensor(0.0003)\n", "mean nll: tensor(0.3086) std: tensor(0.0061)\n", "mean rps: tensor(0.0285) std: tensor(0.0006)\n", "Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.9057) std: tensor(0.0019)\n", "mean ace: tensor(0.0071) std: tensor(8.8219e-06)\n", "mean nll: tensor(0.2909) std: tensor(0.0050)\n", "mean rps: tensor(0.0268) std: tensor(0.0003)\n", "Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9089) std: tensor(nan)\n", "mean ace: tensor(0.0083) std: tensor(nan)\n", "mean nll: tensor(0.2818) std: tensor(nan)\n", "mean rps: tensor(0.0259) std: tensor(nan)\n", "Bayesian, ensemble size: 8\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Bayesian, ensemble size: 16\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:103: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:104: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:105: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:106: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "/var/folders/8g/jjy0z_6j01s4z1xly3tn65zr0000gn/T/ipykernel_7926/3530991776.py:107: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1711403213615/work/aten/src/ATen/native/ReduceOps.cpp:1760.)\n", "  return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}], "source": ["#Bayesian\n", "burnin_epochs=5\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_bayes(pow(2,it),par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chain:  0 /epoch: 0\n", "NLL: tensor(0.3925)\n", "chain:  0 /epoch: 1\n", "NLL: tensor(0.4747)\n", "chain:  0 /epoch: 2\n", "NLL: tensor(0.4271)\n", "chain:  0 /epoch: 3\n", "NLL: tensor(0.3360)\n", "chain:  0 /epoch: 4\n", "NLL: tensor(0.2935)\n", "chain:  0 /epoch: 5\n", "NLL: tensor(0.3111)\n", "chain:  0 /epoch: 6\n", "NLL: tensor(0.2907)\n", "chain:  0 /epoch: 7\n", "NLL: tensor(0.2972)\n", "chain:  0 /epoch: 8\n", "NLL: tensor(0.2928)\n", "chain:  0 /epoch: 9\n", "NLL: tensor(0.3098)\n", "chain:  0 /epoch: 10\n", "NLL: tensor(0.2950)\n", "chain:  0 /epoch: 11\n", "NLL: tensor(0.3044)\n", "chain:  0 /epoch: 12\n", "NLL: tensor(0.3103)\n", "chain:  0 /epoch: 13\n", "NLL: tensor(0.3670)\n", "chain:  0 /epoch: 14\n", "NLL: tensor(0.3080)\n", "chain:  0 /epoch: 15\n", "NLL: tensor(0.3193)\n", "chain:  0 /epoch: 16\n", "NLL: tensor(0.3421)\n", "chain:  0 /epoch: 17\n", "NLL: tensor(0.2957)\n", "chain:  0 /epoch: 18\n", "NLL: tensor(0.3090)\n", "chain:  0 /epoch: 19\n", "NLL: tensor(0.3250)\n", "chain:  0 /epoch: 20\n", "NLL: tensor(0.3070)\n", "chain:  0 /epoch: 21\n", "NLL: tensor(0.2956)\n", "chain:  0 /epoch: 22\n", "NLL: tensor(0.3052)\n", "chain:  0 /epoch: 23\n", "NLL: tensor(0.3219)\n", "chain:  0 /epoch: 24\n", "NLL: tensor(0.3270)\n", "chain:  0 /epoch: 25\n", "NLL: tensor(0.3110)\n", "chain:  0 /epoch: 26\n", "NLL: tensor(0.2939)\n", "chain:  0 /epoch: 27\n", "NLL: tensor(0.2907)\n", "chain:  0 /epoch: 28\n", "NLL: tensor(0.2879)\n", "chain:  0 /epoch: 29\n", "NLL: tensor(0.3057)\n", "chain:  0 /epoch: 30\n", "NLL: tensor(0.3204)\n", "chain:  0 /epoch: 31\n", "NLL: tensor(0.3001)\n", "chain:  0 /epoch: 32\n", "NLL: tensor(0.2755)\n", "chain:  0 /epoch: 33\n", "NLL: tensor(0.2904)\n", "chain:  0 /epoch: 34\n", "NLL: tensor(0.3180)\n", "chain:  0 /epoch: 35\n", "NLL: tensor(0.3047)\n", "chain:  0 /epoch: 36\n", "NLL: tensor(0.2775)\n", "chain:  0 /epoch: 37\n", "NLL: tensor(0.3241)\n", "chain:  0 /epoch: 38\n", "NLL: tensor(0.3131)\n", "chain:  0 /epoch: 39\n", "NLL: tensor(0.3243)\n", "chain:  1 /epoch: 0\n", "NLL: tensor(0.3973)\n", "chain:  1 /epoch: 1\n", "NLL: tensor(0.4840)\n", "chain:  1 /epoch: 2\n", "NLL: tensor(0.3927)\n", "chain:  1 /epoch: 3\n", "NLL: tensor(0.3250)\n", "chain:  1 /epoch: 4\n", "NLL: tensor(0.3070)\n", "chain:  1 /epoch: 5\n", "NLL: tensor(0.2899)\n", "chain:  1 /epoch: 6\n", "NLL: tensor(0.2719)\n", "chain:  1 /epoch: 7\n", "NLL: tensor(0.2889)\n", "chain:  1 /epoch: 8\n", "NLL: tensor(0.2912)\n", "chain:  1 /epoch: 9\n", "NLL: tensor(0.3269)\n", "chain:  1 /epoch: 10\n", "NLL: tensor(0.2880)\n", "chain:  1 /epoch: 11\n", "NLL: tensor(0.2835)\n", "chain:  1 /epoch: 12\n", "NLL: tensor(0.3406)\n", "chain:  1 /epoch: 13\n", "NLL: tensor(0.3049)\n", "chain:  1 /epoch: 14\n", "NLL: tensor(0.3108)\n", "chain:  1 /epoch: 15\n", "NLL: tensor(0.3079)\n", "chain:  1 /epoch: 16\n", "NLL: tensor(0.2874)\n", "chain:  1 /epoch: 17\n", "NLL: tensor(0.2836)\n", "chain:  1 /epoch: 18\n", "NLL: tensor(0.2841)\n", "chain:  1 /epoch: 19\n", "NLL: tensor(0.2761)\n", "chain:  1 /epoch: 20\n", "NLL: tensor(0.2922)\n", "chain:  1 /epoch: 21\n", "NLL: tensor(0.3422)\n", "chain:  1 /epoch: 22\n", "NLL: tensor(0.2898)\n", "chain:  1 /epoch: 23\n", "NLL: tensor(0.3360)\n", "chain:  1 /epoch: 24\n", "NLL: tensor(0.2738)\n", "chain:  1 /epoch: 25\n", "NLL: tensor(0.3107)\n", "chain:  1 /epoch: 26\n", "NLL: tensor(0.2838)\n", "chain:  1 /epoch: 27\n", "NLL: tensor(0.3114)\n", "chain:  1 /epoch: 28\n", "NLL: tensor(0.2853)\n", "chain:  1 /epoch: 29\n", "NLL: tensor(0.3351)\n", "chain:  1 /epoch: 30\n", "NLL: tensor(0.3007)\n", "chain:  1 /epoch: 31\n", "NLL: tensor(0.3034)\n", "chain:  1 /epoch: 32\n", "NLL: tensor(0.3095)\n", "chain:  1 /epoch: 33\n", "NLL: tensor(0.3517)\n", "chain:  1 /epoch: 34\n", "NLL: tensor(0.3104)\n", "chain:  1 /epoch: 35\n", "NLL: tensor(0.2936)\n", "chain:  1 /epoch: 36\n", "NLL: tensor(0.2830)\n", "chain:  1 /epoch: 37\n", "NLL: tensor(0.3254)\n", "chain:  1 /epoch: 38\n", "NLL: tensor(0.2974)\n", "chain:  1 /epoch: 39\n", "NLL: tensor(0.3165)\n", "chain:  2 /epoch: 0\n", "NLL: tensor(0.4106)\n", "chain:  2 /epoch: 1\n", "NLL: tensor(0.4778)\n", "chain:  2 /epoch: 2\n", "NLL: tensor(0.4715)\n", "chain:  2 /epoch: 3\n", "NLL: tensor(0.3729)\n", "chain:  2 /epoch: 4\n", "NLL: tensor(0.3136)\n", "chain:  2 /epoch: 5\n", "NLL: tensor(0.3075)\n", "chain:  2 /epoch: 6\n", "NLL: tensor(0.2825)\n", "chain:  2 /epoch: 7\n", "NLL: tensor(0.3087)\n", "chain:  2 /epoch: 8\n", "NLL: tensor(0.3109)\n", "chain:  2 /epoch: 9\n", "NLL: tensor(0.2933)\n", "chain:  2 /epoch: 10\n", "NLL: tensor(0.3065)\n", "chain:  2 /epoch: 11\n", "NLL: tensor(0.3094)\n", "chain:  2 /epoch: 12\n", "NLL: tensor(0.3148)\n", "chain:  2 /epoch: 13\n", "NLL: tensor(0.3220)\n", "chain:  2 /epoch: 14\n", "NLL: tensor(0.3105)\n", "chain:  2 /epoch: 15\n", "NLL: tensor(0.3091)\n", "chain:  2 /epoch: 16\n", "NLL: tensor(0.3106)\n", "chain:  2 /epoch: 17\n", "NLL: tensor(0.3072)\n", "chain:  2 /epoch: 18\n", "NLL: tensor(0.2891)\n", "chain:  2 /epoch: 19\n", "NLL: tensor(0.3079)\n", "chain:  2 /epoch: 20\n", "NLL: tensor(0.2813)\n", "chain:  2 /epoch: 21\n", "NLL: tensor(0.3171)\n", "chain:  2 /epoch: 22\n", "NLL: tensor(0.3306)\n", "chain:  2 /epoch: 23\n", "NLL: tensor(0.2943)\n", "chain:  2 /epoch: 24\n", "NLL: tensor(0.2783)\n", "chain:  2 /epoch: 25\n", "NLL: tensor(0.3132)\n", "chain:  2 /epoch: 26\n", "NLL: tensor(0.2523)\n", "chain:  2 /epoch: 27\n", "NLL: tensor(0.3163)\n", "chain:  2 /epoch: 28\n", "NLL: tensor(0.2973)\n", "chain:  2 /epoch: 29\n", "NLL: tensor(0.3094)\n", "chain:  2 /epoch: 30\n", "NLL: tensor(0.3063)\n", "chain:  2 /epoch: 31\n", "NLL: tensor(0.2916)\n", "chain:  2 /epoch: 32\n", "NLL: tensor(0.2926)\n", "chain:  2 /epoch: 33\n", "NLL: tensor(0.3222)\n", "chain:  2 /epoch: 34\n", "NLL: tensor(0.2772)\n", "chain:  2 /epoch: 35\n", "NLL: tensor(0.3255)\n", "chain:  2 /epoch: 36\n", "NLL: tensor(0.2922)\n", "chain:  2 /epoch: 37\n", "NLL: tensor(0.2813)\n", "chain:  2 /epoch: 38\n", "NLL: tensor(0.2899)\n", "chain:  2 /epoch: 39\n", "NLL: tensor(0.3027)\n", "chain:  3 /epoch: 0\n", "NLL: tensor(0.3835)\n", "chain:  3 /epoch: 1\n", "NLL: tensor(0.4546)\n", "chain:  3 /epoch: 2\n", "NLL: tensor(0.4224)\n", "chain:  3 /epoch: 3\n", "NLL: tensor(0.3178)\n", "chain:  3 /epoch: 4\n", "NLL: tensor(0.3049)\n", "chain:  3 /epoch: 5\n", "NLL: tensor(0.2976)\n", "chain:  3 /epoch: 6\n", "NLL: tensor(0.3153)\n", "chain:  3 /epoch: 7\n", "NLL: tensor(0.3228)\n", "chain:  3 /epoch: 8\n", "NLL: tensor(0.2887)\n", "chain:  3 /epoch: 9\n", "NLL: tensor(0.2946)\n", "chain:  3 /epoch: 10\n", "NLL: tensor(0.3221)\n", "chain:  3 /epoch: 11\n", "NLL: tensor(0.3399)\n", "chain:  3 /epoch: 12\n", "NLL: tensor(0.3124)\n", "chain:  3 /epoch: 13\n", "NLL: tensor(0.3286)\n", "chain:  3 /epoch: 14\n", "NLL: tensor(0.3156)\n", "chain:  3 /epoch: 15\n", "NLL: tensor(0.2945)\n", "chain:  3 /epoch: 16\n", "NLL: tensor(0.3040)\n", "chain:  3 /epoch: 17\n", "NLL: tensor(0.3215)\n", "chain:  3 /epoch: 18\n", "NLL: tensor(0.2982)\n", "chain:  3 /epoch: 19\n", "NLL: tensor(0.3249)\n", "chain:  3 /epoch: 20\n", "NLL: tensor(0.2636)\n", "chain:  3 /epoch: 21\n", "NLL: tensor(0.3388)\n", "chain:  3 /epoch: 22\n", "NLL: tensor(0.2906)\n", "chain:  3 /epoch: 23\n", "NLL: tensor(0.3284)\n", "chain:  3 /epoch: 24\n", "NLL: tensor(0.3186)\n", "chain:  3 /epoch: 25\n", "NLL: tensor(0.3245)\n", "chain:  3 /epoch: 26\n", "NLL: tensor(0.3290)\n", "chain:  3 /epoch: 27\n", "NLL: tensor(0.2993)\n", "chain:  3 /epoch: 28\n", "NLL: tensor(0.3038)\n", "chain:  3 /epoch: 29\n", "NLL: tensor(0.3101)\n", "chain:  3 /epoch: 30\n", "NLL: tensor(0.3287)\n", "chain:  3 /epoch: 31\n", "NLL: tensor(0.2987)\n", "chain:  3 /epoch: 32\n", "NLL: tensor(0.3053)\n", "chain:  3 /epoch: 33\n", "NLL: tensor(0.2929)\n", "chain:  3 /epoch: 34\n", "NLL: tensor(0.2737)\n", "chain:  3 /epoch: 35\n", "NLL: tensor(0.3095)\n", "chain:  3 /epoch: 36\n", "NLL: tensor(0.2810)\n", "chain:  3 /epoch: 37\n", "NLL: tensor(0.2922)\n", "chain:  3 /epoch: 38\n", "NLL: tensor(0.3069)\n", "chain:  3 /epoch: 39\n", "NLL: tensor(0.2972)\n", "tensor(0.9780)\n"]}], "source": ["def GRdiagnostics(res):\n", "  J=res.shape[0] #Number of chains\n", "  L=res.shape[1] #Number of samples after burnin\n", "  res_means=res.mean(dim=1)\n", "  res_mean=res_means.mean()\n", "  B=(res_means-res_mean).pow(2).sum()*L/(J-1)\n", "  W=(res_means.reshape([J,1])@torch.ones([1,L])-res).pow(2).sum()/(J*(L-1))\n", "  R=(W*(L-1)/L+B/L)/W\n", "  return R\n", "\n", "\n", "par_chains=4\n", "no_GR_epochs=40\n", "test_prob_GR_arr=torch.zeros([test_size,num_classes])\n", "nll_GR_arr=torch.zeros([par_chains,no_GR_epochs])\n", "for chain in range(par_chains):\n", "    net=copy.deepcopy(net_star)\n", "    net.eval()\n", "    for par in list(net.parameters()):\n", "      par.v = torch.randn_like(par,device=device)          \n", "    for epoch in range(no_GR_epochs):\n", "      print(\"chain: \",chain, \"/epoch:\",epoch)\n", "      if(epoch % 2 == 1):\n", "        irange=range(no_batches-1,-1,-1)\n", "      else:\n", "        irange=range(no_batches)\n", "      for b in irange:\n", "        images=images_list[b]\n", "        labels=labels_list[b]\n", "        UBU_step(hper2c,images,labels,b)\n", "\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        outputt = net(imagest).detach()\n", "        test_prob_GR_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:]=torch.softmax(outputt,dim=1)\n", "      \n", "      nll_GR_arr[chain,epoch]=nll_calc(test_prob_GR_arr,test_labels_arr)\n", "      print(\"NLL:\", nll_GR_arr[chain,epoch])\n", "\n", "print(GRdiagnostics(nll_GR_arr))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["#test_prob=torch.Tensor(test_prob_arr[:,:,29,0]).reshape(test_size,num_classes)\n", "#torch.cumsum(test_prob[1,:].reshape(num_classes),0)\n", "#rps_single(test_prob[1,:].reshape(num_classes),test_labels_arr[1])\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Non-Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.8809) std: tensor(nan)\n", "mean ace: tensor(0.0679) std: tensor(nan)\n", "mean nll: tensor(0.4752) std: tensor(nan)\n", "mean rps: tensor(0.0357) std: tensor(nan)\n", "Non-Bayesian, ensemble size: 2\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Non-Bayesian, ensemble size: 4\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Non-Bayesian, ensemble size: 8\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Non-Bayesian, ensemble size: 16\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<ipython-input-16-5dc5e541dd1c>:19: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "<ipython-input-16-5dc5e541dd1c>:20: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "<ipython-input-16-5dc5e541dd1c>:21: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "<ipython-input-16-5dc5e541dd1c>:22: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "<ipython-input-16-5dc5e541dd1c>:23: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}], "source": ["#no bayesian\n", "par_runs=1\n", "def compute_acc_ace_rps_no_bayes(es):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        #test_prob=torch.Tensor(test_prob_arr[:,:,14,it*es:(it+1)*es]).mean(-1).reshape(test_size,num_classes)\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,21,it*es:(it+1)*es]).mean(-1).reshape(test_size,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "    print(\"Non-Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "acc=torch.zeros(5)\n", "acc_std=torch.zeros(5)\n", "ace=torch.zeros(5)\n", "ace_std=torch.zeros(5)\n", "rps=torch.zeros(5)\n", "rps_std=torch.zeros(5)\n", "nll=torch.zeros(5)\n", "nll_std=torch.zeros(5)\n", "[acc[0],acc_std[0],ace[0],ace_std[0],rps[0],rps_std[0],nll[0],nll_std[0]]=compute_acc_ace_rps_no_bayes(1)\n", "[acc[1],acc_std[1],ace[1],ace_std[1],rps[1],rps_std[1],nll[1],nll_std[1]]=compute_acc_ace_rps_no_bayes(2)\n", "[acc[2],acc_std[2],ace[2],ace_std[2],rps[2],rps_std[2],nll[2],nll_std[2]]=compute_acc_ace_rps_no_bayes(4)\n", "[acc[3],acc_std[3],ace[3],ace_std[3],rps[3],rps_std[3],nll[3],nll_std[3]]=compute_acc_ace_rps_no_bayes(8)\n", "[acc[4],acc_std[4],ace[4],ace_std[4],rps[4],rps_std[4],nll[4],nll_std[4]]=compute_acc_ace_rps_no_bayes(16)\n", "\n", "# from scipy.io import savemat\n", "# filepath=\"results_fashion_no_bayes_rand_test.mat\"\n", "# mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "#       \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "# savemat(filepath,mdic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Non-Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9296) std: tensor(0.0020)\n", "mean ace: tensor(0.0555) std: tensor(0.0018)\n", "mean nll: tensor(0.4709) std: tensor(0.0251)\n", "mean rps: tensor(0.0230) std: tensor(0.0008)\n", "Non-Bayesian, ensemble size: 2\n", "mean accuracy: tensor(0.9383) std: tensor(0.0015)\n", "mean ace: tensor(0.0294) std: tensor(0.0016)\n", "mean nll: tensor(0.3109) std: tensor(0.0127)\n", "mean rps: tensor(0.0188) std: tensor(0.0005)\n", "Non-Bayesian, ensemble size: 4\n", "mean accuracy: tensor(0.9422) std: tensor(0.0010)\n", "mean ace: tensor(0.0177) std: tensor(0.0011)\n", "mean nll: tensor(0.2385) std: tensor(0.0066)\n", "mean rps: tensor(0.0168) std: tensor(0.0003)\n", "Non-Bayesian, ensemble size: 8\n", "mean accuracy: tensor(0.9443) std: tensor(0.0006)\n", "mean ace: tensor(0.0126) std: tensor(0.0007)\n", "mean nll: tensor(0.2025) std: tensor(0.0042)\n", "mean rps: tensor(0.0157) std: tensor(0.0002)\n", "Non-Bayesian, ensemble size: 16\n", "mean accuracy: tensor(0.9459) std: tensor(0.0009)\n", "mean ace: tensor(0.0098) std: tensor(0.0007)\n", "mean nll: tensor(0.1832) std: tensor(0.0029)\n", "mean rps: tensor(0.0152) std: tensor(8.2395e-05)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SWA, ensemble size: 1\n", "mean accuracy: tensor(0.8966) std: tensor(nan)\n", "mean ace: tensor(0.0718) std: tensor(nan)\n", "mean nll: tensor(0.5155) std: tensor(nan)\n", "mean rps: tensor(0.0329) std: tensor(nan)\n", "SWA, ensemble size: 2\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "SWA, ensemble size: 4\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "SWA, ensemble size: 8\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "SWA, ensemble size: 16\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<ipython-input-17-f630c4582375>:18: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "<ipython-input-17-f630c4582375>:19: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "<ipython-input-17-f630c4582375>:20: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "<ipython-input-17-f630c4582375>:21: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "<ipython-input-17-f630c4582375>:22: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}], "source": ["#swa\n", "def compute_acc_ace_rps_swa(es):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        #test_prob=torch.Tensor(test_prob_arr[:,:,19,it*es:(it+1)*es]).mean(-1).reshape(test_size,num_classes)\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,29,it*es:(it+1)*es]).mean(-1).reshape(test_size,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "    print(\"SWA, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "acc=torch.zeros(5)\n", "acc_std=torch.zeros(5)\n", "ace=torch.zeros(5)\n", "ace_std=torch.zeros(5)\n", "rps=torch.zeros(5)\n", "rps_std=torch.zeros(5)\n", "nll=torch.zeros(5)\n", "nll_std=torch.zeros(5)\n", "[acc[0],acc_std[0],ace[0],ace_std[0],rps[0],rps_std[0],nll[0],nll_std[0]]=compute_acc_ace_rps_swa(1)\n", "[acc[1],acc_std[1],ace[1],ace_std[1],rps[1],rps_std[1],nll[1],nll_std[1]]=compute_acc_ace_rps_swa(2)\n", "[acc[2],acc_std[2],ace[2],ace_std[2],rps[2],rps_std[2],nll[2],nll_std[2]]=compute_acc_ace_rps_swa(4)\n", "[acc[3],acc_std[3],ace[3],ace_std[3],rps[3],rps_std[3],nll[3],nll_std[3]]=compute_acc_ace_rps_swa(8)\n", "[acc[4],acc_std[4],ace[4],ace_std[4],rps[4],rps_std[4],nll[4],nll_std[4]]=compute_acc_ace_rps_swa(16)\n", "\n", "# from scipy.io import savemat\n", "# filepath=\"results_fashion_swa_rand_test.mat\"\n", "# mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "#       \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "# savemat(filepath,mdic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["SWA, ensemble size: 1\n", "mean accuracy: tensor(0.9337) std: tensor(0.0016)\n", "mean ace: tensor(0.0537) std: tensor(0.0016)\n", "mean nll: tensor(0.4826) std: tensor(0.0191)\n", "mean rps: tensor(0.0220) std: tensor(0.0006)\n", "SWA, ensemble size: 2\n", "mean accuracy: tensor(0.9403) std: tensor(0.0012)\n", "mean ace: tensor(0.0309) std: tensor(0.0012)\n", "mean nll: tensor(0.3312) std: tensor(0.0111)\n", "mean rps: tensor(0.0184) std: tensor(0.0004)\n", "SWA, ensemble size: 4\n", "mean accuracy: tensor(0.9435) std: tensor(0.0011)\n", "mean ace: tensor(0.0223) std: tensor(0.0010)\n", "mean nll: tensor(0.2579) std: tensor(0.0078)\n", "mean rps: tensor(0.0166) std: tensor(0.0003)\n", "SWA, ensemble size: 8\n", "mean accuracy: tensor(0.9453) std: tensor(0.0008)\n", "mean ace: tensor(0.0180) std: tensor(0.0011)\n", "mean nll: tensor(0.2186) std: tensor(0.0036)\n", "mean rps: tensor(0.0157) std: tensor(0.0002)\n", "SWA, ensemble size: 16\n", "mean accuracy: tensor(0.9462) std: tensor(0.0008)\n", "mean ace: tensor(0.0158) std: tensor(0.0009)\n", "mean nll: tensor(0.1964) std: tensor(0.0034)\n", "mean rps: tensor(0.0153) std: tensor(5.5335e-05)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.8990) std: tensor(nan)\n", "mean ace: tensor(0.0168) std: tensor(nan)\n", "mean nll: tensor(0.3330) std: tensor(nan)\n", "mean rps: tensor(0.0287) std: tensor(nan)\n", "Bayesian, ensemble size: 2\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Bayesian, ensemble size: 4\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Bayesian, ensemble size: 8\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n", "Bayesian, ensemble size: 16\n", "mean accuracy: tensor(nan) std: tensor(nan)\n", "mean ace: tensor(nan) std: tensor(nan)\n", "mean nll: tensor(nan) std: tensor(nan)\n", "mean rps: tensor(nan) std: tensor(nan)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<ipython-input-19-24c8d846d4dd>:17: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "<ipython-input-19-24c8d846d4dd>:18: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "<ipython-input-19-24c8d846d4dd>:19: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "<ipython-input-19-24c8d846d4dd>:20: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "<ipython-input-19-24c8d846d4dd>:21: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at C:\\cb\\pytorch_1000000000000\\work\\aten\\src\\ATen\\native\\ReduceOps.cpp:1807.)\n", "  return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}], "source": ["#Bayesian\n", "def compute_acc_ace_rps_bayes(es):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,30:48,it*es:(it+1)*es]).mean(-1).mean(-1).reshape(test_size,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "    print(\"Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "\n", "\n", "acc=torch.zeros(5)\n", "acc_std=torch.zeros(5)\n", "ace=torch.zeros(5)\n", "ace_std=torch.zeros(5)\n", "rps=torch.zeros(5)\n", "rps_std=torch.zeros(5)\n", "nll=torch.zeros(5)\n", "nll_std=torch.zeros(5)\n", "[acc[0],acc_std[0],ace[0],ace_std[0],rps[0],rps_std[0],nll[0],nll_std[0]]=compute_acc_ace_rps_bayes(1)\n", "[acc[1],acc_std[1],ace[1],ace_std[1],rps[1],rps_std[1],nll[1],nll_std[1]]=compute_acc_ace_rps_bayes(2)\n", "[acc[2],acc_std[2],ace[2],ace_std[2],rps[2],rps_std[2],nll[2],nll_std[2]]=compute_acc_ace_rps_bayes(4)\n", "[acc[3],acc_std[3],ace[3],ace_std[3],rps[3],rps_std[3],nll[3],nll_std[3]]=compute_acc_ace_rps_bayes(8)\n", "[acc[4],acc_std[4],ace[4],ace_std[4],rps[4],rps_std[4],nll[4],nll_std[4]]=compute_acc_ace_rps_bayes(16)\n", "\n", "# from scipy.io import savemat\n", "# filepath=\"results_fashion_bayes_rand_test.mat\"\n", "# mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "#       \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "# savemat(filepath,mdic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9358) std: tensor(nan)\n", "mean ace: tensor(0.0123) std: tensor(nan)\n", "mean nll: tensor(0.2039) std: tensor(nan)\n", "mean rps: tensor(0.0177) std: tensor(nan)\n", "\n", "Bayesian, ensemble size: 1\n", "mean accuracy: tensor(0.9400) std: tensor(nan)\n", "mean ace: tensor(0.0072) std: tensor(nan)\n", "mean nll: tensor(0.1876) std: tensor(nan)\n", "mean rps: tensor(0.0169) std: tensor(nan)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# test_prob=torch.Tensor(test_prob_arr[:,7,0]).reshape(test_size,1)\n", "# ace=adaptive_calibration_error(test_labels_arr.reshape(test_size,1).numpy(),test_prob.numpy(),20)\n", "# ace"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# test_prob=torch.Tensor(test_prob_arr[:,10:16,48:64]).mean(-1).mean(-1).reshape(test_size,1)\n", "# ace=adaptive_calibration_error(test_labels_arr.reshape(test_size,1).numpy(),test_prob.numpy(),20)\n", "# ace"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning: Could not load \"C:\\Users\\<USER>\\.conda\\envs\\torchenv39\\Library\\bin\\gvplugin_pango.dll\" - It was found, so perhaps one of its dependents was not.  Try ldd.\n", "Warning: no hard-coded metrics for 'Linux libertine'.  Falling back to 'Times' metrics\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 8.1.0 (0)\n", " -->\n", "<!-- Title: model Pages: 1 -->\n", "<svg width=\"127pt\" height=\"2894pt\"\n", " viewBox=\"0.00 0.00 126.55 2894.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(0.575211 0.575211) rotate(0) translate(4 5027.2)\">\n", "<title>model</title>\n", "<polygon fill=\"white\" stroke=\"none\" points=\"-4,4 -4,-5027.2 216,-5027.2 216,4 -4,4\"/>\n", "<g id=\"clust1\" class=\"cluster\">\n", "<title>cluster_2</title>\n", "<polygon fill=\"none\" stroke=\"black\" stroke-dasharray=\"5,2\" points=\"8,-1218.8 8,-4981.2 204,-4981.2 204,-1218.8 8,-1218.8\"/>\n", "<text text-anchor=\"middle\" x=\"41.33\" y=\"-4966.4\" font-family=\"Times New Roman,serif\" font-size=\"12.00\">Sequential</text>\n", "</g>\n", "<g id=\"clust2\" class=\"cluster\">\n", "<title>cluster_3</title>\n", "<polygon fill=\"none\" stroke=\"black\" stroke-dasharray=\"5,2\" points=\"18,-168.4 18,-1210.8 194,-1210.8 194,-168.4 18,-168.4\"/>\n", "<text text-anchor=\"middle\" x=\"51.33\" y=\"-1196\" font-family=\"Times New Roman,serif\" font-size=\"12.00\">Sequential</text>\n", "</g>\n", "<g id=\"clust3\" class=\"cluster\">\n", "<title>cluster_4</title>\n", "<polygon fill=\"none\" stroke=\"black\" stroke-dasharray=\"5,2\" points=\"34,-8 34,-160.4 178,-160.4 178,-8 34,-8\"/>\n", "<text text-anchor=\"middle\" x=\"67.33\" y=\"-145.6\" font-family=\"Times New Roman,serif\" font-size=\"12.00\">Sequential</text>\n", "</g>\n", "<!-- 0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>0</title>\n", "<polygon fill=\"lightyellow\" stroke=\"none\" points=\"165.99,-5023.2 46.01,-5023.2 46.01,-4989.2 165.99,-4989.2 165.99,-5023.2\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"46.01,-4989.2 46.01,-5023.2 104.33,-5023.2 104.33,-4989.2 46.01,-4989.2\"/>\n", "<text text-anchor=\"start\" x=\"51.01\" y=\"-5009.2\" font-family=\"Linux libertine\" font-size=\"10.00\">input&#45;tensor</text>\n", "<text text-anchor=\"start\" x=\"60.17\" y=\"-4997.2\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:0</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"104.33,-4989.2 104.33,-5023.2 165.99,-5023.2 165.99,-4989.2 104.33,-4989.2\"/>\n", "<text text-anchor=\"start\" x=\"109.33\" y=\"-5003.2\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 3, 32, 32)</text>\n", "</g>\n", "<!-- 1 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>1</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-4950.8 28.5,-4950.8 28.5,-4906.8 183.5,-4906.8 183.5,-4950.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-4906.8 28.5,-4950.8 69.5,-4950.8 69.5,-4906.8 28.5,-4906.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-4931.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4919.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4928.8 69.5,-4950.8 109.5,-4950.8 109.5,-4928.8 69.5,-4928.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-4936.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4928.8 109.5,-4950.8 183.5,-4950.8 183.5,-4928.8 109.5,-4928.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-4936.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 3, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4906.8 69.5,-4928.8 109.5,-4928.8 109.5,-4906.8 69.5,-4906.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-4914.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4906.8 109.5,-4928.8 183.5,-4928.8 183.5,-4906.8 109.5,-4906.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4914.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 0&#45;&gt;1 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>0&#45;&gt;1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4989.21C106,-4981.13 106,-4971.04 106,-4961.47\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4961.64 106,-4951.64 102.5,-4961.64 109.5,-4961.64\"/>\n", "</g>\n", "<!-- 2 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>2</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-4870.8 27.5,-4870.8 27.5,-4826.8 184.5,-4826.8 184.5,-4870.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-4826.8 27.5,-4870.8 70.5,-4870.8 70.5,-4826.8 27.5,-4826.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-4851.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4839.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4848.8 70.5,-4870.8 110.5,-4870.8 110.5,-4848.8 70.5,-4848.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-4856.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4848.8 110.5,-4870.8 184.5,-4870.8 184.5,-4848.8 110.5,-4848.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4856.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4826.8 70.5,-4848.8 110.5,-4848.8 110.5,-4826.8 70.5,-4826.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-4834.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4826.8 110.5,-4848.8 184.5,-4848.8 184.5,-4826.8 110.5,-4826.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4834.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 1&#45;&gt;2 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>1&#45;&gt;2</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4906.9C106,-4899.12 106,-4890.1 106,-4881.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4881.76 106,-4871.76 102.5,-4881.76 109.5,-4881.76\"/>\n", "</g>\n", "<!-- 3 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>3</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-4790.8 16,-4790.8 16,-4746.8 196,-4746.8 196,-4790.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-4746.8 16,-4790.8 82,-4790.8 82,-4746.8 16,-4746.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-4771.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4759.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4768.8 82,-4790.8 122,-4790.8 122,-4768.8 82,-4768.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-4776.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4768.8 122,-4790.8 196,-4790.8 196,-4768.8 122,-4768.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4776.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4746.8 82,-4768.8 122,-4768.8 122,-4746.8 82,-4746.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-4754.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4746.8 122,-4768.8 196,-4768.8 196,-4746.8 122,-4746.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4754.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 2&#45;&gt;3 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>2&#45;&gt;3</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4826.9C106,-4819.12 106,-4810.1 106,-4801.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4801.76 106,-4791.76 102.5,-4801.76 109.5,-4801.76\"/>\n", "</g>\n", "<!-- 4 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>4</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-4710.8 28.5,-4710.8 28.5,-4666.8 183.5,-4666.8 183.5,-4710.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-4666.8 28.5,-4710.8 69.5,-4710.8 69.5,-4666.8 28.5,-4666.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-4691.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4679.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4688.8 69.5,-4710.8 109.5,-4710.8 109.5,-4688.8 69.5,-4688.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-4696.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4688.8 109.5,-4710.8 183.5,-4710.8 183.5,-4688.8 109.5,-4688.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4696.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4666.8 69.5,-4688.8 109.5,-4688.8 109.5,-4666.8 69.5,-4666.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-4674.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4666.8 109.5,-4688.8 183.5,-4688.8 183.5,-4666.8 109.5,-4666.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4674.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 3&#45;&gt;4 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>3&#45;&gt;4</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4746.9C106,-4739.12 106,-4730.1 106,-4721.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4721.76 106,-4711.76 102.5,-4721.76 109.5,-4721.76\"/>\n", "</g>\n", "<!-- 5 -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>5</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-4630.8 27.5,-4630.8 27.5,-4586.8 184.5,-4586.8 184.5,-4630.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-4586.8 27.5,-4630.8 70.5,-4630.8 70.5,-4586.8 27.5,-4586.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-4611.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4599.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4608.8 70.5,-4630.8 110.5,-4630.8 110.5,-4608.8 70.5,-4608.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-4616.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4608.8 110.5,-4630.8 184.5,-4630.8 184.5,-4608.8 110.5,-4608.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4616.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4586.8 70.5,-4608.8 110.5,-4608.8 110.5,-4586.8 70.5,-4586.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-4594.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4586.8 110.5,-4608.8 184.5,-4608.8 184.5,-4586.8 110.5,-4586.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4594.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 4&#45;&gt;5 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>4&#45;&gt;5</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4666.9C106,-4659.12 106,-4650.1 106,-4641.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4641.76 106,-4631.76 102.5,-4641.76 109.5,-4641.76\"/>\n", "</g>\n", "<!-- 6 -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>6</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-4550.8 16,-4550.8 16,-4506.8 196,-4506.8 196,-4550.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-4506.8 16,-4550.8 82,-4550.8 82,-4506.8 16,-4506.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-4531.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4519.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4528.8 82,-4550.8 122,-4550.8 122,-4528.8 82,-4528.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-4536.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4528.8 122,-4550.8 196,-4550.8 196,-4528.8 122,-4528.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4536.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4506.8 82,-4528.8 122,-4528.8 122,-4506.8 82,-4506.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-4514.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4506.8 122,-4528.8 196,-4528.8 196,-4506.8 122,-4506.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4514.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 5&#45;&gt;6 -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>5&#45;&gt;6</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4586.9C106,-4579.12 106,-4570.1 106,-4561.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4561.76 106,-4551.76 102.5,-4561.76 109.5,-4561.76\"/>\n", "</g>\n", "<!-- 7 -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>7</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-4470.8 28.5,-4470.8 28.5,-4426.8 183.5,-4426.8 183.5,-4470.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-4426.8 28.5,-4470.8 69.5,-4470.8 69.5,-4426.8 28.5,-4426.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-4451.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4439.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4448.8 69.5,-4470.8 109.5,-4470.8 109.5,-4448.8 69.5,-4448.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-4456.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4448.8 109.5,-4470.8 183.5,-4470.8 183.5,-4448.8 109.5,-4448.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4456.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4426.8 69.5,-4448.8 109.5,-4448.8 109.5,-4426.8 69.5,-4426.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-4434.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4426.8 109.5,-4448.8 183.5,-4448.8 183.5,-4426.8 109.5,-4426.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4434.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 6&#45;&gt;7 -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>6&#45;&gt;7</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4506.9C106,-4499.12 106,-4490.1 106,-4481.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4481.76 106,-4471.76 102.5,-4481.76 109.5,-4481.76\"/>\n", "</g>\n", "<!-- 8 -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>8</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-4390.8 27.5,-4390.8 27.5,-4346.8 184.5,-4346.8 184.5,-4390.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-4346.8 27.5,-4390.8 70.5,-4390.8 70.5,-4346.8 27.5,-4346.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-4371.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4359.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4368.8 70.5,-4390.8 110.5,-4390.8 110.5,-4368.8 70.5,-4368.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-4376.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4368.8 110.5,-4390.8 184.5,-4390.8 184.5,-4368.8 110.5,-4368.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4376.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4346.8 70.5,-4368.8 110.5,-4368.8 110.5,-4346.8 70.5,-4346.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-4354.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4346.8 110.5,-4368.8 184.5,-4368.8 184.5,-4346.8 110.5,-4346.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4354.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 7&#45;&gt;8 -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>7&#45;&gt;8</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4426.9C106,-4419.12 106,-4410.1 106,-4401.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4401.76 106,-4391.76 102.5,-4401.76 109.5,-4401.76\"/>\n", "</g>\n", "<!-- 9 -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>9</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-4310.8 16,-4310.8 16,-4266.8 196,-4266.8 196,-4310.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-4266.8 16,-4310.8 82,-4310.8 82,-4266.8 16,-4266.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-4291.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4279.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4288.8 82,-4310.8 122,-4310.8 122,-4288.8 82,-4288.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-4296.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4288.8 122,-4310.8 196,-4310.8 196,-4288.8 122,-4288.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4296.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4266.8 82,-4288.8 122,-4288.8 122,-4266.8 82,-4266.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-4274.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4266.8 122,-4288.8 196,-4288.8 196,-4266.8 122,-4266.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4274.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 8&#45;&gt;9 -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>8&#45;&gt;9</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4346.9C106,-4339.12 106,-4330.1 106,-4321.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4321.76 106,-4311.76 102.5,-4321.76 109.5,-4321.76\"/>\n", "</g>\n", "<!-- 10 -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>10</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-4230.8 28.5,-4230.8 28.5,-4186.8 183.5,-4186.8 183.5,-4230.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-4186.8 28.5,-4230.8 69.5,-4230.8 69.5,-4186.8 28.5,-4186.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-4211.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4199.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4208.8 69.5,-4230.8 109.5,-4230.8 109.5,-4208.8 69.5,-4208.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-4216.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4208.8 109.5,-4230.8 183.5,-4230.8 183.5,-4208.8 109.5,-4208.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4216.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-4186.8 69.5,-4208.8 109.5,-4208.8 109.5,-4186.8 69.5,-4186.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-4194.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-4186.8 109.5,-4208.8 183.5,-4208.8 183.5,-4186.8 109.5,-4186.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-4194.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 9&#45;&gt;10 -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>9&#45;&gt;10</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4266.9C106,-4259.12 106,-4250.1 106,-4241.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4241.76 106,-4231.76 102.5,-4241.76 109.5,-4241.76\"/>\n", "</g>\n", "<!-- 11 -->\n", "<g id=\"node12\" class=\"node\">\n", "<title>11</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-4150.8 27.5,-4150.8 27.5,-4106.8 184.5,-4106.8 184.5,-4150.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-4106.8 27.5,-4150.8 70.5,-4150.8 70.5,-4106.8 27.5,-4106.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-4131.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4119.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4128.8 70.5,-4150.8 110.5,-4150.8 110.5,-4128.8 70.5,-4128.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-4136.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4128.8 110.5,-4150.8 184.5,-4150.8 184.5,-4128.8 110.5,-4128.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4136.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-4106.8 70.5,-4128.8 110.5,-4128.8 110.5,-4106.8 70.5,-4106.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-4114.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-4106.8 110.5,-4128.8 184.5,-4128.8 184.5,-4106.8 110.5,-4106.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-4114.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 10&#45;&gt;11 -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>10&#45;&gt;11</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4186.9C106,-4179.12 106,-4170.1 106,-4161.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4161.76 106,-4151.76 102.5,-4161.76 109.5,-4161.76\"/>\n", "</g>\n", "<!-- 12 -->\n", "<g id=\"node13\" class=\"node\">\n", "<title>12</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-4070.8 16,-4070.8 16,-4026.8 196,-4026.8 196,-4070.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-4026.8 16,-4070.8 82,-4070.8 82,-4026.8 16,-4026.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-4051.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-4039.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4048.8 82,-4070.8 122,-4070.8 122,-4048.8 82,-4048.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-4056.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4048.8 122,-4070.8 196,-4070.8 196,-4048.8 122,-4048.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4056.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-4026.8 82,-4048.8 122,-4048.8 122,-4026.8 82,-4026.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-4034.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-4026.8 122,-4048.8 196,-4048.8 196,-4026.8 122,-4026.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-4034.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 11&#45;&gt;12 -->\n", "<g id=\"edge12\" class=\"edge\">\n", "<title>11&#45;&gt;12</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4106.9C106,-4099.12 106,-4090.1 106,-4081.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4081.76 106,-4071.76 102.5,-4081.76 109.5,-4081.76\"/>\n", "</g>\n", "<!-- 13 -->\n", "<g id=\"node14\" class=\"node\">\n", "<title>13</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-3990.8 28.5,-3990.8 28.5,-3946.8 183.5,-3946.8 183.5,-3990.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-3946.8 28.5,-3990.8 69.5,-3990.8 69.5,-3946.8 28.5,-3946.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-3971.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3959.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3968.8 69.5,-3990.8 109.5,-3990.8 109.5,-3968.8 69.5,-3968.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-3976.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3968.8 109.5,-3990.8 183.5,-3990.8 183.5,-3968.8 109.5,-3968.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3976.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3946.8 69.5,-3968.8 109.5,-3968.8 109.5,-3946.8 69.5,-3946.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-3954.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3946.8 109.5,-3968.8 183.5,-3968.8 183.5,-3946.8 109.5,-3946.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3954.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 12&#45;&gt;13 -->\n", "<g id=\"edge13\" class=\"edge\">\n", "<title>12&#45;&gt;13</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-4026.9C106,-4019.12 106,-4010.1 106,-4001.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-4001.76 106,-3991.76 102.5,-4001.76 109.5,-4001.76\"/>\n", "</g>\n", "<!-- 14 -->\n", "<g id=\"node15\" class=\"node\">\n", "<title>14</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-3910.8 27.5,-3910.8 27.5,-3866.8 184.5,-3866.8 184.5,-3910.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-3866.8 27.5,-3910.8 70.5,-3910.8 70.5,-3866.8 27.5,-3866.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-3891.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3879.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3888.8 70.5,-3910.8 110.5,-3910.8 110.5,-3888.8 70.5,-3888.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-3896.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3888.8 110.5,-3910.8 184.5,-3910.8 184.5,-3888.8 110.5,-3888.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3896.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3866.8 70.5,-3888.8 110.5,-3888.8 110.5,-3866.8 70.5,-3866.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-3874.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3866.8 110.5,-3888.8 184.5,-3888.8 184.5,-3866.8 110.5,-3866.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3874.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "</g>\n", "<!-- 13&#45;&gt;14 -->\n", "<g id=\"edge14\" class=\"edge\">\n", "<title>13&#45;&gt;14</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3946.9C106,-3939.12 106,-3930.1 106,-3921.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3921.76 106,-3911.76 102.5,-3921.76 109.5,-3921.76\"/>\n", "</g>\n", "<!-- 15 -->\n", "<g id=\"node16\" class=\"node\">\n", "<title>15</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"191,-3830.8 21,-3830.8 21,-3786.8 191,-3786.8 191,-3830.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"21,-3786.8 21,-3830.8 77,-3830.8 77,-3786.8 21,-3786.8\"/>\n", "<text text-anchor=\"start\" x=\"25.67\" y=\"-3811.8\" font-family=\"Linux libertine\" font-size=\"10.00\">MaxPool2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3799.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"77,-3808.8 77,-3830.8 117,-3830.8 117,-3808.8 77,-3808.8\"/>\n", "<text text-anchor=\"start\" x=\"85.33\" y=\"-3816.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"117,-3808.8 117,-3830.8 191,-3830.8 191,-3808.8 117,-3808.8\"/>\n", "<text text-anchor=\"start\" x=\"121.92\" y=\"-3816.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 32, 32) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"77,-3786.8 77,-3808.8 117,-3808.8 117,-3786.8 77,-3786.8\"/>\n", "<text text-anchor=\"start\" x=\"81.58\" y=\"-3794.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"117,-3786.8 117,-3808.8 191,-3808.8 191,-3786.8 117,-3786.8\"/>\n", "<text text-anchor=\"start\" x=\"121.92\" y=\"-3794.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 16, 16) </text>\n", "</g>\n", "<!-- 14&#45;&gt;15 -->\n", "<g id=\"edge15\" class=\"edge\">\n", "<title>14&#45;&gt;15</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3866.9C106,-3859.12 106,-3850.1 106,-3841.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3841.76 106,-3831.76 102.5,-3841.76 109.5,-3841.76\"/>\n", "</g>\n", "<!-- 16 -->\n", "<g id=\"node17\" class=\"node\">\n", "<title>16</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-3750.8 16,-3750.8 16,-3706.8 196,-3706.8 196,-3750.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-3706.8 16,-3750.8 82,-3750.8 82,-3706.8 16,-3706.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-3731.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3719.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3728.8 82,-3750.8 122,-3750.8 122,-3728.8 82,-3728.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-3736.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3728.8 122,-3750.8 196,-3750.8 196,-3728.8 122,-3728.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3736.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3706.8 82,-3728.8 122,-3728.8 122,-3706.8 82,-3706.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-3714.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3706.8 122,-3728.8 196,-3728.8 196,-3706.8 122,-3706.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3714.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 16, 16) </text>\n", "</g>\n", "<!-- 15&#45;&gt;16 -->\n", "<g id=\"edge16\" class=\"edge\">\n", "<title>15&#45;&gt;16</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3786.9C106,-3779.12 106,-3770.1 106,-3761.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3761.76 106,-3751.76 102.5,-3761.76 109.5,-3761.76\"/>\n", "</g>\n", "<!-- 17 -->\n", "<g id=\"node18\" class=\"node\">\n", "<title>17</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-3670.8 28.5,-3670.8 28.5,-3626.8 183.5,-3626.8 183.5,-3670.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-3626.8 28.5,-3670.8 69.5,-3670.8 69.5,-3626.8 28.5,-3626.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-3651.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3639.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3648.8 69.5,-3670.8 109.5,-3670.8 109.5,-3648.8 69.5,-3648.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-3656.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3648.8 109.5,-3670.8 183.5,-3670.8 183.5,-3648.8 109.5,-3648.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3656.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3626.8 69.5,-3648.8 109.5,-3648.8 109.5,-3626.8 69.5,-3626.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-3634.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3626.8 109.5,-3648.8 183.5,-3648.8 183.5,-3626.8 109.5,-3626.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3634.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 16&#45;&gt;17 -->\n", "<g id=\"edge17\" class=\"edge\">\n", "<title>16&#45;&gt;17</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3706.9C106,-3699.12 106,-3690.1 106,-3681.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3681.76 106,-3671.76 102.5,-3681.76 109.5,-3681.76\"/>\n", "</g>\n", "<!-- 18 -->\n", "<g id=\"node19\" class=\"node\">\n", "<title>18</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-3590.8 27.5,-3590.8 27.5,-3546.8 184.5,-3546.8 184.5,-3590.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-3546.8 27.5,-3590.8 70.5,-3590.8 70.5,-3546.8 27.5,-3546.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-3571.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3559.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3568.8 70.5,-3590.8 110.5,-3590.8 110.5,-3568.8 70.5,-3568.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-3576.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3568.8 110.5,-3590.8 184.5,-3590.8 184.5,-3568.8 110.5,-3568.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3576.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3546.8 70.5,-3568.8 110.5,-3568.8 110.5,-3546.8 70.5,-3546.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-3554.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3546.8 110.5,-3568.8 184.5,-3568.8 184.5,-3546.8 110.5,-3546.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3554.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 17&#45;&gt;18 -->\n", "<g id=\"edge18\" class=\"edge\">\n", "<title>17&#45;&gt;18</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3626.9C106,-3619.12 106,-3610.1 106,-3601.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3601.76 106,-3591.76 102.5,-3601.76 109.5,-3601.76\"/>\n", "</g>\n", "<!-- 19 -->\n", "<g id=\"node20\" class=\"node\">\n", "<title>19</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-3510.8 16,-3510.8 16,-3466.8 196,-3466.8 196,-3510.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-3466.8 16,-3510.8 82,-3510.8 82,-3466.8 16,-3466.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-3491.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3479.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3488.8 82,-3510.8 122,-3510.8 122,-3488.8 82,-3488.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-3496.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3488.8 122,-3510.8 196,-3510.8 196,-3488.8 122,-3488.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3496.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3466.8 82,-3488.8 122,-3488.8 122,-3466.8 82,-3466.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-3474.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3466.8 122,-3488.8 196,-3488.8 196,-3466.8 122,-3466.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3474.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 18&#45;&gt;19 -->\n", "<g id=\"edge19\" class=\"edge\">\n", "<title>18&#45;&gt;19</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3546.9C106,-3539.12 106,-3530.1 106,-3521.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3521.76 106,-3511.76 102.5,-3521.76 109.5,-3521.76\"/>\n", "</g>\n", "<!-- 20 -->\n", "<g id=\"node21\" class=\"node\">\n", "<title>20</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-3430.8 28.5,-3430.8 28.5,-3386.8 183.5,-3386.8 183.5,-3430.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-3386.8 28.5,-3430.8 69.5,-3430.8 69.5,-3386.8 28.5,-3386.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-3411.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3399.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3408.8 69.5,-3430.8 109.5,-3430.8 109.5,-3408.8 69.5,-3408.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-3416.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3408.8 109.5,-3430.8 183.5,-3430.8 183.5,-3408.8 109.5,-3408.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3416.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3386.8 69.5,-3408.8 109.5,-3408.8 109.5,-3386.8 69.5,-3386.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-3394.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3386.8 109.5,-3408.8 183.5,-3408.8 183.5,-3386.8 109.5,-3386.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3394.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 19&#45;&gt;20 -->\n", "<g id=\"edge20\" class=\"edge\">\n", "<title>19&#45;&gt;20</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3466.9C106,-3459.12 106,-3450.1 106,-3441.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3441.76 106,-3431.76 102.5,-3441.76 109.5,-3441.76\"/>\n", "</g>\n", "<!-- 21 -->\n", "<g id=\"node22\" class=\"node\">\n", "<title>21</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-3350.8 27.5,-3350.8 27.5,-3306.8 184.5,-3306.8 184.5,-3350.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-3306.8 27.5,-3350.8 70.5,-3350.8 70.5,-3306.8 27.5,-3306.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-3331.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3319.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3328.8 70.5,-3350.8 110.5,-3350.8 110.5,-3328.8 70.5,-3328.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-3336.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3328.8 110.5,-3350.8 184.5,-3350.8 184.5,-3328.8 110.5,-3328.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3336.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3306.8 70.5,-3328.8 110.5,-3328.8 110.5,-3306.8 70.5,-3306.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-3314.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3306.8 110.5,-3328.8 184.5,-3328.8 184.5,-3306.8 110.5,-3306.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3314.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 20&#45;&gt;21 -->\n", "<g id=\"edge21\" class=\"edge\">\n", "<title>20&#45;&gt;21</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3386.9C106,-3379.12 106,-3370.1 106,-3361.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3361.76 106,-3351.76 102.5,-3361.76 109.5,-3361.76\"/>\n", "</g>\n", "<!-- 22 -->\n", "<g id=\"node23\" class=\"node\">\n", "<title>22</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-3270.8 16,-3270.8 16,-3226.8 196,-3226.8 196,-3270.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-3226.8 16,-3270.8 82,-3270.8 82,-3226.8 16,-3226.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-3251.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3239.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3248.8 82,-3270.8 122,-3270.8 122,-3248.8 82,-3248.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-3256.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3248.8 122,-3270.8 196,-3270.8 196,-3248.8 122,-3248.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3256.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3226.8 82,-3248.8 122,-3248.8 122,-3226.8 82,-3226.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-3234.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3226.8 122,-3248.8 196,-3248.8 196,-3226.8 122,-3226.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3234.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 21&#45;&gt;22 -->\n", "<g id=\"edge22\" class=\"edge\">\n", "<title>21&#45;&gt;22</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3306.9C106,-3299.12 106,-3290.1 106,-3281.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3281.76 106,-3271.76 102.5,-3281.76 109.5,-3281.76\"/>\n", "</g>\n", "<!-- 23 -->\n", "<g id=\"node24\" class=\"node\">\n", "<title>23</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-3190.8 28.5,-3190.8 28.5,-3146.8 183.5,-3146.8 183.5,-3190.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-3146.8 28.5,-3190.8 69.5,-3190.8 69.5,-3146.8 28.5,-3146.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-3171.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3159.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3168.8 69.5,-3190.8 109.5,-3190.8 109.5,-3168.8 69.5,-3168.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-3176.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3168.8 109.5,-3190.8 183.5,-3190.8 183.5,-3168.8 109.5,-3168.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3176.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-3146.8 69.5,-3168.8 109.5,-3168.8 109.5,-3146.8 69.5,-3146.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-3154.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-3146.8 109.5,-3168.8 183.5,-3168.8 183.5,-3146.8 109.5,-3146.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-3154.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 22&#45;&gt;23 -->\n", "<g id=\"edge23\" class=\"edge\">\n", "<title>22&#45;&gt;23</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3226.9C106,-3219.12 106,-3210.1 106,-3201.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3201.76 106,-3191.76 102.5,-3201.76 109.5,-3201.76\"/>\n", "</g>\n", "<!-- 24 -->\n", "<g id=\"node25\" class=\"node\">\n", "<title>24</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-3110.8 27.5,-3110.8 27.5,-3066.8 184.5,-3066.8 184.5,-3110.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-3066.8 27.5,-3110.8 70.5,-3110.8 70.5,-3066.8 27.5,-3066.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-3091.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-3079.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3088.8 70.5,-3110.8 110.5,-3110.8 110.5,-3088.8 70.5,-3088.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-3096.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3088.8 110.5,-3110.8 184.5,-3110.8 184.5,-3088.8 110.5,-3088.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3096.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-3066.8 70.5,-3088.8 110.5,-3088.8 110.5,-3066.8 70.5,-3066.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-3074.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-3066.8 110.5,-3088.8 184.5,-3088.8 184.5,-3066.8 110.5,-3066.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-3074.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 23&#45;&gt;24 -->\n", "<g id=\"edge24\" class=\"edge\">\n", "<title>23&#45;&gt;24</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3146.9C106,-3139.12 106,-3130.1 106,-3121.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3121.76 106,-3111.76 102.5,-3121.76 109.5,-3121.76\"/>\n", "</g>\n", "<!-- 25 -->\n", "<g id=\"node26\" class=\"node\">\n", "<title>25</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-3030.8 16,-3030.8 16,-2986.8 196,-2986.8 196,-3030.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-2986.8 16,-3030.8 82,-3030.8 82,-2986.8 16,-2986.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-3011.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2999.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-3008.8 82,-3030.8 122,-3030.8 122,-3008.8 82,-3008.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-3016.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-3008.8 122,-3030.8 196,-3030.8 196,-3008.8 122,-3008.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-3016.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-2986.8 82,-3008.8 122,-3008.8 122,-2986.8 82,-2986.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-2994.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-2986.8 122,-3008.8 196,-3008.8 196,-2986.8 122,-2986.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-2994.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 24&#45;&gt;25 -->\n", "<g id=\"edge25\" class=\"edge\">\n", "<title>24&#45;&gt;25</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-3066.9C106,-3059.12 106,-3050.1 106,-3041.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-3041.76 106,-3031.76 102.5,-3041.76 109.5,-3041.76\"/>\n", "</g>\n", "<!-- 26 -->\n", "<g id=\"node27\" class=\"node\">\n", "<title>26</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-2950.8 28.5,-2950.8 28.5,-2906.8 183.5,-2906.8 183.5,-2950.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-2906.8 28.5,-2950.8 69.5,-2950.8 69.5,-2906.8 28.5,-2906.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-2931.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2919.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-2928.8 69.5,-2950.8 109.5,-2950.8 109.5,-2928.8 69.5,-2928.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-2936.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-2928.8 109.5,-2950.8 183.5,-2950.8 183.5,-2928.8 109.5,-2928.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-2936.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-2906.8 69.5,-2928.8 109.5,-2928.8 109.5,-2906.8 69.5,-2906.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-2914.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-2906.8 109.5,-2928.8 183.5,-2928.8 183.5,-2906.8 109.5,-2906.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-2914.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 25&#45;&gt;26 -->\n", "<g id=\"edge26\" class=\"edge\">\n", "<title>25&#45;&gt;26</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2986.9C106,-2979.12 106,-2970.1 106,-2961.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2961.76 106,-2951.76 102.5,-2961.76 109.5,-2961.76\"/>\n", "</g>\n", "<!-- 27 -->\n", "<g id=\"node28\" class=\"node\">\n", "<title>27</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-2870.8 27.5,-2870.8 27.5,-2826.8 184.5,-2826.8 184.5,-2870.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-2826.8 27.5,-2870.8 70.5,-2870.8 70.5,-2826.8 27.5,-2826.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-2851.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2839.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-2848.8 70.5,-2870.8 110.5,-2870.8 110.5,-2848.8 70.5,-2848.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-2856.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-2848.8 110.5,-2870.8 184.5,-2870.8 184.5,-2848.8 110.5,-2848.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-2856.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-2826.8 70.5,-2848.8 110.5,-2848.8 110.5,-2826.8 70.5,-2826.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-2834.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-2826.8 110.5,-2848.8 184.5,-2848.8 184.5,-2826.8 110.5,-2826.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-2834.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 26&#45;&gt;27 -->\n", "<g id=\"edge27\" class=\"edge\">\n", "<title>26&#45;&gt;27</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2906.9C106,-2899.12 106,-2890.1 106,-2881.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2881.76 106,-2871.76 102.5,-2881.76 109.5,-2881.76\"/>\n", "</g>\n", "<!-- 28 -->\n", "<g id=\"node29\" class=\"node\">\n", "<title>28</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"196,-2790.8 16,-2790.8 16,-2746.8 196,-2746.8 196,-2790.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"16,-2746.8 16,-2790.8 82,-2790.8 82,-2746.8 16,-2746.8\"/>\n", "<text text-anchor=\"start\" x=\"20.67\" y=\"-2771.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2759.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-2768.8 82,-2790.8 122,-2790.8 122,-2768.8 82,-2768.8\"/>\n", "<text text-anchor=\"start\" x=\"90.33\" y=\"-2776.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-2768.8 122,-2790.8 196,-2790.8 196,-2768.8 122,-2768.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-2776.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"82,-2746.8 82,-2768.8 122,-2768.8 122,-2746.8 82,-2746.8\"/>\n", "<text text-anchor=\"start\" x=\"86.58\" y=\"-2754.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"122,-2746.8 122,-2768.8 196,-2768.8 196,-2746.8 122,-2746.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-2754.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 27&#45;&gt;28 -->\n", "<g id=\"edge28\" class=\"edge\">\n", "<title>27&#45;&gt;28</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2826.9C106,-2819.12 106,-2810.1 106,-2801.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2801.76 106,-2791.76 102.5,-2801.76 109.5,-2801.76\"/>\n", "</g>\n", "<!-- 29 -->\n", "<g id=\"node30\" class=\"node\">\n", "<title>29</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-2710.8 28.5,-2710.8 28.5,-2666.8 183.5,-2666.8 183.5,-2710.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-2666.8 28.5,-2710.8 69.5,-2710.8 69.5,-2666.8 28.5,-2666.8\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-2691.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2679.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-2688.8 69.5,-2710.8 109.5,-2710.8 109.5,-2688.8 69.5,-2688.8\"/>\n", "<text text-anchor=\"start\" x=\"77.83\" y=\"-2696.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-2688.8 109.5,-2710.8 183.5,-2710.8 183.5,-2688.8 109.5,-2688.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-2696.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"69.5,-2666.8 69.5,-2688.8 109.5,-2688.8 109.5,-2666.8 69.5,-2666.8\"/>\n", "<text text-anchor=\"start\" x=\"74.08\" y=\"-2674.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"109.5,-2666.8 109.5,-2688.8 183.5,-2688.8 183.5,-2666.8 109.5,-2666.8\"/>\n", "<text text-anchor=\"start\" x=\"114.42\" y=\"-2674.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 28&#45;&gt;29 -->\n", "<g id=\"edge29\" class=\"edge\">\n", "<title>28&#45;&gt;29</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2746.9C106,-2739.12 106,-2730.1 106,-2721.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2721.76 106,-2711.76 102.5,-2721.76 109.5,-2721.76\"/>\n", "</g>\n", "<!-- 30 -->\n", "<g id=\"node31\" class=\"node\">\n", "<title>30</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"184.5,-2630.8 27.5,-2630.8 27.5,-2586.8 184.5,-2586.8 184.5,-2630.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"27.5,-2586.8 27.5,-2630.8 70.5,-2630.8 70.5,-2586.8 27.5,-2586.8\"/>\n", "<text text-anchor=\"start\" x=\"32.33\" y=\"-2611.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2599.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-2608.8 70.5,-2630.8 110.5,-2630.8 110.5,-2608.8 70.5,-2608.8\"/>\n", "<text text-anchor=\"start\" x=\"78.83\" y=\"-2616.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-2608.8 110.5,-2630.8 184.5,-2630.8 184.5,-2608.8 110.5,-2608.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-2616.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"70.5,-2586.8 70.5,-2608.8 110.5,-2608.8 110.5,-2586.8 70.5,-2586.8\"/>\n", "<text text-anchor=\"start\" x=\"75.08\" y=\"-2594.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"110.5,-2586.8 110.5,-2608.8 184.5,-2608.8 184.5,-2586.8 110.5,-2586.8\"/>\n", "<text text-anchor=\"start\" x=\"115.42\" y=\"-2594.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "</g>\n", "<!-- 29&#45;&gt;30 -->\n", "<g id=\"edge30\" class=\"edge\">\n", "<title>29&#45;&gt;30</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2666.9C106,-2659.12 106,-2650.1 106,-2641.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2641.76 106,-2631.76 102.5,-2641.76 109.5,-2641.76\"/>\n", "</g>\n", "<!-- 31 -->\n", "<g id=\"node32\" class=\"node\">\n", "<title>31</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"191,-2550.8 21,-2550.8 21,-2506.8 191,-2506.8 191,-2550.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"21,-2506.8 21,-2550.8 77,-2550.8 77,-2506.8 21,-2506.8\"/>\n", "<text text-anchor=\"start\" x=\"25.67\" y=\"-2531.8\" font-family=\"Linux libertine\" font-size=\"10.00\">MaxPool2d</text>\n", "<text text-anchor=\"start\" x=\"34\" y=\"-2519.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"77,-2528.8 77,-2550.8 117,-2550.8 117,-2528.8 77,-2528.8\"/>\n", "<text text-anchor=\"start\" x=\"85.33\" y=\"-2536.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"117,-2528.8 117,-2550.8 191,-2550.8 191,-2528.8 117,-2528.8\"/>\n", "<text text-anchor=\"start\" x=\"121.92\" y=\"-2536.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 16, 16) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"77,-2506.8 77,-2528.8 117,-2528.8 117,-2506.8 77,-2506.8\"/>\n", "<text text-anchor=\"start\" x=\"81.58\" y=\"-2514.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"117,-2506.8 117,-2528.8 191,-2528.8 191,-2506.8 117,-2506.8\"/>\n", "<text text-anchor=\"start\" x=\"126.92\" y=\"-2514.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 8, 8) </text>\n", "</g>\n", "<!-- 30&#45;&gt;31 -->\n", "<g id=\"edge31\" class=\"edge\">\n", "<title>30&#45;&gt;31</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2586.9C106,-2579.12 106,-2570.1 106,-2561.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2561.76 106,-2551.76 102.5,-2561.76 109.5,-2561.76\"/>\n", "</g>\n", "<!-- 32 -->\n", "<g id=\"node33\" class=\"node\">\n", "<title>32</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"191,-2470.8 21,-2470.8 21,-2426.8 191,-2426.8 191,-2470.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"21,-2426.8 21,-2470.8 87,-2470.8 87,-2426.8 21,-2426.8\"/>\n", "<text text-anchor=\"start\" x=\"25.67\" y=\"-2451.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"39\" y=\"-2439.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"87,-2448.8 87,-2470.8 127,-2470.8 127,-2448.8 87,-2448.8\"/>\n", "<text text-anchor=\"start\" x=\"95.33\" y=\"-2456.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"127,-2448.8 127,-2470.8 191,-2470.8 191,-2448.8 127,-2448.8\"/>\n", "<text text-anchor=\"start\" x=\"131.92\" y=\"-2456.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"87,-2426.8 87,-2448.8 127,-2448.8 127,-2426.8 87,-2426.8\"/>\n", "<text text-anchor=\"start\" x=\"91.58\" y=\"-2434.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"127,-2426.8 127,-2448.8 191,-2448.8 191,-2426.8 127,-2426.8\"/>\n", "<text text-anchor=\"start\" x=\"131.92\" y=\"-2434.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 8, 8) </text>\n", "</g>\n", "<!-- 31&#45;&gt;32 -->\n", "<g id=\"edge32\" class=\"edge\">\n", "<title>31&#45;&gt;32</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2506.9C106,-2499.12 106,-2490.1 106,-2481.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2481.76 106,-2471.76 102.5,-2481.76 109.5,-2481.76\"/>\n", "</g>\n", "<!-- 33 -->\n", "<g id=\"node34\" class=\"node\">\n", "<title>33</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-2390.8 31,-2390.8 31,-2346.8 181,-2346.8 181,-2390.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-2346.8 31,-2390.8 72,-2390.8 72,-2346.8 31,-2346.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-2371.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2359.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2368.8 72,-2390.8 112,-2390.8 112,-2368.8 72,-2368.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-2376.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2368.8 112,-2390.8 181,-2390.8 181,-2368.8 112,-2368.8\"/>\n", "<text text-anchor=\"start\" x=\"119.42\" y=\"-2376.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 768, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2346.8 72,-2368.8 112,-2368.8 112,-2346.8 72,-2346.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-2354.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2346.8 112,-2368.8 181,-2368.8 181,-2346.8 112,-2346.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-2354.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 32&#45;&gt;33 -->\n", "<g id=\"edge33\" class=\"edge\">\n", "<title>32&#45;&gt;33</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2426.9C106,-2419.12 106,-2410.1 106,-2401.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2401.76 106,-2391.76 102.5,-2401.76 109.5,-2401.76\"/>\n", "</g>\n", "<!-- 34 -->\n", "<g id=\"node35\" class=\"node\">\n", "<title>34</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-2310.8 30,-2310.8 30,-2266.8 182,-2266.8 182,-2310.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-2266.8 30,-2310.8 73,-2310.8 73,-2266.8 30,-2266.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-2291.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2279.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2288.8 73,-2310.8 113,-2310.8 113,-2288.8 73,-2288.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-2296.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2288.8 113,-2310.8 182,-2310.8 182,-2288.8 113,-2288.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2296.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2266.8 73,-2288.8 113,-2288.8 113,-2266.8 73,-2266.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-2274.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2266.8 113,-2288.8 182,-2288.8 182,-2266.8 113,-2266.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2274.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 33&#45;&gt;34 -->\n", "<g id=\"edge34\" class=\"edge\">\n", "<title>33&#45;&gt;34</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2346.9C106,-2339.12 106,-2330.1 106,-2321.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2321.76 106,-2311.76 102.5,-2321.76 109.5,-2321.76\"/>\n", "</g>\n", "<!-- 35 -->\n", "<g id=\"node36\" class=\"node\">\n", "<title>35</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-2230.8 18.5,-2230.8 18.5,-2186.8 193.5,-2186.8 193.5,-2230.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-2186.8 18.5,-2230.8 84.5,-2230.8 84.5,-2186.8 18.5,-2186.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-2211.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2199.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-2208.8 84.5,-2230.8 124.5,-2230.8 124.5,-2208.8 84.5,-2208.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-2216.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-2208.8 124.5,-2230.8 193.5,-2230.8 193.5,-2208.8 124.5,-2208.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-2216.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-2186.8 84.5,-2208.8 124.5,-2208.8 124.5,-2186.8 84.5,-2186.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-2194.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-2186.8 124.5,-2208.8 193.5,-2208.8 193.5,-2186.8 124.5,-2186.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-2194.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 34&#45;&gt;35 -->\n", "<g id=\"edge35\" class=\"edge\">\n", "<title>34&#45;&gt;35</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2266.9C106,-2259.12 106,-2250.1 106,-2241.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2241.76 106,-2231.76 102.5,-2241.76 109.5,-2241.76\"/>\n", "</g>\n", "<!-- 36 -->\n", "<g id=\"node37\" class=\"node\">\n", "<title>36</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-2150.8 31,-2150.8 31,-2106.8 181,-2106.8 181,-2150.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-2106.8 31,-2150.8 72,-2150.8 72,-2106.8 31,-2106.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-2131.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2119.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2128.8 72,-2150.8 112,-2150.8 112,-2128.8 72,-2128.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-2136.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2128.8 112,-2150.8 181,-2150.8 181,-2128.8 112,-2128.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-2136.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-2106.8 72,-2128.8 112,-2128.8 112,-2106.8 72,-2106.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-2114.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-2106.8 112,-2128.8 181,-2128.8 181,-2106.8 112,-2106.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-2114.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 35&#45;&gt;36 -->\n", "<g id=\"edge36\" class=\"edge\">\n", "<title>35&#45;&gt;36</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2186.9C106,-2179.12 106,-2170.1 106,-2161.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2161.76 106,-2151.76 102.5,-2161.76 109.5,-2161.76\"/>\n", "</g>\n", "<!-- 37 -->\n", "<g id=\"node38\" class=\"node\">\n", "<title>37</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-2070.8 30,-2070.8 30,-2026.8 182,-2026.8 182,-2070.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-2026.8 30,-2070.8 73,-2070.8 73,-2026.8 30,-2026.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-2051.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-2039.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2048.8 73,-2070.8 113,-2070.8 113,-2048.8 73,-2048.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-2056.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2048.8 113,-2070.8 182,-2070.8 182,-2048.8 113,-2048.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2056.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-2026.8 73,-2048.8 113,-2048.8 113,-2026.8 73,-2026.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-2034.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-2026.8 113,-2048.8 182,-2048.8 182,-2026.8 113,-2026.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-2034.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 36&#45;&gt;37 -->\n", "<g id=\"edge37\" class=\"edge\">\n", "<title>36&#45;&gt;37</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2106.9C106,-2099.12 106,-2090.1 106,-2081.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2081.76 106,-2071.76 102.5,-2081.76 109.5,-2081.76\"/>\n", "</g>\n", "<!-- 38 -->\n", "<g id=\"node39\" class=\"node\">\n", "<title>38</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-1990.8 18.5,-1990.8 18.5,-1946.8 193.5,-1946.8 193.5,-1990.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-1946.8 18.5,-1990.8 84.5,-1990.8 84.5,-1946.8 18.5,-1946.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-1971.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1959.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1968.8 84.5,-1990.8 124.5,-1990.8 124.5,-1968.8 84.5,-1968.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-1976.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1968.8 124.5,-1990.8 193.5,-1990.8 193.5,-1968.8 124.5,-1968.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1976.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1946.8 84.5,-1968.8 124.5,-1968.8 124.5,-1946.8 84.5,-1946.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-1954.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1946.8 124.5,-1968.8 193.5,-1968.8 193.5,-1946.8 124.5,-1946.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1954.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 37&#45;&gt;38 -->\n", "<g id=\"edge38\" class=\"edge\">\n", "<title>37&#45;&gt;38</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-2026.9C106,-2019.12 106,-2010.1 106,-2001.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-2001.76 106,-1991.76 102.5,-2001.76 109.5,-2001.76\"/>\n", "</g>\n", "<!-- 39 -->\n", "<g id=\"node40\" class=\"node\">\n", "<title>39</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-1910.8 31,-1910.8 31,-1866.8 181,-1866.8 181,-1910.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-1866.8 31,-1910.8 72,-1910.8 72,-1866.8 31,-1866.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-1891.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1879.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1888.8 72,-1910.8 112,-1910.8 112,-1888.8 72,-1888.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-1896.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1888.8 112,-1910.8 181,-1910.8 181,-1888.8 112,-1888.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1896.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1866.8 72,-1888.8 112,-1888.8 112,-1866.8 72,-1866.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-1874.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1866.8 112,-1888.8 181,-1888.8 181,-1866.8 112,-1866.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1874.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 38&#45;&gt;39 -->\n", "<g id=\"edge39\" class=\"edge\">\n", "<title>38&#45;&gt;39</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1946.9C106,-1939.12 106,-1930.1 106,-1921.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1921.76 106,-1911.76 102.5,-1921.76 109.5,-1921.76\"/>\n", "</g>\n", "<!-- 40 -->\n", "<g id=\"node41\" class=\"node\">\n", "<title>40</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-1830.8 30,-1830.8 30,-1786.8 182,-1786.8 182,-1830.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-1786.8 30,-1830.8 73,-1830.8 73,-1786.8 30,-1786.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-1811.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1799.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1808.8 73,-1830.8 113,-1830.8 113,-1808.8 73,-1808.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-1816.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1808.8 113,-1830.8 182,-1830.8 182,-1808.8 113,-1808.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1816.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1786.8 73,-1808.8 113,-1808.8 113,-1786.8 73,-1786.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-1794.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1786.8 113,-1808.8 182,-1808.8 182,-1786.8 113,-1786.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1794.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 39&#45;&gt;40 -->\n", "<g id=\"edge40\" class=\"edge\">\n", "<title>39&#45;&gt;40</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1866.9C106,-1859.12 106,-1850.1 106,-1841.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1841.76 106,-1831.76 102.5,-1841.76 109.5,-1841.76\"/>\n", "</g>\n", "<!-- 41 -->\n", "<g id=\"node42\" class=\"node\">\n", "<title>41</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-1750.8 18.5,-1750.8 18.5,-1706.8 193.5,-1706.8 193.5,-1750.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-1706.8 18.5,-1750.8 84.5,-1750.8 84.5,-1706.8 18.5,-1706.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-1731.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1719.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1728.8 84.5,-1750.8 124.5,-1750.8 124.5,-1728.8 84.5,-1728.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-1736.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1728.8 124.5,-1750.8 193.5,-1750.8 193.5,-1728.8 124.5,-1728.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1736.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1706.8 84.5,-1728.8 124.5,-1728.8 124.5,-1706.8 84.5,-1706.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-1714.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1706.8 124.5,-1728.8 193.5,-1728.8 193.5,-1706.8 124.5,-1706.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1714.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 40&#45;&gt;41 -->\n", "<g id=\"edge41\" class=\"edge\">\n", "<title>40&#45;&gt;41</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1786.9C106,-1779.12 106,-1770.1 106,-1761.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1761.76 106,-1751.76 102.5,-1761.76 109.5,-1761.76\"/>\n", "</g>\n", "<!-- 42 -->\n", "<g id=\"node43\" class=\"node\">\n", "<title>42</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-1670.8 31,-1670.8 31,-1626.8 181,-1626.8 181,-1670.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-1626.8 31,-1670.8 72,-1670.8 72,-1626.8 31,-1626.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-1651.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1639.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1648.8 72,-1670.8 112,-1670.8 112,-1648.8 72,-1648.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-1656.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1648.8 112,-1670.8 181,-1670.8 181,-1648.8 112,-1648.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1656.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1626.8 72,-1648.8 112,-1648.8 112,-1626.8 72,-1626.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-1634.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1626.8 112,-1648.8 181,-1648.8 181,-1626.8 112,-1626.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1634.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 41&#45;&gt;42 -->\n", "<g id=\"edge42\" class=\"edge\">\n", "<title>41&#45;&gt;42</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1706.9C106,-1699.12 106,-1690.1 106,-1681.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1681.76 106,-1671.76 102.5,-1681.76 109.5,-1681.76\"/>\n", "</g>\n", "<!-- 43 -->\n", "<g id=\"node44\" class=\"node\">\n", "<title>43</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-1590.8 30,-1590.8 30,-1546.8 182,-1546.8 182,-1590.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-1546.8 30,-1590.8 73,-1590.8 73,-1546.8 30,-1546.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-1571.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1559.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1568.8 73,-1590.8 113,-1590.8 113,-1568.8 73,-1568.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-1576.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1568.8 113,-1590.8 182,-1590.8 182,-1568.8 113,-1568.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1576.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1546.8 73,-1568.8 113,-1568.8 113,-1546.8 73,-1546.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-1554.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1546.8 113,-1568.8 182,-1568.8 182,-1546.8 113,-1546.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1554.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 42&#45;&gt;43 -->\n", "<g id=\"edge43\" class=\"edge\">\n", "<title>42&#45;&gt;43</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1626.9C106,-1619.12 106,-1610.1 106,-1601.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1601.76 106,-1591.76 102.5,-1601.76 109.5,-1601.76\"/>\n", "</g>\n", "<!-- 44 -->\n", "<g id=\"node45\" class=\"node\">\n", "<title>44</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"193.5,-1510.8 18.5,-1510.8 18.5,-1466.8 193.5,-1466.8 193.5,-1510.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"18.5,-1466.8 18.5,-1510.8 84.5,-1510.8 84.5,-1466.8 18.5,-1466.8\"/>\n", "<text text-anchor=\"start\" x=\"23.17\" y=\"-1491.8\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1479.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1488.8 84.5,-1510.8 124.5,-1510.8 124.5,-1488.8 84.5,-1488.8\"/>\n", "<text text-anchor=\"start\" x=\"92.83\" y=\"-1496.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1488.8 124.5,-1510.8 193.5,-1510.8 193.5,-1488.8 124.5,-1488.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1496.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"84.5,-1466.8 84.5,-1488.8 124.5,-1488.8 124.5,-1466.8 84.5,-1466.8\"/>\n", "<text text-anchor=\"start\" x=\"89.08\" y=\"-1474.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"124.5,-1466.8 124.5,-1488.8 193.5,-1488.8 193.5,-1466.8 124.5,-1466.8\"/>\n", "<text text-anchor=\"start\" x=\"129.42\" y=\"-1474.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 43&#45;&gt;44 -->\n", "<g id=\"edge44\" class=\"edge\">\n", "<title>43&#45;&gt;44</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1546.9C106,-1539.12 106,-1530.1 106,-1521.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1521.76 106,-1511.76 102.5,-1521.76 109.5,-1521.76\"/>\n", "</g>\n", "<!-- 45 -->\n", "<g id=\"node46\" class=\"node\">\n", "<title>45</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-1430.8 31,-1430.8 31,-1386.8 181,-1386.8 181,-1430.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-1386.8 31,-1430.8 72,-1430.8 72,-1386.8 31,-1386.8\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-1411.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Conv2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1399.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1408.8 72,-1430.8 112,-1430.8 112,-1408.8 72,-1408.8\"/>\n", "<text text-anchor=\"start\" x=\"80.33\" y=\"-1416.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1408.8 112,-1430.8 181,-1430.8 181,-1408.8 112,-1408.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1416.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"72,-1386.8 72,-1408.8 112,-1408.8 112,-1386.8 72,-1386.8\"/>\n", "<text text-anchor=\"start\" x=\"76.58\" y=\"-1394.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"112,-1386.8 112,-1408.8 181,-1408.8 181,-1386.8 112,-1386.8\"/>\n", "<text text-anchor=\"start\" x=\"116.92\" y=\"-1394.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 44&#45;&gt;45 -->\n", "<g id=\"edge45\" class=\"edge\">\n", "<title>44&#45;&gt;45</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1466.9C106,-1459.12 106,-1450.1 106,-1441.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1441.76 106,-1431.76 102.5,-1441.76 109.5,-1441.76\"/>\n", "</g>\n", "<!-- 46 -->\n", "<g id=\"node47\" class=\"node\">\n", "<title>46</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"182,-1350.8 30,-1350.8 30,-1306.8 182,-1306.8 182,-1350.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"30,-1306.8 30,-1350.8 73,-1350.8 73,-1306.8 30,-1306.8\"/>\n", "<text text-anchor=\"start\" x=\"34.83\" y=\"-1331.8\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1319.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1328.8 73,-1350.8 113,-1350.8 113,-1328.8 73,-1328.8\"/>\n", "<text text-anchor=\"start\" x=\"81.33\" y=\"-1336.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1328.8 113,-1350.8 182,-1350.8 182,-1328.8 113,-1328.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1336.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"73,-1306.8 73,-1328.8 113,-1328.8 113,-1306.8 73,-1306.8\"/>\n", "<text text-anchor=\"start\" x=\"77.58\" y=\"-1314.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"113,-1306.8 113,-1328.8 182,-1328.8 182,-1306.8 113,-1306.8\"/>\n", "<text text-anchor=\"start\" x=\"117.92\" y=\"-1314.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "</g>\n", "<!-- 45&#45;&gt;46 -->\n", "<g id=\"edge46\" class=\"edge\">\n", "<title>45&#45;&gt;46</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1386.9C106,-1379.12 106,-1370.1 106,-1361.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1361.76 106,-1351.76 102.5,-1361.76 109.5,-1361.76\"/>\n", "</g>\n", "<!-- 47 -->\n", "<g id=\"node48\" class=\"node\">\n", "<title>47</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"188.5,-1270.8 23.5,-1270.8 23.5,-1226.8 188.5,-1226.8 188.5,-1270.8\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"23.5,-1226.8 23.5,-1270.8 79.5,-1270.8 79.5,-1226.8 23.5,-1226.8\"/>\n", "<text text-anchor=\"start\" x=\"28.17\" y=\"-1251.8\" font-family=\"Linux libertine\" font-size=\"10.00\">MaxPool2d</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1239.8\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"79.5,-1248.8 79.5,-1270.8 119.5,-1270.8 119.5,-1248.8 79.5,-1248.8\"/>\n", "<text text-anchor=\"start\" x=\"87.83\" y=\"-1256.8\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"119.5,-1248.8 119.5,-1270.8 188.5,-1270.8 188.5,-1248.8 119.5,-1248.8\"/>\n", "<text text-anchor=\"start\" x=\"124.42\" y=\"-1256.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 8, 8) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"79.5,-1226.8 79.5,-1248.8 119.5,-1248.8 119.5,-1226.8 79.5,-1226.8\"/>\n", "<text text-anchor=\"start\" x=\"84.08\" y=\"-1234.8\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"119.5,-1226.8 119.5,-1248.8 188.5,-1248.8 188.5,-1226.8 119.5,-1226.8\"/>\n", "<text text-anchor=\"start\" x=\"124.42\" y=\"-1234.8\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 4, 4) </text>\n", "</g>\n", "<!-- 46&#45;&gt;47 -->\n", "<g id=\"edge47\" class=\"edge\">\n", "<title>46&#45;&gt;47</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1306.9C106,-1299.12 106,-1290.1 106,-1281.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1281.76 106,-1271.76 102.5,-1281.76 109.5,-1281.76\"/>\n", "</g>\n", "<!-- 48 -->\n", "<g id=\"node49\" class=\"node\">\n", "<title>48</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"180,-1180.4 32,-1180.4 32,-1136.4 180,-1136.4 180,-1180.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"32,-1136.4 32,-1180.4 71,-1180.4 71,-1136.4 32,-1136.4\"/>\n", "<text text-anchor=\"start\" x=\"37.61\" y=\"-1161.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Flatten</text>\n", "<text text-anchor=\"start\" x=\"36.5\" y=\"-1149.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"71,-1158.4 71,-1180.4 111,-1180.4 111,-1158.4 71,-1158.4\"/>\n", "<text text-anchor=\"start\" x=\"79.33\" y=\"-1166.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"111,-1158.4 111,-1180.4 180,-1180.4 180,-1158.4 111,-1158.4\"/>\n", "<text text-anchor=\"start\" x=\"115.92\" y=\"-1166.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024, 4, 4) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"71,-1136.4 71,-1158.4 111,-1158.4 111,-1136.4 71,-1136.4\"/>\n", "<text text-anchor=\"start\" x=\"75.58\" y=\"-1144.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"111,-1136.4 111,-1158.4 180,-1158.4 180,-1136.4 111,-1136.4\"/>\n", "<text text-anchor=\"start\" x=\"123.42\" y=\"-1144.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 16384) </text>\n", "</g>\n", "<!-- 47&#45;&gt;48 -->\n", "<g id=\"edge48\" class=\"edge\">\n", "<title>47&#45;&gt;48</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1226.85C106,-1216.17 106,-1202.96 106,-1191.07\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1191.2 106,-1181.2 102.5,-1191.2 109.5,-1191.2\"/>\n", "</g>\n", "<!-- 49 -->\n", "<g id=\"node50\" class=\"node\">\n", "<title>49</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"186,-1100.4 26,-1100.4 26,-1056.4 186,-1056.4 186,-1100.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"26,-1056.4 26,-1100.4 92,-1100.4 92,-1056.4 26,-1056.4\"/>\n", "<text text-anchor=\"start\" x=\"30.67\" y=\"-1081.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"44\" y=\"-1069.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"92,-1078.4 92,-1100.4 132,-1100.4 132,-1078.4 92,-1078.4\"/>\n", "<text text-anchor=\"start\" x=\"100.33\" y=\"-1086.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"132,-1078.4 132,-1100.4 186,-1100.4 186,-1078.4 132,-1078.4\"/>\n", "<text text-anchor=\"start\" x=\"136.92\" y=\"-1086.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 16384) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"92,-1056.4 92,-1078.4 132,-1078.4 132,-1056.4 92,-1056.4\"/>\n", "<text text-anchor=\"start\" x=\"96.58\" y=\"-1064.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"132,-1056.4 132,-1078.4 186,-1078.4 186,-1056.4 132,-1056.4\"/>\n", "<text text-anchor=\"start\" x=\"136.92\" y=\"-1064.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 16384) </text>\n", "</g>\n", "<!-- 48&#45;&gt;49 -->\n", "<g id=\"edge49\" class=\"edge\">\n", "<title>48&#45;&gt;49</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1136.5C106,-1128.72 106,-1119.7 106,-1111.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1111.36 106,-1101.36 102.5,-1111.36 109.5,-1111.36\"/>\n", "</g>\n", "<!-- 50 -->\n", "<g id=\"node51\" class=\"node\">\n", "<title>50</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"172.5,-1020.4 39.5,-1020.4 39.5,-976.4 172.5,-976.4 172.5,-1020.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"39.5,-976.4 39.5,-1020.4 78.5,-1020.4 78.5,-976.4 39.5,-976.4\"/>\n", "<text text-anchor=\"start\" x=\"45.95\" y=\"-1001.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"44\" y=\"-989.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"78.5,-998.4 78.5,-1020.4 118.5,-1020.4 118.5,-998.4 78.5,-998.4\"/>\n", "<text text-anchor=\"start\" x=\"86.83\" y=\"-1006.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"118.5,-998.4 118.5,-1020.4 172.5,-1020.4 172.5,-998.4 118.5,-998.4\"/>\n", "<text text-anchor=\"start\" x=\"123.42\" y=\"-1006.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 16384) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"78.5,-976.4 78.5,-998.4 118.5,-998.4 118.5,-976.4 78.5,-976.4\"/>\n", "<text text-anchor=\"start\" x=\"83.08\" y=\"-984.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"118.5,-976.4 118.5,-998.4 172.5,-998.4 172.5,-976.4 118.5,-976.4\"/>\n", "<text text-anchor=\"start\" x=\"128.42\" y=\"-984.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 49&#45;&gt;50 -->\n", "<g id=\"edge50\" class=\"edge\">\n", "<title>49&#45;&gt;50</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-1056.5C106,-1048.72 106,-1039.7 106,-1031.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-1031.36 106,-1021.36 102.5,-1031.36 109.5,-1031.36\"/>\n", "</g>\n", "<!-- 51 -->\n", "<g id=\"node52\" class=\"node\">\n", "<title>51</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-940.4 31,-940.4 31,-896.4 181,-896.4 181,-940.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-896.4 31,-940.4 97,-940.4 97,-896.4 31,-896.4\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-921.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-909.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"97,-918.4 97,-940.4 137,-940.4 137,-918.4 97,-918.4\"/>\n", "<text text-anchor=\"start\" x=\"105.33\" y=\"-926.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"137,-918.4 137,-940.4 181,-940.4 181,-918.4 137,-918.4\"/>\n", "<text text-anchor=\"start\" x=\"141.92\" y=\"-926.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"97,-896.4 97,-918.4 137,-918.4 137,-896.4 97,-896.4\"/>\n", "<text text-anchor=\"start\" x=\"101.58\" y=\"-904.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"137,-896.4 137,-918.4 181,-918.4 181,-896.4 137,-896.4\"/>\n", "<text text-anchor=\"start\" x=\"141.92\" y=\"-904.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 50&#45;&gt;51 -->\n", "<g id=\"edge51\" class=\"edge\">\n", "<title>50&#45;&gt;51</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-976.5C106,-968.72 106,-959.7 106,-951.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-951.36 106,-941.36 102.5,-951.36 109.5,-951.36\"/>\n", "</g>\n", "<!-- 52 -->\n", "<g id=\"node53\" class=\"node\">\n", "<title>52</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"170,-860.4 42,-860.4 42,-816.4 170,-816.4 170,-860.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42,-816.4 42,-860.4 81,-860.4 81,-816.4 42,-816.4\"/>\n", "<text text-anchor=\"start\" x=\"48.45\" y=\"-841.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-829.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-838.4 81,-860.4 121,-860.4 121,-838.4 81,-838.4\"/>\n", "<text text-anchor=\"start\" x=\"89.33\" y=\"-846.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-838.4 121,-860.4 170,-860.4 170,-838.4 121,-838.4\"/>\n", "<text text-anchor=\"start\" x=\"128.42\" y=\"-846.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-816.4 81,-838.4 121,-838.4 121,-816.4 81,-816.4\"/>\n", "<text text-anchor=\"start\" x=\"85.58\" y=\"-824.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-816.4 121,-838.4 170,-838.4 170,-816.4 121,-816.4\"/>\n", "<text text-anchor=\"start\" x=\"125.92\" y=\"-824.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2048) </text>\n", "</g>\n", "<!-- 51&#45;&gt;52 -->\n", "<g id=\"edge52\" class=\"edge\">\n", "<title>51&#45;&gt;52</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-896.5C106,-888.72 106,-879.7 106,-871.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-871.36 106,-861.36 102.5,-871.36 109.5,-871.36\"/>\n", "</g>\n", "<!-- 53 -->\n", "<g id=\"node54\" class=\"node\">\n", "<title>53</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"172,-780.4 40,-780.4 40,-736.4 172,-736.4 172,-780.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"40,-736.4 40,-780.4 83,-780.4 83,-736.4 40,-736.4\"/>\n", "<text text-anchor=\"start\" x=\"44.83\" y=\"-761.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-749.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83,-758.4 83,-780.4 123,-780.4 123,-758.4 83,-758.4\"/>\n", "<text text-anchor=\"start\" x=\"91.33\" y=\"-766.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123,-758.4 123,-780.4 172,-780.4 172,-758.4 123,-758.4\"/>\n", "<text text-anchor=\"start\" x=\"127.92\" y=\"-766.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2048) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83,-736.4 83,-758.4 123,-758.4 123,-736.4 83,-736.4\"/>\n", "<text text-anchor=\"start\" x=\"87.58\" y=\"-744.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123,-736.4 123,-758.4 172,-758.4 172,-736.4 123,-736.4\"/>\n", "<text text-anchor=\"start\" x=\"127.92\" y=\"-744.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2048) </text>\n", "</g>\n", "<!-- 52&#45;&gt;53 -->\n", "<g id=\"edge53\" class=\"edge\">\n", "<title>52&#45;&gt;53</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-816.5C106,-808.72 106,-799.7 106,-791.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-791.36 106,-781.36 102.5,-791.36 109.5,-791.36\"/>\n", "</g>\n", "<!-- 54 -->\n", "<g id=\"node55\" class=\"node\">\n", "<title>54</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-700.4 28.5,-700.4 28.5,-656.4 183.5,-656.4 183.5,-700.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-656.4 28.5,-700.4 94.5,-700.4 94.5,-656.4 28.5,-656.4\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-681.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-669.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"94.5,-678.4 94.5,-700.4 134.5,-700.4 134.5,-678.4 94.5,-678.4\"/>\n", "<text text-anchor=\"start\" x=\"102.83\" y=\"-686.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"134.5,-678.4 134.5,-700.4 183.5,-700.4 183.5,-678.4 134.5,-678.4\"/>\n", "<text text-anchor=\"start\" x=\"139.42\" y=\"-686.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2048) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"94.5,-656.4 94.5,-678.4 134.5,-678.4 134.5,-656.4 94.5,-656.4\"/>\n", "<text text-anchor=\"start\" x=\"99.08\" y=\"-664.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"134.5,-656.4 134.5,-678.4 183.5,-678.4 183.5,-656.4 134.5,-656.4\"/>\n", "<text text-anchor=\"start\" x=\"139.42\" y=\"-664.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2048) </text>\n", "</g>\n", "<!-- 53&#45;&gt;54 -->\n", "<g id=\"edge54\" class=\"edge\">\n", "<title>53&#45;&gt;54</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-736.5C106,-728.72 106,-719.7 106,-711.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-711.36 106,-701.36 102.5,-711.36 109.5,-711.36\"/>\n", "</g>\n", "<!-- 55 -->\n", "<g id=\"node56\" class=\"node\">\n", "<title>55</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"170,-620.4 42,-620.4 42,-576.4 170,-576.4 170,-620.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42,-576.4 42,-620.4 81,-620.4 81,-576.4 42,-576.4\"/>\n", "<text text-anchor=\"start\" x=\"48.45\" y=\"-601.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-589.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-598.4 81,-620.4 121,-620.4 121,-598.4 81,-598.4\"/>\n", "<text text-anchor=\"start\" x=\"89.33\" y=\"-606.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-598.4 121,-620.4 170,-620.4 170,-598.4 121,-598.4\"/>\n", "<text text-anchor=\"start\" x=\"125.92\" y=\"-606.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 2048) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-576.4 81,-598.4 121,-598.4 121,-576.4 81,-576.4\"/>\n", "<text text-anchor=\"start\" x=\"85.58\" y=\"-584.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-576.4 121,-598.4 170,-598.4 170,-576.4 121,-576.4\"/>\n", "<text text-anchor=\"start\" x=\"128.42\" y=\"-584.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 54&#45;&gt;55 -->\n", "<g id=\"edge55\" class=\"edge\">\n", "<title>54&#45;&gt;55</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-656.5C106,-648.72 106,-639.7 106,-631.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-631.36 106,-621.36 102.5,-631.36 109.5,-631.36\"/>\n", "</g>\n", "<!-- 56 -->\n", "<g id=\"node57\" class=\"node\">\n", "<title>56</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"169.5,-540.4 42.5,-540.4 42.5,-496.4 169.5,-496.4 169.5,-540.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42.5,-496.4 42.5,-540.4 85.5,-540.4 85.5,-496.4 42.5,-496.4\"/>\n", "<text text-anchor=\"start\" x=\"47.33\" y=\"-521.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-509.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"85.5,-518.4 85.5,-540.4 125.5,-540.4 125.5,-518.4 85.5,-518.4\"/>\n", "<text text-anchor=\"start\" x=\"93.83\" y=\"-526.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"125.5,-518.4 125.5,-540.4 169.5,-540.4 169.5,-518.4 125.5,-518.4\"/>\n", "<text text-anchor=\"start\" x=\"130.42\" y=\"-526.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"85.5,-496.4 85.5,-518.4 125.5,-518.4 125.5,-496.4 85.5,-496.4\"/>\n", "<text text-anchor=\"start\" x=\"90.08\" y=\"-504.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"125.5,-496.4 125.5,-518.4 169.5,-518.4 169.5,-496.4 125.5,-496.4\"/>\n", "<text text-anchor=\"start\" x=\"130.42\" y=\"-504.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 55&#45;&gt;56 -->\n", "<g id=\"edge56\" class=\"edge\">\n", "<title>55&#45;&gt;56</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-576.5C106,-568.72 106,-559.7 106,-551.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-551.36 106,-541.36 102.5,-551.36 109.5,-551.36\"/>\n", "</g>\n", "<!-- 57 -->\n", "<g id=\"node58\" class=\"node\">\n", "<title>57</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"181,-460.4 31,-460.4 31,-416.4 181,-416.4 181,-460.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"31,-416.4 31,-460.4 97,-460.4 97,-416.4 31,-416.4\"/>\n", "<text text-anchor=\"start\" x=\"35.67\" y=\"-441.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-429.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"97,-438.4 97,-460.4 137,-460.4 137,-438.4 97,-438.4\"/>\n", "<text text-anchor=\"start\" x=\"105.33\" y=\"-446.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"137,-438.4 137,-460.4 181,-460.4 181,-438.4 137,-438.4\"/>\n", "<text text-anchor=\"start\" x=\"141.92\" y=\"-446.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"97,-416.4 97,-438.4 137,-438.4 137,-416.4 97,-416.4\"/>\n", "<text text-anchor=\"start\" x=\"101.58\" y=\"-424.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"137,-416.4 137,-438.4 181,-438.4 181,-416.4 137,-416.4\"/>\n", "<text text-anchor=\"start\" x=\"141.92\" y=\"-424.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "</g>\n", "<!-- 56&#45;&gt;57 -->\n", "<g id=\"edge57\" class=\"edge\">\n", "<title>56&#45;&gt;57</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-496.5C106,-488.72 106,-479.7 106,-471.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-471.36 106,-461.36 102.5,-471.36 109.5,-471.36\"/>\n", "</g>\n", "<!-- 58 -->\n", "<g id=\"node59\" class=\"node\">\n", "<title>58</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"170,-380.4 42,-380.4 42,-336.4 170,-336.4 170,-380.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42,-336.4 42,-380.4 81,-380.4 81,-336.4 42,-336.4\"/>\n", "<text text-anchor=\"start\" x=\"48.45\" y=\"-361.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-349.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-358.4 81,-380.4 121,-380.4 121,-358.4 81,-358.4\"/>\n", "<text text-anchor=\"start\" x=\"89.33\" y=\"-366.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-358.4 121,-380.4 170,-380.4 170,-358.4 121,-358.4\"/>\n", "<text text-anchor=\"start\" x=\"128.42\" y=\"-366.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 512) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-336.4 81,-358.4 121,-358.4 121,-336.4 81,-336.4\"/>\n", "<text text-anchor=\"start\" x=\"85.58\" y=\"-344.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-336.4 121,-358.4 170,-358.4 170,-336.4 121,-336.4\"/>\n", "<text text-anchor=\"start\" x=\"125.92\" y=\"-344.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024) </text>\n", "</g>\n", "<!-- 57&#45;&gt;58 -->\n", "<g id=\"edge58\" class=\"edge\">\n", "<title>57&#45;&gt;58</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-416.5C106,-408.72 106,-399.7 106,-391.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-391.36 106,-381.36 102.5,-391.36 109.5,-391.36\"/>\n", "</g>\n", "<!-- 59 -->\n", "<g id=\"node60\" class=\"node\">\n", "<title>59</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"172,-300.4 40,-300.4 40,-256.4 172,-256.4 172,-300.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"40,-256.4 40,-300.4 83,-300.4 83,-256.4 40,-256.4\"/>\n", "<text text-anchor=\"start\" x=\"44.83\" y=\"-281.4\" font-family=\"Linux libertine\" font-size=\"10.00\">Softplus</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-269.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83,-278.4 83,-300.4 123,-300.4 123,-278.4 83,-278.4\"/>\n", "<text text-anchor=\"start\" x=\"91.33\" y=\"-286.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123,-278.4 123,-300.4 172,-300.4 172,-278.4 123,-278.4\"/>\n", "<text text-anchor=\"start\" x=\"127.92\" y=\"-286.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"83,-256.4 83,-278.4 123,-278.4 123,-256.4 83,-256.4\"/>\n", "<text text-anchor=\"start\" x=\"87.58\" y=\"-264.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"123,-256.4 123,-278.4 172,-278.4 172,-256.4 123,-256.4\"/>\n", "<text text-anchor=\"start\" x=\"127.92\" y=\"-264.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024) </text>\n", "</g>\n", "<!-- 58&#45;&gt;59 -->\n", "<g id=\"edge59\" class=\"edge\">\n", "<title>58&#45;&gt;59</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-336.5C106,-328.72 106,-319.7 106,-311.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-311.36 106,-301.36 102.5,-311.36 109.5,-311.36\"/>\n", "</g>\n", "<!-- 60 -->\n", "<g id=\"node61\" class=\"node\">\n", "<title>60</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"183.5,-220.4 28.5,-220.4 28.5,-176.4 183.5,-176.4 183.5,-220.4\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"28.5,-176.4 28.5,-220.4 94.5,-220.4 94.5,-176.4 28.5,-176.4\"/>\n", "<text text-anchor=\"start\" x=\"33.17\" y=\"-201.4\" font-family=\"Linux libertine\" font-size=\"10.00\">BatchNorm1d</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-189.4\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"94.5,-198.4 94.5,-220.4 134.5,-220.4 134.5,-198.4 94.5,-198.4\"/>\n", "<text text-anchor=\"start\" x=\"102.83\" y=\"-206.4\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"134.5,-198.4 134.5,-220.4 183.5,-220.4 183.5,-198.4 134.5,-198.4\"/>\n", "<text text-anchor=\"start\" x=\"139.42\" y=\"-206.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"94.5,-176.4 94.5,-198.4 134.5,-198.4 134.5,-176.4 94.5,-176.4\"/>\n", "<text text-anchor=\"start\" x=\"99.08\" y=\"-184.4\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"134.5,-176.4 134.5,-198.4 183.5,-198.4 183.5,-176.4 134.5,-176.4\"/>\n", "<text text-anchor=\"start\" x=\"139.42\" y=\"-184.4\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024) </text>\n", "</g>\n", "<!-- 59&#45;&gt;60 -->\n", "<g id=\"edge60\" class=\"edge\">\n", "<title>59&#45;&gt;60</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-256.5C106,-248.72 106,-239.7 106,-231.16\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-231.36 106,-221.36 102.5,-231.36 109.5,-231.36\"/>\n", "</g>\n", "<!-- 61 -->\n", "<g id=\"node62\" class=\"node\">\n", "<title>61</title>\n", "<polygon fill=\"#c1ffc1\" stroke=\"none\" points=\"170,-130 42,-130 42,-86 170,-86 170,-130\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"42,-86 42,-130 81,-130 81,-86 42,-86\"/>\n", "<text text-anchor=\"start\" x=\"48.45\" y=\"-111\" font-family=\"Linux libertine\" font-size=\"10.00\">Linear</text>\n", "<text text-anchor=\"start\" x=\"46.5\" y=\"-99\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:2</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-108 81,-130 121,-130 121,-108 81,-108\"/>\n", "<text text-anchor=\"start\" x=\"89.33\" y=\"-116\" font-family=\"Linux libertine\" font-size=\"10.00\">input:</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-108 121,-130 170,-130 170,-108 121,-108\"/>\n", "<text text-anchor=\"start\" x=\"125.92\" y=\"-116\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 1024) </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"81,-86 81,-108 121,-108 121,-86 81,-86\"/>\n", "<text text-anchor=\"start\" x=\"85.58\" y=\"-94\" font-family=\"Linux libertine\" font-size=\"10.00\">output: </text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"121,-86 121,-108 170,-108 170,-86 121,-86\"/>\n", "<text text-anchor=\"start\" x=\"130.92\" y=\"-94\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 10) </text>\n", "</g>\n", "<!-- 60&#45;&gt;61 -->\n", "<g id=\"edge61\" class=\"edge\">\n", "<title>60&#45;&gt;61</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-176.45C106,-165.77 106,-152.56 106,-140.67\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-140.8 106,-130.8 102.5,-140.8 109.5,-140.8\"/>\n", "</g>\n", "<!-- 62 -->\n", "<g id=\"node63\" class=\"node\">\n", "<title>62</title>\n", "<polygon fill=\"lightyellow\" stroke=\"none\" points=\"155.99,-50 56.01,-50 56.01,-16 155.99,-16 155.99,-50\"/>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"56.01,-16 56.01,-50 119.33,-50 119.33,-16 56.01,-16\"/>\n", "<text text-anchor=\"start\" x=\"61.01\" y=\"-36\" font-family=\"Linux libertine\" font-size=\"10.00\">output&#45;tensor</text>\n", "<text text-anchor=\"start\" x=\"72.67\" y=\"-24\" font-family=\"Linux libertine\" font-size=\"10.00\">depth:0</text>\n", "<polygon fill=\"none\" stroke=\"black\" points=\"119.33,-16 119.33,-50 155.99,-50 155.99,-16 119.33,-16\"/>\n", "<text text-anchor=\"start\" x=\"124.33\" y=\"-30\" font-family=\"Linux libertine\" font-size=\"10.00\">(1, 10)</text>\n", "</g>\n", "<!-- 61&#45;&gt;62 -->\n", "<g id=\"edge62\" class=\"edge\">\n", "<title>61&#45;&gt;62</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M106,-86.28C106,-78.28 106,-69.03 106,-60.55\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"109.5,-60.67 106,-50.67 102.5,-60.67 109.5,-60.67\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x1cbfefb5040>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import torchvision\n", "from torchview import draw_graph\n", "net2 = CIFAR10_CNN()\n", "\n", "#model_graph = draw_graph(net2, input_size=(1,3,64,64), expand_nested=True)\n", "model_graph = draw_graph(net2, input_size=(1,3,32,32), expand_nested=True)\n", "model_graph.visual_graph\n"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Pytorch MNIST.ipynb", "provenance": []}, "kernelspec": {"display_name": "torchenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 0}