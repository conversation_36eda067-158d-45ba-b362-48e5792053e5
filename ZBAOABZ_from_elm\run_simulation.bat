@echo off
echo Compiling ZBAOABZ C++ code...
g++ -std=c++17 -O3 -march=native -Wall -Wextra -o zbaoabz.exe zbaoabz_cpp.cpp

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Running simulation...
zbaoabz.exe

if %ERRORLEVEL% NEQ 0 (
    echo Simulation failed!
    pause
    exit /b 1
)

echo.
echo Simulation completed successfully!
echo.
echo To generate plots, run: python plot_results.py
echo.
pause 