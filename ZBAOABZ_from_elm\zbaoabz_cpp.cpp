#include <iostream>
#include <vector>
#include <cmath>
#include <random>
#include <fstream>
#include <algorithm>
#include <iomanip>
#include <chrono>

// Constants
const double PI = 3.14159265358979323846;

// Random number generator
std::random_device rd;
std::mt19937 gen(rd());
std::normal_distribution<double> normal_dist(0.0, 1.0);

// Vector2D class for 2D vectors
struct Vector2D {
    double x, y;
    
    Vector2D() : x(0.0), y(0.0) {}
    Vector2D(double x_, double y_) : x(x_), y(y_) {}
    
    Vector2D operator+(const Vector2D& other) const {
        return Vector2D(x + other.x, y + other.y);
    }
    
    Vector2D operator-(const Vector2D& other) const {
        return Vector2D(x - other.x, y - other.y);
    }
    
    Vector2D operator*(double scalar) const {
        return Vector2D(x * scalar, y * scalar);
    }
    
    double norm() const {
        return std::abs(x) + std::abs(y); // L1 norm as in Python code
    }
};

// BAOAB Constants class
class BAOAB_Constants {
public:
    double eta, xc1, xc2, xc3, vc1, vc2, vc3;
    
    BAOAB_Constants(double h, double gamma_val) {
        double h_ = h;
        double gam_ = gamma_val;
        double gh = gam_ * h_;
        
        // exponential factor
        eta = std::exp(-gh);
        
        // Terms
        xc1 = 0.5 * h_ * (1 + eta);
        xc2 = 0.25 * h_ * h_ * (1 + eta);
        // xc2 = 0.5 * h_ * h_ * (1 + eta);
        
        // sqrt(1 - e^{-2gh})
        double tmp = -std::expm1(-2.0 * gh);
        tmp = std::max(tmp, 1e-16);  // guard
        xc3 = 0.5 * h_ * std::sqrt(tmp);
        
        vc1 = eta * 0.5 * h_;
        vc2 = 0.5 * h_;
        vc3 = std::sqrt(tmp);
        // vc3 = std::sqrt(tmp) * 0.5;
    }
};

// 2D Neal's Funnel potential
double U_funnel_2d(const Vector2D& params, double eps = 0.1) {
    double x = params.x;
    double theta = params.y;
    return 0.5 * x * x * std::exp(-theta) + 0.5 * eps * (x * x + theta * theta);
}

// 2D Neal's Funnel potential gradient
Vector2D grad_funnel_2d(const Vector2D& params, double eps = 0.1) {
    double x = params.x;
    double theta = params.y;
    
    double gx = x * std::exp(-theta) + eps * x;
    double gtheta = -0.5 * x * x * std::exp(-theta) + eps * theta;
    
    return Vector2D(gx, gtheta);
}

// Sundman transform kernel
double psi_of_zeta(double z, double m = 0.1, double M = 10.0, double r = 0.5) {
    // clamp z so it's never negative or zero
    // if (z < 1e-14) {
    //     z = 1e-14;
    // }
    double zr = std::pow(z, r);
    return m * (zr + M/m) / (zr + 1);
}

// Half-step update for z
double z_half_step(double z_old, double alpha, double half_dtau, double g_val) {
    double rho = std::exp(-alpha * half_dtau);
    return rho * z_old + (1.0 - rho) / alpha * g_val;
}

// Monitor function g
double monitor_g(const Vector2D& x, const Vector2D& v, const Vector2D& gradU, double scale_g) {
    double gn = gradU.norm();
    return gn / scale_g;
}

// BAOAB single step
void BAOAB_single_step(Vector2D& x, Vector2D& v, Vector2D& gradU, 
                       const BAOAB_Constants& bconst, double eps = 0.1) {
    // Random noise
    Vector2D xi(normal_dist(gen), normal_dist(gen));
    
    // A step: x update
    Vector2D x_new = x + v * bconst.xc1 - gradU * bconst.xc2 + xi * bconst.xc3;
    
    // fetch new grad
    Vector2D gradU_new = grad_funnel_2d(x_new, eps);
    
    // B+O step: v update
    Vector2D v_new = v * bconst.eta - gradU * bconst.vc1 - gradU_new * bconst.vc2 + xi * bconst.vc3;
    
    // update
    x = x_new;
    v = v_new;
    gradU = gradU_new;
}

// Main SamAdams (ZBAOABZ) routine
struct SimulationResult {
    std::vector<Vector2D> X_traj;
    std::vector<double> Z_traj;
    std::vector<int> steps;
};

// SimulationResult SamAdams_ZBAOABZ_funnel2d(
//     const Vector2D& x_init, const Vector2D& v_init, double z0,
//     int nmax, int nmeas, double dtau,
//     double gamma_val = 1.0, double alpha = 0.1, double eps = 0.1,
//     double scale_g = 1.0, double m = 0.1, double M = 10.0, double r = 0.5) {
    
//     Vector2D x = x_init;
//     Vector2D v = v_init;
//     Vector2D gradU = grad_funnel_2d(x, eps);
    
//     double zeta = z0;
//     double half_dtau = 0.5 * dtau;
    
//     // arrays to store
//     int store_size = (nmax / nmeas) + 1;
//     std::vector<Vector2D> X_store;
//     std::vector<double> Z_store;
//     std::vector<int> steps_store;
    
//     X_store.reserve(store_size);
//     Z_store.reserve(store_size);
//     steps_store.reserve(store_size);
    
//     int count = 0;
//     X_store.push_back(x);
//     Z_store.push_back(zeta);
//     steps_store.push_back(0);
    
//     for (int step_i = 0; step_i < nmax; ++step_i) {
//         // 1) first half-step in ζ
//         double g_val = monitor_g(x, v, gradU, scale_g);
//         double z_half = z_half_step(zeta, alpha, half_dtau, g_val);
        
//         // 2) compute real stepsize = ψ(z_half)*∆τ
//         double psival = psi_of_zeta(z_half, m, M, r);
//         double real_dt = psival * dtau;
        
//         // 3) BAOAB step
//         BAOAB_Constants bconst(real_dt, gamma_val);
//         BAOAB_single_step(x, v, gradU, bconst, eps);
        
//         // 4) second half-step in ζ
//         double g_val2 = monitor_g(x, v, gradU, scale_g);
//         double z_new = z_half_step(z_half, alpha, half_dtau, g_val2);
        
//         // update zeta
//         zeta = z_new;
        
//         // store if needed
//         if ((step_i + 1) % nmeas == 0) {
//             X_store.push_back(x);
//             Z_store.push_back(zeta);
//             steps_store.push_back(step_i + 1);
//         }
//     }
    
//     return {X_store, Z_store, steps_store};
// }

SimulationResult SamAdams_ZBAOABZ_funnel2d(
    const Vector2D& x_init, const Vector2D& v_init, double z0,
    int nmax, int nmeas, double dtau,
    double gamma_val = 1.0, double alpha = 0.1, double eps = 0.1,
    double scale_g = 1.0, double m = 0.1, double M = 10.0, double r = 0.5) {
    
    Vector2D x = x_init;
    Vector2D v = v_init;
    Vector2D gradU = grad_funnel_2d(x, eps);
    
    double zeta = z0;
    double half_dtau = 0.5 * dtau;
    double g_val = monitor_g(x, v, gradU, scale_g);
    
    // arrays to store
    int store_size = (nmax / nmeas) + 1;
    std::vector<Vector2D> X_store;
    std::vector<double> Z_store;
    std::vector<int> steps_store;
    
    X_store.reserve(store_size);
    Z_store.reserve(store_size);
    steps_store.reserve(store_size);
    
    int count = 0;
    X_store.push_back(x);
    Z_store.push_back(zeta);
    steps_store.push_back(0);
    
    for (int step_i = 0; step_i < nmax; ++step_i) {
        // 1) first half-step in ζ        
        double z_half = z_half_step(zeta, alpha, half_dtau, g_val);
        
        // 2) compute real stepsize = ψ(z_half)*∆τ
        double psival = psi_of_zeta(z_half, m, M, r);
        double real_dt = psival * dtau;
        
        // 3) BAOAB step
        BAOAB_Constants bconst(real_dt, gamma_val);
        BAOAB_single_step(x, v, gradU, bconst, eps);
        
        // 4) second half-step in ζ
        double g_val = monitor_g(x, v, gradU, scale_g);
        double z_new = z_half_step(z_half, alpha, half_dtau, g_val);
        
        // update zeta
        zeta = z_new;
        
        // store if needed
        if ((step_i + 1) % nmeas == 0) {
            X_store.push_back(x);
            Z_store.push_back(zeta);
            steps_store.push_back(step_i + 1);
        }
    }
    
    return {X_store, Z_store, steps_store};
}

// Function to save data for plotting
void save_data_for_plotting(const SimulationResult& result, 
                           double dtau, double m, double M, double r) {
    std::ofstream file("zbaoabz_results.txt");
    if (!file.is_open()) {
        std::cerr << "Error: Could not open file for writing" << std::endl;
        return;
    }
    
    file << "# x theta zeta psi U" << std::endl;
    
    for (size_t i = 0; i < result.X_traj.size(); ++i) {
        double x = result.X_traj[i].x;
        double theta = result.X_traj[i].y;
        double zeta = result.Z_traj[i];
        double psi = psi_of_zeta(zeta, m, M, r);
        double U = U_funnel_2d(result.X_traj[i], 0.1);
        
        file << std::setprecision(15) << x << " " << theta << " " << zeta << " " << psi << " " << U << std::endl;
    }
    
    file.close();
    std::cout << "Data saved to zbaoabz_results.txt" << std::endl;
}

// Function to generate Python plotting script
void generate_plotting_script() {
    std::ofstream file("plot_results.py");
    if (!file.is_open()) {
        std::cerr << "Error: Could not open plotting script file" << std::endl;
        return;
    }
    
    file << "import numpy as np\n";
    file << "import matplotlib.pyplot as plt\n\n";
    file << "# Load data\n";
    file << "data = np.loadtxt('zbaoabz_results.txt')\n";
    file << "x_vals = data[:, 0]\n";
    file << "theta_vals = data[:, 1]\n";
    file << "zeta_vals = data[:, 2]\n";
    file << "dt_vals = data[:, 3]\n\n";
    file << "# Create plots\n";
    file << "fig, axs = plt.subplots(1, 2, figsize=(12, 5))\n\n";
    file << "# Left plot: trajectory in x-theta space\n";
    file << "axs[0].plot(x_vals, theta_vals, 'b.', alpha=0.3, markersize=1)\n";
    file << "axs[0].set_title(\"SamAdams trajectory in Neal's Funnel\")\n";
    file << "axs[0].set_xlabel(\"x\")\n";
    file << "axs[0].set_ylabel(\"theta\")\n";
    file << "axs[0].grid(True, alpha=0.3)\n\n";
    file << "# Right plot: adaptive stepsize distribution\n";
    file << "axs[1].hist(dt_vals, bins=50, density=True, alpha=0.7, color='blue')\n";
    file << "mean_dt = np.mean(dt_vals)\n";
    file << "axs[1].axvline(x=mean_dt, color='red', label=f\"mean ∆t = {mean_dt:.6f}\")\n";
    file << "axs[1].set_title(\"Histogram of adaptive ∆t\")\n";
    file << "axs[1].set_xlabel(\"stepsize ∆t\")\n";
    file << "axs[1].set_ylabel(\"density\")\n";
    file << "axs[1].legend()\n";
    file << "axs[1].grid(True, alpha=0.3)\n\n";
    file << "plt.tight_layout()\n";
    file << "plt.savefig('zbaoabz_results.png', dpi=300, bbox_inches='tight')\n";
    file << "plt.show()\n\n";
    file << "print(f\"Total samples: {len(x_vals)}\")\n";
    file << "print(f\"Mean stepsize: {mean_dt:.6f}\")\n";
    file << "print(f\"Min stepsize: {np.min(dt_vals):.6f}\")\n";
    file << "print(f\"Max stepsize: {np.max(dt_vals):.6f}\")\n";
    
    file.close();
    std::cout << "Python plotting script generated: plot_results.py" << std::endl;
}

int main() {
    std::cout << "Starting ZBAOABZ simulation for 2D Neal's Funnel..." << std::endl;
    
    // Initial conditions in the wide part
    Vector2D x0(0.0, 5.0);
    Vector2D v0(0.0, 0.0);
    double z0 = 0.0;
    
    // Parameters
    double alpha_in = 0.1;      // slow relaxation for z
    double dtau_in = 0.01;
    double gamma_in = 5.0;
    double eps_in = 0.1;
    double scale_gin = 1.0;
    double m_in = 0.01, M_in = 60.0, r_in = 0.5;
    
    // For faster testing, use smaller values
    int nmax = 10000000;  // 1M steps instead of 10M
    int nmeas = 100;    // sample every 1000 steps
    
    std::cout << "Parameters:" << std::endl;
    std::cout << "  nmax = " << nmax << std::endl;
    std::cout << "  nmeas = " << nmeas << std::endl;
    std::cout << "  dtau = " << dtau_in << std::endl;
    std::cout << "  gamma = " << gamma_in << std::endl;
    std::cout << "  alpha = " << alpha_in << std::endl;
    std::cout << "  eps = " << eps_in << std::endl;
    std::cout << "  m = " << m_in << ", M = " << M_in << ", r = " << r_in << std::endl;
    
    // Run simulation
    std::cout << "\nRunning simulation..." << std::endl;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    SimulationResult result = SamAdams_ZBAOABZ_funnel2d(
        x0, v0, z0, nmax, nmeas, dtau_in,
        gamma_in, alpha_in, eps_in, scale_gin, m_in, M_in, r_in
    );
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "Simulation completed in " << duration.count() << " ms" << std::endl;
    std::cout << "Collected " << result.X_traj.size() << " samples" << std::endl;
    
    // Save results
    save_data_for_plotting(result, dtau_in, m_in, M_in, r_in);
    generate_plotting_script();
    
    std::cout << "\nTo generate plots, run: python plot_results.py" << std::endl;
    
    return 0;
} 