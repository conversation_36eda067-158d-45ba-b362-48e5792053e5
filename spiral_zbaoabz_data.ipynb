{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["epoch 3999/4000, step 20/20, test loss = 0.1424, train acc = 92.00%, test acc = 94.00%\n", "epoch 3999/4000, step 20/20, test loss = 4.0548, train acc = 88.00%, test acc = 93.70%\n", "epoch 3999/4000, step 20/20, test loss = 0.1691, train acc = 96.00%, test acc = 92.40%\n", "epoch 3999/4000, step 20/20, test loss = 0.3438, train acc = 80.00%, test acc = 79.10%\n", "epoch 3999/4000, step 20/20, test loss = 5.7138, train acc = 88.00%, test acc = 87.50%\n", "epoch 3999/4000, step 20/20, test loss = 0.3533, train acc = 80.00%, test acc = 74.00%\n", "epoch 3999/4000, step 20/20, test loss = 0.1534, train acc = 96.00%, test acc = 91.90%\n", "epoch 3999/4000, step 20/20, test loss = 4.0539, train acc = 92.00%, test acc = 83.90%\n", "epoch 3999/4000, step 20/20, test loss = 0.0043, train acc = 100.00%, test acc = 99.90%\n", "epoch 3999/4000, step 20/20, test loss = 0.3325, train acc = 68.00%, test acc = 78.90%\n", "epoch 3999/4000, step 20/20, test loss = 4.0824, train acc = 96.00%, test acc = 92.90%\n", "epoch 3999/4000, step 20/20, test loss = 0.2674, train acc = 80.00%, test acc = 83.40%\n", "epoch 3999/4000, step 20/20, test loss = 0.1881, train acc = 96.00%, test acc = 88.40%\n", "epoch 3999/4000, step 20/20, test loss = 0.0661, train acc = 100.00%, test acc = 98.60%\n", "epoch 3999/4000, step 20/20, test loss = 0.3014, train acc = 96.00%, test acc = 87.10%\n", "epoch 3999/4000, step 20/20, test loss = 0.7617, train acc = 96.00%, test acc = 94.40%\n", "epoch 3999/4000, step 20/20, test loss = 4.1794, train acc = 92.00%, test acc = 83.90%\n", "epoch 3999/4000, step 20/20, test loss = 0.3395, train acc = 60.00%, test acc = 79.20%\n", "epoch 3999/4000, step 20/20, test loss = 4.0713, train acc = 92.00%, test acc = 94.10%\n", "epoch 3999/4000, step 20/20, test loss = 0.0854, train acc = 100.00%, test acc = 97.90%\n", "epoch 3999/4000, step 20/20, test loss = 2.3779, train acc = 96.00%, test acc = 96.30%\n", "epoch 3999/4000, step 20/20, test loss = 0.2576, train acc = 76.00%, test acc = 81.20%\n", "epoch 3999/4000, step 20/20, test loss = 0.1515, train acc = 100.00%, test acc = 96.00%\n", "epoch 3999/4000, step 20/20, test loss = 0.1347, train acc = 96.00%, test acc = 91.20%\n", "epoch 3999/4000, step 20/20, test loss = 2.3690, train acc = 76.00%, test acc = 86.60%\n", "epoch 3999/4000, step 20/20, test loss = 3.5482, train acc = 100.00%, test acc = 94.10%\n", "epoch 3999/4000, step 20/20, test loss = 0.1795, train acc = 96.00%, test acc = 91.90%\n", "epoch 3999/4000, step 20/20, test loss = 3.8597, train acc = 92.00%, test acc = 91.50%\n", "epoch 3999/4000, step 20/20, test loss = 0.4849, train acc = 96.00%, test acc = 95.60%\n", "epoch 3999/4000, step 20/20, test loss = 2.3054, train acc = 100.00%, test acc = 94.20%\n", "epoch 3999/4000, step 20/20, test loss = 5.0124, train acc = 84.00%, test acc = 92.80%\n", "epoch 3999/4000, step 20/20, test loss = 0.3954, train acc = 76.00%, test acc = 81.10%\n", "epoch 3999/4000, step 20/20, test loss = 3.6771, train acc = 88.00%, test acc = 88.10%\n", "epoch 3999/4000, step 20/20, test loss = 0.0578, train acc = 100.00%, test acc = 97.80%\n", "epoch 3999/4000, step 20/20, test loss = 0.8083, train acc = 100.00%, test acc = 96.20%\n", "epoch 3999/4000, step 20/20, test loss = 3.8786, train acc = 96.00%, test acc = 88.70%\n", "epoch 3999/4000, step 20/20, test loss = 0.1514, train acc = 84.00%, test acc = 90.60%\n", "epoch 3999/4000, step 20/20, test loss = 0.2333, train acc = 84.00%, test acc = 85.50%\n", "epoch 3999/4000, step 20/20, test loss = 0.4158, train acc = 84.00%, test acc = 77.90%\n", "epoch 3999/4000, step 20/20, test loss = 0.2214, train acc = 84.00%, test acc = 88.10%\n", "epoch 3999/4000, step 20/20, test loss = 0.4337, train acc = 80.00%, test acc = 89.40%\n", "\n", "epoch 3999/4000, step 20/20, test loss = 0.5332, train acc = 64.00%, test acc = 77.10%\n", "epoch 3999/4000, step 20/20, test loss = 0.9510, train acc = 80.00%, test acc = 86.10%\n", "epoch 3999/4000, step 20/20, test loss = 6.4249, train acc = 64.00%, test acc = 72.70%\n", "epoch 3999/4000, step 20/20, test loss = 0.6412, train acc = 84.00%, test acc = 68.40%\n", "epoch 3999/4000, step 20/20, test loss = 0.4960, train acc = 64.00%, test acc = 71.30%\n", "epoch 3999/4000, step 20/20, test loss = 0.2091, train acc = 96.00%, test acc = 87.60%\n", "epoch 3999/4000, step 20/20, test loss = 10.3375, train acc = 92.00%, test acc = 88.30%\n", "epoch 3999/4000, step 20/20, test loss = 5.8781, train acc = 92.00%, test acc = 85.40%\n", "epoch 3999/4000, step 20/20, test loss = 6.3416, train acc = 88.00%, test acc = 89.40%\n", "\n", "epoch 3999/4000, test loss = 1.0097, train acc = 85.71%, test acc = 70.80%\n", "epoch 3999/4000, test loss = 0.7169, train acc = 100.00%, test acc = 74.50%"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# --- Run ZBAOABZ on the two-spirals dataset (mirrors your AdLaLa loop) ---\n", "\n", "# Hyperparameters (good starting points for spirals)\n", "dtau         = 4e-2          # \"slow time\" step (<PERSON><PERSON> base)\n", "weight_decay = 0.0          # set >0 (e.g., 1e-5) if you want extra smoothing\n", "gamma        = 1.0           # OU friction\n", "alpha        = 5.0          # ζ controller strength\n", "alpha2       = 1.0 / Ntrain  # Ω^{-1} scale; for MNIST we used 1/60000; here use 1/Ntrain\n", "m, M         = 0.1, 10.0     # <PERSON><PERSON> params\n", "temperature  = 1e-4         # OU temperature (akin to τ); try 1e-4 … 1e-3\n", "r_exp        = 0.25          # exponent in ζ^r\n", "num_epochs   = 4000\n", "\n", "NN = Net()\n", "criterion = nn.BCE<PERSON>oss()            # matches your sigmoid output\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau,\n", "    weight_decay=weight_decay,\n", "    gamma=gamma,\n", "    alpha=alpha,\n", "    alpha2=alpha2,\n", "    m=m,\n", "    M=M,\n", "    temperature=temperature,\n", "    r=r_exp\n", ")\n", "\n", "RES = []\n", "dt_trace = []          # every iteration (batch)\n", "tkin_trace = []        # every eval\n", "tconf_trace = []       # every eval\n", "eval_epoch_idx = []    # x-axis for Tkin/Tconf plots\n", "\n", "total_steps = len(dataloader)\n", "\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "    for i, (x, y) in enumerate(dataloader):\n", "        # BCELoss expects float targets with sigmoid output\n", "        y = y.float().unsqueeze(1)\n", "\n", "        # ---- PRE (<PERSON> <PERSON> + <PERSON><PERSON> -> sets adaptive lr) ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(x)\n", "        loss = criterion(out, y) * len(x)   # keep \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # track current adaptive step size (per iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        \n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(x)\n", "        loss = criterion(out, y) * len(x)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (Z step + <PERSON><PERSON> for next iter) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- periodic eval (same cadence as your AdLaLa example) ----\n", "        if (epoch + 1) % 10 == 0 and (i + 1) % (Ntrain / batchsize) == 0:\n", "            with torch.no_grad():\n", "                # acc = accuracy_Nclass(out.detach().numpy(), y.detach().numpy())\n", "                acc = accuracy_Nclass(out.detach().cpu().numpy(), y.detach().cpu().numpy().squeeze())\n", "                out_test = NN(xtest)\n", "                loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "                acc_test = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "            RES.append([epoch, loss.item() / len(x), acc, loss_test.item(), acc_test])\n", "\n", "            # measure temperatures right after backward() so grads are present\n", "            <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "            tkin_trace.append(Tkin)\n", "            tconf_trace.append(Tconf)\n", "            eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, step {i+1}/{total_steps}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBAOABZ = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "\n", "\n", "plt.figure(figsize=[17,5]) # Increase the size of the plots\n", "plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "\n", "# Plot the training and test loss for a neural network trained using Adam\n", "plt.subplot(1,3,1)\n", "plt.plot( RES_ZBAOABZ[:,0]+1,RES_ZBAOABZ[:,1],label='Training loss')\n", "plt.plot( RES_ZBAOABZ[:,0]+1,RES_ZBAOABZ[:,3],label='Test loss')\n", "plt.legend()\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.grid()\n", "plt.ylim([0,0.8])\n", "plt.title(r'ZBAOABZ Loss function (T=$10^{-4}$)')\n", "plt.tight_layout()\n", "\n", "# Plot the accuracy of the classifier on the training and test dataset\n", "plt.subplot(1,3,2)\n", "plt.plot( RES_ZBAOABZ[:,0]+1,RES_ZBAOABZ[:,2],label='Training set accuracy')\n", "plt.plot( RES_ZBAOABZ[:,0]+1,RES_ZBAOABZ[:,4],label='Test set accuracy')\n", "plt.legend()\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy (in %)')\n", "plt.title(r'ZBAOABZ Prediction Accuracy (T=$10^{-4}$)')\n", "plt.grid()\n", "plt.ylim([40,100])\n", "plt.tight_layout()\n", "\n", "# Plot the classification plane for the current state of the neural network\n", "plt.subplot(1,3,3)\n", "xx = np.linspace(-2,2,100)\n", "X,Y = np.meshgrid(xx,xx)\n", "Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "out = NN(torch.tensor(Z.T).float())\n", "\n", "out=out.reshape(100,100)\n", "plt.pcolor(xx,xx,out.data)\n", "plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "plt.axis('square')\n", "\n", "kk1 = ytest.reshape(-1)==0\n", "kk2 = ytest.reshape(-1)==1\n", "plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["****"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import numpy as np\n", "import torch \n", "import torch.nn as nn\n", "from torch.utils.data import DataLoader, Dataset\n", "torch.manual_seed(0)\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def twospirals(datapoints, spiralturns = 3, noise=.02, p = 0.5, a = 2):\n", "    \n", "    \"\"\"\n", "     Creates a two spiral planar dataset consisting of the 2D coordinates (X) of the datapoints \n", "     and the corresponding labels (Y).\n", "     The user can set the number of datapoints (N), the number of turns of the spiral (default = 4) \n", "     and the noise level (default = 0.02).\n", "    \"\"\"\n", "    \n", "    N = int(datapoints/2)  \n", "    \n", "    # Generate a (N,1) array with samples from the uniform distribution over [0,1)\n", "    t = np.random.rand(N,1)\n", "    \n", "    # Generate noise-free training data\n", "    dx1 = a*(t**p)*np.cos(2*spiralturns*(t**p)*np.pi) \n", "    dx2 = a*(t**p)*np.sin(2*spiralturns*(t**p)*np.pi)\n", "    \n", "    X = np.vstack((np.hstack((dx1,dx2)),np.hstack((-dx1,-dx2)))) + np.random.randn( 2*N,2) * noise # Coordinates\n", "    Y = np.hstack((np.zeros(N),np.ones(N))) # Corresponding Labels\n", "    \n", "    return torch.Tensor(X),torch.Tensor(Y)  # Return data + labels as torch.Tensors"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["Ntest = 1000  # Set the number of test data points\n", "xtest,ytest = twospirals(Ntest) # Create the test data\n", "xtestplot = xtest.detach().numpy()\n", "ytestplot = ytest.detach().numpy()\n", "plt.rcParams.update({'font.size': 14})\n", "plt.title('Test Data')\n", "plt.plot(xtestplot[ytestplot==0,0], xtestplot[ytestplot==0,1], '.', label='Class A',color='red')\n", "plt.plot(xtestplot[ytestplot==1,0], xtestplot[ytestplot==1,1], '.', label='Class B')\n", "plt.legend()\n", "plt.show()\n", "print(f'There are {xtest.shape[0]} training data points!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Prepare our data into the correct format for PyTorch's Dataloader \n", "class spirals(Dataset):\n", "    \n", "    def __init__(self,length):\n", "        super().__init__()\n", "        self.length = length\n", "        self.x,self.y = twospirals(self.length)\n", "        \n", "    def __len__(self):\n", "        return len(self.y)\n", "    \n", "    def __getitem__(self, i):\n", "        return self.x[i], self.y[i] "]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["Ntrain = 500                # Set the number of training data points\n", "datanew = spirals(Ntrain)    # Generate training data points\n", "batchsize = 25               # The batch size can be set here\n", "dataloader = DataLoader(datanew, batch_size=batchsize, shuffle = True)  # Feed the data into the dataloader."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["class Net(nn.Module):\n", "    def __init__(self):\n", "        super(Net, self).__init__()\n", "        \n", "        self.big_pass = nn.Sequential(nn.Linear(2,100), # Input layer (2 nodes) to hidden layer (100 nodes)\n", "                                      nn.ReLU(),        # ReLU activation\n", "                                      nn.Linear(100,1), # Hidden layer to output\n", "                                      nn.Sigmoid()      # Pass output through a sigmoid, which is appropriate for a binary classification problem\n", "                                     )\n", "\n", "    def forward(self, x):\n", "        out = self.big_pass(x)\n", "        return out "]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def accuracy_Nclass(out,y):\n", "    diff = np.count_nonzero(np.round(out.squeeze())-y)\n", "    return (1-(diff/np.size(y)))*100"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import math, torch\n", "from torch.optim.optimizer import Optimizer\n", "\n", "class ZBAOABZ(Optimizer):\n", "    def __init__(self, params, dtau=4e-4, weight_decay=1e-5, gamma=1.0,\n", "                 alpha=50.0, alpha2=1.0/60000.0, m=0.1, M=10.0, temperature=1.0, r=0.25):\n", "        if dtau <= 0: raise ValueError(\"dtau > 0 required\")\n", "        if alpha <= 0: raise ValueError(\"alpha > 0 required\")\n", "        if m <= 0 or M <= 0: raise ValueError(\"m, M > 0 required\")\n", "        if temperature < 0: raise ValueError(\"temperature >= 0 required\")\n", "        if r <= 0: raise ValueError(\"r > 0 required\")\n", "        defaults = dict(lr=None, dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "                        alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r)\n", "        super().__init__(params, defaults)\n", "        self._zeta = None\n", "        self._exptau_half = None\n", "        self._a, self._sqrt_aT = {}, {}\n", "        self.dt_history, self.zeta_history = [], []\n", "\n", "    def _ensure_buffers(self):\n", "        any_p = None\n", "        for g in self.param_groups:\n", "            for p in g['params']:\n", "                if p.grad is None: continue\n", "                any_p = p\n", "                st = self.state[p]\n", "                if 'momentum_buffer' not in st:\n", "                    T = g['temperature']; std = (0.5*T)**0.5 if T>0 else 0.0\n", "                    st['momentum_buffer'] = torch.zeros_like(p) if std==0 else torch.normal(torch.zeros_like(p), std)\n", "        if self._zeta is None:\n", "            dev = any_p.device if any_p is not None else torch.device('cpu')\n", "            dtype = any_p.dtype if any_p is not None else torch.float32\n", "            self._zeta = torch.zeros((), device=dev, dtype=dtype)\n", "\n", "    @torch.no_grad()\n", "    def _grad_norm_scaled(self):\n", "        total = None\n", "        for g in self.param_groups:\n", "            wd = g['weight_decay']\n", "            for p in g['params']:\n", "                if p.grad is None: continue\n", "                gg = p.grad.detach()\n", "                if wd!=0: gg = gg + wd*p.data\n", "                s = (gg*gg).sum()\n", "                total = s if total is None else total + s\n", "        if total is None:\n", "            total = torch.zeros_like(self._zeta)\n", "        return total * self.param_groups[0]['alpha2']\n", "\n", "    @torch.no_grad()\n", "    def _Z_step(self):\n", "        g0 = self.param_groups[0]\n", "        alpha, dtau = g0['alpha'], g0['dtau']\n", "        if self._exptau_half is None or self._exptau_half.device != self._zeta.device or self._exptau_half.dtype != self._zeta.dtype:\n", "            self._exptau_half = torch.exp(torch.tensor(-0.5*alpha*dtau, device=self._zeta.device, dtype=self._zeta.dtype))\n", "        g = self._grad_norm_scaled()\n", "        self._zeta.mul_(self._exptau_half).add_(g, alpha=(1.0 - self._exptau_half.item())/alpha)\n", "\n", "    @torch.no_grad()\n", "    def _<PERSON><PERSON>_and_O<PERSON>(self):\n", "        zeta_r = torch.clamp(self._zeta, min=0).pow(self.param_groups[0]['r'])\n", "        for gi,g in enumerate(self.param_groups):\n", "            dtau, m, M, gamma, T = g['dtau'], g['m'], g['M'], g['gamma'], g['temperature']\n", "            dt = dtau * m * (zeta_r + M) / (zeta_r + m)\n", "            g['lr'] = dt\n", "            a = torch.exp(torch.tensor(-gamma, device=self._zeta.device, dtype=self._zeta.dtype) * dt)\n", "            one_minus_a2 = torch.clamp(1 - a*a, min=0.0)\n", "            self._a[gi] = a\n", "            self._sqrt_aT[gi] = torch.sqrt(one_minus_a2 * T)\n", "        self.dt_history.append(float(self.param_groups[0]['lr']))\n", "        self.zeta_history.append(float(self._zeta))\n", "\n", "    @torch.no_grad()\n", "    def step_pre(self):\n", "        self._ensure_buffers()\n", "        if self._zeta.item()==0.0:\n", "            self._zeta = self._grad_norm_scaled().clone().detach()\n", "        self._Z_step()\n", "        self._<PERSON><PERSON>_and_OU()\n", "\n", "    @torch.no_grad()\n", "    def step_BAOA(self):\n", "        for gi,g in enumerate(self.param_groups):\n", "            dt = g['lr']; dt = dt if isinstance(dt, torch.Tensor) else torch.tensor(dt, device=self._zeta.device, dtype=self._zeta.dtype)\n", "            wd = g['weight_decay']; a = self._a[gi]; sT = self._sqrt_aT[gi]\n", "            for p in g['params']:\n", "                if p.grad is None: continue\n", "                st = self.state[p]; mom = st['momentum_buffer']\n", "                gg = p.grad.detach(); \n", "                if wd!=0: gg = gg + wd*p.data\n", "                mom.add_(gg, alpha=-(0.5)*dt)      # B/2\n", "                p.add_(mom, alpha=0.5*dt)          # A/2\n", "                mom.mul_(a.to(device=p.device, dtype=p.dtype)).add_(torch.randn_like(p), alpha=sT.to(device=p.device, dtype=p.dtype))  # O\n", "                p.add_(mom, alpha=0.5*dt)          # A/2\n", "\n", "    @torch.no_grad()\n", "    def step_B(self):\n", "        for g in self.param_groups:\n", "            dt = g['lr']; dt = dt if isinstance(dt, torch.Tensor) else torch.tensor(dt, device=self._zeta.device, dtype=self._zeta.dtype)\n", "            wd = g['weight_decay']\n", "            for p in g['params']:\n", "                if p.grad is None: continue\n", "                mom = self.state[p]['momentum_buffer']\n", "                gg = p.grad.detach()\n", "                if wd!=0: gg = gg + wd*p.data\n", "                mom.add_(gg, alpha=-(0.5)*dt)      # B/2\n", "\n", "    @torch.no_grad()\n", "    def step_post(self):\n", "        self._Z_step()\n", "        self._<PERSON><PERSON>_and_OU()\n", "\n", "    @torch.no_grad()\n", "    def set_temperature(self, T: float):\n", "        for g in self.param_groups:\n", "            g['temperature'] = float(T)\n", "\n", "    @torch.no_grad()\n", "    def get_dt(self): \n", "        dt = self.param_groups[0]['lr']\n", "        return float(dt.detach().cpu().item()) if isinstance(dt, torch.Tensor) else float(dt)\n", "\n", "    @torch.no_grad()\n", "    def get_zeta(self):\n", "        return float(self._zeta.detach().cpu().item())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# ===== ZBAOABZ + Adaptive Batch Size on Two-Spirals =====\n", "\n", "# --- Hyperparameters ---\n", "# ZBAOABZ (step-size) control\n", "dtau         = 4e-2\n", "gamma        = 1.0\n", "alpha        = 5.0\n", "alpha2       = 1.0 / Ntrain\n", "m, M         = 0.1, 10.0\n", "temperature  = 1e-4\n", "r_exp        = 0.25\n", "weight_decay = 1e-5\n", "\n", "# Adaptive batch-size control (simple version for spirals)\n", "burn_in_epochs   = 1000\n", "min_B            = 10\n", "max_B            = 200\n", "init_B           = 25                # start at your old batch size\n", "post_burn_init_B = min_B             # reset once after burn-in\n", "alpha_batch      = 5.0\n", "dtau_batch       = 0.05\n", "scale_g_batch    = 1             # scales Tkin monitor\n", "r_batch          = 0.5              # curvature of mapping\n", "steepness_batch  = 0.01\n", "threshold_batch  = 5.0\n", "\n", "# helper: monotone mapping between sigma and batch size (inverse: B = N/(sigma+1))\n", "Ntotal     = Ntrain\n", "sigma_from_B = lambda B: (Ntotal - B) / max(B, 1)\n", "sigma_min  = sigma_from_B(max_B)\n", "sigma_max  = sigma_from_B(min_B)\n", "\n", "\n", "def psi_batch(zeta_b):\n", "    \"\"\"Map auxiliary zeta_b to sigma via atan (decreasing).\"\"\"\n", "    mid   = 0.5 * (sigma_min + sigma_max)\n", "    span  = (sigma_max - sigma_min)\n", "    # arctan with sign + exponent shaping\n", "    val   = np.arctan(steepness_batch * np.sign(zeta_b) * (abs(zeta_b)**r_batch))\n", "    return mid - (span / np.pi) * val\n", "\n", "# measure Tkin / Tconf\n", "@torch.no_grad()\n", "def measure_temperatures(model, optimizer):\n", "    kin_sum = 0.0\n", "    conf_sum = 0.0\n", "    count = 0\n", "    for p in model.parameters():\n", "        st = optimizer.state.get(p, None)\n", "        if st is not None and 'momentum_buffer' in st:\n", "            buf = st['momentum_buffer']\n", "            kin_sum += (buf * buf).sum().item()\n", "            count   += buf.numel()\n", "        if p.grad is not None:\n", "            conf_sum += (p.data * p.grad).sum().item()\n", "    if count == 0:\n", "        return float('nan'), float('nan')\n", "    return kin_sum / count, conf_sum / count\n", "\n", "# Build model/optimizer/criterion\n", "NN = Net()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "    alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r_exp\n", ")\n", "\n", "# Training traces\n", "RES = []\n", "dt_trace, batch_trace = [], []\n", "tkin_trace, tconf_trace, eval_epoch_idx = [], [], []\n", "zeta_batch_trace = []\n", "\n", "# adaptive-batch state\n", "batch_size = init_B\n", "zeta_b     = 0.0\n", "rho_b      = np.exp(-alpha_batch * dtau_batch)\n", "post_burn_reset_done = False\n", "\n", "num_epochs = 4000\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "\n", "    # one-time reset to a large batch after burn-in (optional but helpful)\n", "    if epoch == burn_in_epochs and not post_burn_reset_done:\n", "        batch_size = post_burn_init_B\n", "        post_burn_reset_done = True\n", "\n", "    # shuffle indices for manual batching\n", "    idx = torch.randperm(Ntrain)\n", "    cur = 0\n", "    while cur < Ntrain:\n", "        # ---- <PERSON>E (<PERSON> <PERSON>) ----\n", "        # build a batch with the *current* adaptive batch size\n", "        end = min(cur + batch_size, Ntrain)\n", "        batch_idx = idx[cur:end]\n", "        xb = datanew.x[batch_idx]\n", "        yb = datanew.y[batch_idx].float().unsqueeze(1)\n", "\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)       # \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # record current dt (per-iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        batch_trace.append(batch_size)\n", "\n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (<PERSON> + <PERSON>) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- Adaptive Batch Update (after burn-in) ----\n", "        if epoch >= burn_in_epochs:\n", "            # monitor: use Tkin (noisy but cheap)\n", "            _, Tconf = measure_temperatures(NN, optimizer)\n", "            g_val = np.abs(Tconf - temperature) / temperature if temperature > 0 else np.abs(Tconf)\n", "            g_val *= scale_g_batch\n", "            # zeta_b update\n", "            zeta_b = rho_b * zeta_b + (1 - rho_b) * g_val / max(alpha_batch, 1e-8)\n", "            # map to sigma, clamp, then to batch size\n", "            sigma = np.clip(psi_batch(zeta_b), sigma_min, sigma_max)\n", "            new_B = int(np.round(np.clip(Ntotal / (sigma + 1.0), min_B, max_B)))\n", "            batch_size = new_B\n", "\n", "        # traces (optional)\n", "        zeta_batch_trace.append(zeta_b)\n", "\n", "        # advance\n", "        cur = end\n", "\n", "    # periodic eval every 10 epochs (to mirror your AdLaLa cadence)\n", "    if (epoch + 1) % 10 == 0:\n", "        with torch.no_grad():\n", "            # training snapshot on last minibatch\n", "            acc = accuracy_Nclass(out.detach().cpu().numpy(), yb.detach().cpu().numpy().squeeze())\n", "            # acc = accuracy_Nclass(out.detach().numpy(), yb.detach().numpy())\n", "            # test snapshot (all test at once)\n", "            out_test = NN(xtest)\n", "            loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "            acc_test  = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "\n", "            RES.append([epoch, (loss.item() / len(xb)), acc, loss_test.item(), acc_test])\n", "\n", "        <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "        tkin_trace.append(Tkin)\n", "        tconf_trace.append(Tconf)\n", "        eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBABS = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "# ---- Plots ----\n", "if len(RES_ZBABS):\n", "    epochs_plot = RES_ZBABS[:,0]\n", "    tr_loss     = RES_ZBABS[:,1]\n", "    tr_acc      = RES_ZBABS[:,2]\n", "    te_loss     = RES_ZBABS[:,3]\n", "    te_acc      = RES_ZBABS[:,4]\n", "\n", "    plt.figure(figsize=[17,10]) # Increase the size of the plots\n", "    plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "    plt.subplot(2,3,1)\n", "    plt.plot(epochs_plot, tr_loss, label='Train Loss')\n", "    plt.plot(epochs_plot, te_loss, label='Test Loss')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid()\n", "    plt.ylim([0,0.8])\n", "    plt.title(r'Adaptive Batchsize Loss function')\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,2)\n", "    plt.plot(epochs_plot, tr_acc, label='Train Acc')\n", "    plt.plot(epochs_plot, te_acc, label='Test Acc')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy (in %)')\n", "    plt.title(r'Adaptive Batchsize Prediction Accuracy')\n", "    plt.grid()\n", "    plt.ylim([40,100])\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,3)\n", "    xx = np.linspace(-2,2,100)\n", "    X,Y = np.meshgrid(xx,xx)\n", "    Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "    out = NN(torch.tensor(Z.T).float())\n", "\n", "    out=out.reshape(100,100)\n", "    plt.pcolor(xx,xx,out.data)\n", "    plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "    plt.axis('square')\n", "\n", "    kk1 = ytest.reshape(-1)==0\n", "    kk2 = ytest.reshape(-1)==1\n", "    plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "    plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "    plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "    \n", "    plt.subplot(2,3,4)\n", "    plt.plot(dt_trace, lw=1)\n", "    plt.title('Adaptive step size Δt'); plt.grid(True); plt.xlabel('Iteration')\n", "\n", "    plt.subplot(2,3,5)\n", "    plt.hist(batch_trace[burn_in_epochs:], bins=50, density=True, label='Batch Size Distribution')\n", "    plt.axvline(np.mean(batch_trace[burn_in_epochs:]), color='r', linestyle='--', label=f'Mean: {np.mean(batch_trace[burn_in_epochs:]):.2f}')\n", "    plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "    plt.xlabel('<PERSON><PERSON> Size')\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "\n", "    # Tkin/Tconf at eval cadence\n", "    plt.subplot(2,3,6)\n", "    plt.plot(eval_epoch_idx, tkin_trace, marker='o', label='Tkin')\n", "    plt.plot(eval_epoch_idx, tconf_trace, marker='s', label='Tconf')\n", "    plt.axhline(temperature, color='k', ls='--', label=f'Temperature: {temperature}')\n", "    plt.title('Temperatures'); plt.xlabel('Epoch'); plt.ylabel('Value'); plt.legend(); plt.grid(True)\n", "    plt.show()\n", "\n", "    fig, ax = plt.subplots(1,2, figsize=(16,4))\n", "    ax[0].plot(batch_trace)\n", "    ax[0].set_title('Adaptive batch size B'); ax[0].grid(True); ax[0].set_xlabel('Iteration')\n", "\n", "    ax[1].plot(zeta_batch_trace)\n", "    ax[1].set_title(r'$\\zeta_{\\sigma}$'); ax[1].grid(True); ax[1].set_xlabel('Iteration')\n", "    plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["epoch 499/4000, test loss = 0.1035, train acc = 96.00%, test acc = 95.20%\n", "epoch 999/4000, test loss = 0.0842, train acc = 100.00%, test acc = 96.80%\n", "epoch 1499/4000, test loss = 0.0432, train acc = 100.00%, test acc = 98.80%\n", "epoch 1999/4000, test loss = 0.0265, train acc = 100.00%, test acc = 99.10%\n", "epoch 2499/4000, test loss = 0.0191, train acc = 100.00%, test acc = 99.00%\n", "epoch 2999/4000, test loss = 2.1275, train acc = 100.00%, test acc = 97.00%\n", "epoch 3499/4000, test loss = 0.0505, train acc = 100.00%, test acc = 98.70%\n", "epoch 3999/4000, test loss = 0.0214, train acc = 100.00%, test acc = 99.20%"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# ===== ZBAOABZ + Adaptive Batch Size on Two-Spirals =====\n", "\n", "# --- Hyperparameters ---\n", "# ZBAOABZ (step-size) control\n", "dtau         = 4e-2\n", "gamma        = 1.0\n", "alpha        = 5.0\n", "alpha2       = 1.0 / Ntrain\n", "m, M         = 0.1, 10.0\n", "temperature  = 1e-4\n", "r_exp        = 0.25\n", "weight_decay = 1e-5\n", "\n", "# Adaptive batch-size control (simple version for spirals)\n", "burn_in_epochs   = 1000\n", "min_B            = 10\n", "max_B            = 200\n", "init_B           = 25                # start at your old batch size\n", "post_burn_init_B = min_B             # reset once after burn-in\n", "alpha_batch      = 5.0\n", "dtau_batch       = 0.05\n", "scale_g_batch    = 1             # scales Tkin monitor\n", "r_batch          = 0.5              # curvature of mapping\n", "steepness_batch  = 0.01\n", "threshold_batch  = 3.0\n", "\n", "# helper: monotone mapping between sigma and batch size (inverse: B = N/(sigma+1))\n", "Ntotal     = Ntrain\n", "sigma_from_B = lambda B: (Ntotal - B) / max(B, 1)\n", "sigma_min  = sigma_from_B(max_B)\n", "sigma_max  = sigma_from_B(min_B)\n", "\n", "\n", "def psi_batch(zeta_b):\n", "    \"\"\"Map auxiliary zeta_b to sigma via atan (decreasing).\"\"\"\n", "    mid   = 0.5 * (sigma_min + sigma_max)\n", "    span  = (sigma_max - sigma_min)\n", "    # arctan with sign + exponent shaping\n", "    val   = np.arctan(steepness_batch * np.sign(zeta_b) * (abs(zeta_b)**r_batch))\n", "    return mid - (span / np.pi) * val\n", "\n", "# measure Tkin / Tconf\n", "@torch.no_grad()\n", "def measure_temperatures(model, optimizer):\n", "    kin_sum = 0.0\n", "    conf_sum = 0.0\n", "    count = 0\n", "    for p in model.parameters():\n", "        st = optimizer.state.get(p, None)\n", "        if st is not None and 'momentum_buffer' in st:\n", "            buf = st['momentum_buffer']\n", "            kin_sum += (buf * buf).sum().item()\n", "            count   += buf.numel()\n", "        if p.grad is not None:\n", "            conf_sum += (p.data * p.grad).sum().item()\n", "    if count == 0:\n", "        return float('nan'), float('nan')\n", "    return kin_sum / count, conf_sum / count\n", "\n", "# Build model/optimizer/criterion\n", "NN = Net()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "    alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r_exp\n", ")\n", "\n", "# Training traces\n", "RES = []\n", "dt_trace, batch_trace = [], []\n", "tkin_trace, tconf_trace, eval_epoch_idx = [], [], []\n", "zeta_batch_trace = []\n", "\n", "# adaptive-batch state\n", "batch_size = init_B\n", "zeta_b     = 0.0\n", "rho_b      = np.exp(-alpha_batch * dtau_batch)\n", "post_burn_reset_done = False\n", "\n", "num_epochs = 4000\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "\n", "    # one-time reset to a large batch after burn-in (optional but helpful)\n", "    if epoch == burn_in_epochs and not post_burn_reset_done:\n", "        batch_size = post_burn_init_B\n", "        post_burn_reset_done = True\n", "\n", "    # shuffle indices for manual batching\n", "    idx = torch.randperm(Ntrain)\n", "    cur = 0\n", "    while cur < Ntrain:\n", "        # ---- <PERSON>E (<PERSON> <PERSON>) ----\n", "        # build a batch with the *current* adaptive batch size\n", "        end = min(cur + batch_size, Ntrain)\n", "        batch_idx = idx[cur:end]\n", "        xb = datanew.x[batch_idx]\n", "        yb = datanew.y[batch_idx].float().unsqueeze(1)\n", "\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)       # \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # record current dt (per-iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        batch_trace.append(batch_size)\n", "\n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (<PERSON> + <PERSON>) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- Adaptive Batch Update (after burn-in) ----\n", "        if epoch >= burn_in_epochs:\n", "            # monitor: use Tkin (noisy but cheap)\n", "            _, Tconf = measure_temperatures(NN, optimizer)\n", "            g_val = np.abs(Tconf - temperature) / temperature if temperature > 0 else np.abs(Tconf)\n", "            g_val *= scale_g_batch\n", "            # zeta_b update\n", "            zeta_b = rho_b * zeta_b + (1 - rho_b) * g_val / max(alpha_batch, 1e-8)\n", "            # map to sigma, clamp, then to batch size\n", "            sigma = np.clip(psi_batch(zeta_b), sigma_min, sigma_max)\n", "            new_B = int(np.round(np.clip(Ntotal / (sigma + 1.0), min_B, max_B)))\n", "            batch_size = new_B\n", "\n", "        # traces (optional)\n", "        zeta_batch_trace.append(zeta_b)\n", "\n", "        # advance\n", "        cur = end\n", "\n", "    # periodic eval every 10 epochs (to mirror your AdLaLa cadence)\n", "    if (epoch + 1) % 10 == 0:\n", "        with torch.no_grad():\n", "            # training snapshot on last minibatch\n", "            acc = accuracy_Nclass(out.detach().cpu().numpy(), yb.detach().cpu().numpy().squeeze())\n", "            # acc = accuracy_Nclass(out.detach().numpy(), yb.detach().numpy())\n", "            # test snapshot (all test at once)\n", "            out_test = NN(xtest)\n", "            loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "            acc_test  = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "\n", "            RES.append([epoch, (loss.item() / len(xb)), acc, loss_test.item(), acc_test])\n", "\n", "        <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "        tkin_trace.append(Tkin)\n", "        tconf_trace.append(Tconf)\n", "        eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBABS = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "# ---- Plots ----\n", "if len(RES_ZBABS):\n", "    epochs_plot = RES_ZBABS[:,0]\n", "    tr_loss     = RES_ZBABS[:,1]\n", "    tr_acc      = RES_ZBABS[:,2]\n", "    te_loss     = RES_ZBABS[:,3]\n", "    te_acc      = RES_ZBABS[:,4]\n", "\n", "    plt.figure(figsize=[17,10]) # Increase the size of the plots\n", "    plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "    plt.subplot(2,3,1)\n", "    plt.plot(epochs_plot, tr_loss, label='Train Loss')\n", "    plt.plot(epochs_plot, te_loss, label='Test Loss')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid()\n", "    plt.ylim([0,0.8])\n", "    plt.title(r'Adaptive Batchsize Loss function')\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,2)\n", "    plt.plot(epochs_plot, tr_acc, label='Train Acc')\n", "    plt.plot(epochs_plot, te_acc, label='Test Acc')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy (in %)')\n", "    plt.title(r'Adaptive Batchsize Prediction Accuracy')\n", "    plt.grid()\n", "    plt.ylim([40,100])\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,3)\n", "    xx = np.linspace(-2,2,100)\n", "    X,Y = np.meshgrid(xx,xx)\n", "    Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "    out = NN(torch.tensor(Z.T).float())\n", "\n", "    out=out.reshape(100,100)\n", "    plt.pcolor(xx,xx,out.data)\n", "    plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "    plt.axis('square')\n", "\n", "    kk1 = ytest.reshape(-1)==0\n", "    kk2 = ytest.reshape(-1)==1\n", "    plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "    plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "    plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "    \n", "    plt.subplot(2,3,4)\n", "    plt.plot(dt_trace, lw=1)\n", "    plt.title('Adaptive step size Δt'); plt.grid(True); plt.xlabel('Iteration')\n", "\n", "    plt.subplot(2,3,5)\n", "    plt.hist(batch_trace[burn_in_epochs:], bins=50, density=True, label='Batch Size Distribution')\n", "    plt.axvline(np.mean(batch_trace[burn_in_epochs:]), color='r', linestyle='--', label=f'Mean: {np.mean(batch_trace[burn_in_epochs:]):.2f}')\n", "    plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "    plt.xlabel('<PERSON><PERSON> Size')\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "\n", "    # Tkin/Tconf at eval cadence\n", "    plt.subplot(2,3,6)\n", "    plt.plot(eval_epoch_idx, tkin_trace, marker='o', label='Tkin')\n", "    plt.plot(eval_epoch_idx, tconf_trace, marker='s', label='Tconf')\n", "    plt.axhline(temperature, color='k', ls='--', label=f'Temperature: {temperature}')\n", "    plt.title('Temperatures'); plt.xlabel('Epoch'); plt.ylabel('Value'); plt.legend(); plt.grid(True)\n", "    plt.show()\n", "\n", "    fig, ax = plt.subplots(1,2, figsize=(16,4))\n", "    ax[0].plot(batch_trace)\n", "    ax[0].set_title('Adaptive batch size B'); ax[0].grid(True); ax[0].set_xlabel('Iteration')\n", "\n", "    ax[1].plot(zeta_batch_trace)\n", "    ax[1].set_title(r'$\\zeta_{\\sigma}$'); ax[1].grid(True); ax[1].set_xlabel('Iteration')\n", "    plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["epoch 499/4000, test loss = 0.1748, train acc = 92.00%, test acc = 90.70%\n", "epoch 999/4000, test loss = 0.0623, train acc = 100.00%, test acc = 98.20%\n", "epoch 1499/4000, test loss = 0.0495, train acc = 100.00%, test acc = 98.40%\n", "epoch 1999/4000, test loss = 0.0872, train acc = 100.00%, test acc = 97.20%\n", "epoch 2499/4000, test loss = 0.1072, train acc = 100.00%, test acc = 96.80%\n", "epoch 2999/4000, test loss = 0.0159, train acc = 100.00%, test acc = 99.20%\n", "epoch 3499/4000, test loss = 0.0211, train acc = 100.00%, test acc = 98.90%\n", "epoch 3999/4000, test loss = 0.0174, train acc = 100.00%, test acc = 99.40%"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# ===== ZBAOABZ + Adaptive Batch Size on Two-Spirals =====\n", "\n", "# --- Hyperparameters ---\n", "# ZBAOABZ (step-size) control\n", "dtau         = 4e-2\n", "gamma        = 1.0\n", "alpha        = 5.0\n", "alpha2       = 1.0 / Ntrain\n", "m, M         = 0.1, 10.0\n", "temperature  = 1e-4\n", "r_exp        = 0.25\n", "weight_decay = 1e-5\n", "\n", "# Adaptive batch-size control (simple version for spirals)\n", "burn_in_epochs   = 1000\n", "min_B            = 10\n", "max_B            = 200\n", "init_B           = 25                # start at your old batch size\n", "post_burn_init_B = min_B             # reset once after burn-in\n", "alpha_batch      = 5.0\n", "dtau_batch       = 0.05\n", "scale_g_batch    = 1             # scales Tkin monitor\n", "r_batch          = 0.5              # curvature of mapping\n", "steepness_batch  = 0.05\n", "threshold_batch  = 1.0\n", "\n", "# helper: monotone mapping between sigma and batch size (inverse: B = N/(sigma+1))\n", "Ntotal     = Ntrain\n", "sigma_from_B = lambda B: (Ntotal - B) / max(B, 1)\n", "sigma_min  = sigma_from_B(max_B)\n", "sigma_max  = sigma_from_B(min_B)\n", "\n", "\n", "def psi_batch(zeta_b):\n", "    \"\"\"Map auxiliary zeta_b to sigma via atan (decreasing).\"\"\"\n", "    mid   = 0.5 * (sigma_min + sigma_max)\n", "    span  = (sigma_max - sigma_min)\n", "    # arctan with sign + exponent shaping\n", "    val   = np.arctan(steepness_batch * np.sign(zeta_b) * (abs(zeta_b)**r_batch))\n", "    return mid - (span / np.pi) * val\n", "\n", "# measure Tkin / Tconf\n", "@torch.no_grad()\n", "def measure_temperatures(model, optimizer):\n", "    kin_sum = 0.0\n", "    conf_sum = 0.0\n", "    count = 0\n", "    for p in model.parameters():\n", "        st = optimizer.state.get(p, None)\n", "        if st is not None and 'momentum_buffer' in st:\n", "            buf = st['momentum_buffer']\n", "            kin_sum += (buf * buf).sum().item()\n", "            count   += buf.numel()\n", "        if p.grad is not None:\n", "            conf_sum += (p.data * p.grad).sum().item()\n", "    if count == 0:\n", "        return float('nan'), float('nan')\n", "    return kin_sum / count, conf_sum / count\n", "\n", "# Build model/optimizer/criterion\n", "NN = Net()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "    alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r_exp\n", ")\n", "\n", "# Training traces\n", "RES = []\n", "dt_trace, batch_trace = [], []\n", "tkin_trace, tconf_trace, eval_epoch_idx = [], [], []\n", "zeta_batch_trace = []\n", "\n", "# adaptive-batch state\n", "batch_size = init_B\n", "zeta_b     = 0.0\n", "rho_b      = np.exp(-alpha_batch * dtau_batch)\n", "post_burn_reset_done = False\n", "\n", "num_epochs = 4000\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "\n", "    # one-time reset to a large batch after burn-in (optional but helpful)\n", "    if epoch == burn_in_epochs and not post_burn_reset_done:\n", "        batch_size = post_burn_init_B\n", "        post_burn_reset_done = True\n", "\n", "    # shuffle indices for manual batching\n", "    idx = torch.randperm(Ntrain)\n", "    cur = 0\n", "    while cur < Ntrain:\n", "        # ---- <PERSON>E (<PERSON> <PERSON>) ----\n", "        # build a batch with the *current* adaptive batch size\n", "        end = min(cur + batch_size, Ntrain)\n", "        batch_idx = idx[cur:end]\n", "        xb = datanew.x[batch_idx]\n", "        yb = datanew.y[batch_idx].float().unsqueeze(1)\n", "\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)       # \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # record current dt (per-iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        batch_trace.append(batch_size)\n", "\n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (<PERSON> + <PERSON>) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- Adaptive Batch Update (after burn-in) ----\n", "        if epoch >= burn_in_epochs:\n", "            # monitor: use Tkin (noisy but cheap)\n", "            _, Tconf = measure_temperatures(NN, optimizer)\n", "            g_val = np.abs(Tconf - temperature) / temperature if temperature > 0 else np.abs(Tconf)\n", "            g_val *= scale_g_batch\n", "            # zeta_b update\n", "            zeta_b = rho_b * zeta_b + (1 - rho_b) * g_val / max(alpha_batch, 1e-8)\n", "            # map to sigma, clamp, then to batch size\n", "            sigma = np.clip(psi_batch(zeta_b), sigma_min, sigma_max)\n", "            new_B = int(np.round(np.clip(Ntotal / (sigma + 1.0), min_B, max_B)))\n", "            batch_size = new_B\n", "\n", "        # traces (optional)\n", "        zeta_batch_trace.append(zeta_b)\n", "\n", "        # advance\n", "        cur = end\n", "\n", "    # periodic eval every 10 epochs (to mirror your AdLaLa cadence)\n", "    if (epoch + 1) % 10 == 0:\n", "        with torch.no_grad():\n", "            # training snapshot on last minibatch\n", "            acc = accuracy_Nclass(out.detach().cpu().numpy(), yb.detach().cpu().numpy().squeeze())\n", "            # acc = accuracy_Nclass(out.detach().numpy(), yb.detach().numpy())\n", "            # test snapshot (all test at once)\n", "            out_test = NN(xtest)\n", "            loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "            acc_test  = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "\n", "            RES.append([epoch, (loss.item() / len(xb)), acc, loss_test.item(), acc_test])\n", "\n", "        <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "        tkin_trace.append(Tkin)\n", "        tconf_trace.append(Tconf)\n", "        eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBABS = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "# ---- Plots ----\n", "if len(RES_ZBABS):\n", "    epochs_plot = RES_ZBABS[:,0]\n", "    tr_loss     = RES_ZBABS[:,1]\n", "    tr_acc      = RES_ZBABS[:,2]\n", "    te_loss     = RES_ZBABS[:,3]\n", "    te_acc      = RES_ZBABS[:,4]\n", "\n", "    plt.figure(figsize=[17,10]) # Increase the size of the plots\n", "    plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "    plt.subplot(2,3,1)\n", "    plt.plot(epochs_plot, tr_loss, label='Train Loss')\n", "    plt.plot(epochs_plot, te_loss, label='Test Loss')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid()\n", "    plt.ylim([0,0.8])\n", "    plt.title(r'Adaptive Batchsize Loss function')\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,2)\n", "    plt.plot(epochs_plot, tr_acc, label='Train Acc')\n", "    plt.plot(epochs_plot, te_acc, label='Test Acc')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy (in %)')\n", "    plt.title(r'Adaptive Batchsize Prediction Accuracy')\n", "    plt.grid()\n", "    plt.ylim([40,100])\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,3)\n", "    xx = np.linspace(-2,2,100)\n", "    X,Y = np.meshgrid(xx,xx)\n", "    Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "    out = NN(torch.tensor(Z.T).float())\n", "\n", "    out=out.reshape(100,100)\n", "    plt.pcolor(xx,xx,out.data)\n", "    plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "    plt.axis('square')\n", "\n", "    kk1 = ytest.reshape(-1)==0\n", "    kk2 = ytest.reshape(-1)==1\n", "    plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "    plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "    plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "    \n", "    plt.subplot(2,3,4)\n", "    plt.plot(dt_trace, lw=1)\n", "    plt.title('Adaptive step size Δt'); plt.grid(True); plt.xlabel('Iteration')\n", "\n", "    plt.subplot(2,3,5)\n", "    plt.hist(batch_trace[burn_in_epochs:], bins=50, density=True, label='Batch Size Distribution')\n", "    plt.axvline(np.mean(batch_trace[burn_in_epochs:]), color='r', linestyle='--', label=f'Mean: {np.mean(batch_trace[burn_in_epochs:]):.2f}')\n", "    plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "    plt.xlabel('<PERSON><PERSON> Size')\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "\n", "    # Tkin/Tconf at eval cadence\n", "    plt.subplot(2,3,6)\n", "    plt.plot(eval_epoch_idx, tkin_trace, marker='o', label='Tkin')\n", "    plt.plot(eval_epoch_idx, tconf_trace, marker='s', label='Tconf')\n", "    plt.axhline(temperature, color='k', ls='--', label=f'Temperature: {temperature}')\n", "    plt.title('Temperatures'); plt.xlabel('Epoch'); plt.ylabel('Value'); plt.legend(); plt.grid(True)\n", "    plt.show()\n", "\n", "    fig, ax = plt.subplots(1,2, figsize=(16,4))\n", "    ax[0].plot(batch_trace)\n", "    ax[0].set_title('Adaptive batch size B'); ax[0].grid(True); ax[0].set_xlabel('Iteration')\n", "\n", "    ax[1].plot(zeta_batch_trace)\n", "    ax[1].set_title(r'$\\zeta_{\\sigma}$'); ax[1].grid(True); ax[1].set_xlabel('Iteration')\n", "    plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["epoch 499/4000, test loss = 0.2253, train acc = 92.00%, test acc = 90.20%\n", "epoch 999/4000, test loss = 0.1071, train acc = 100.00%, test acc = 94.60%\n", "epoch 1499/4000, test loss = 0.9624, train acc = 100.00%, test acc = 93.30%\n", "epoch 1999/4000, test loss = 0.0510, train acc = 100.00%, test acc = 97.70%\n", "epoch 2499/4000, test loss = 0.3551, train acc = 95.00%, test acc = 91.00%\n", "epoch 2999/4000, test loss = 0.2337, train acc = 100.00%, test acc = 95.70%\n", "epoch 3499/4000, test loss = 0.1471, train acc = 100.00%, test acc = 96.40%\n", "epoch 3999/4000, test loss = 0.1130, train acc = 100.00%, test acc = 98.20%"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# ===== ZBAOABZ + Adaptive Batch Size on Two-Spirals =====\n", "\n", "# --- Hyperparameters ---\n", "# ZBAOABZ (step-size) control\n", "dtau         = 4e-2\n", "gamma        = 1.0\n", "alpha        = 5.0\n", "alpha2       = 1.0 / Ntrain\n", "m, M         = 0.1, 10.0\n", "temperature  = 1e-4\n", "r_exp        = 0.25\n", "weight_decay = 1e-5\n", "\n", "# Adaptive batch-size control (simple version for spirals)\n", "burn_in_epochs   = 1000\n", "min_B            = 10\n", "max_B            = 200\n", "init_B           = 25                # start at your old batch size\n", "post_burn_init_B = min_B             # reset once after burn-in\n", "alpha_batch      = 5.0\n", "dtau_batch       = 0.1\n", "scale_g_batch    = 1             # scales Tkin monitor\n", "r_batch          = 0.5              # curvature of mapping\n", "steepness_batch  = 0.05\n", "threshold_batch  = 1.0\n", "\n", "# helper: monotone mapping between sigma and batch size (inverse: B = N/(sigma+1))\n", "Ntotal     = Ntrain\n", "sigma_from_B = lambda B: (Ntotal - B) / max(B, 1)\n", "sigma_min  = sigma_from_B(max_B)\n", "sigma_max  = sigma_from_B(min_B)\n", "\n", "\n", "def psi_batch(zeta_b):\n", "    \"\"\"Map auxiliary zeta_b to sigma via atan (decreasing).\"\"\"\n", "    mid   = 0.5 * (sigma_min + sigma_max)\n", "    span  = (sigma_max - sigma_min)\n", "    # arctan with sign + exponent shaping\n", "    val   = np.arctan(steepness_batch * np.sign(zeta_b) * (abs(zeta_b)**r_batch))\n", "    return mid - (span / np.pi) * val\n", "\n", "# measure Tkin / Tconf\n", "@torch.no_grad()\n", "def measure_temperatures(model, optimizer):\n", "    kin_sum = 0.0\n", "    conf_sum = 0.0\n", "    count = 0\n", "    for p in model.parameters():\n", "        st = optimizer.state.get(p, None)\n", "        if st is not None and 'momentum_buffer' in st:\n", "            buf = st['momentum_buffer']\n", "            kin_sum += (buf * buf).sum().item()\n", "            count   += buf.numel()\n", "        if p.grad is not None:\n", "            conf_sum += (p.data * p.grad).sum().item()\n", "    if count == 0:\n", "        return float('nan'), float('nan')\n", "    return kin_sum / count, conf_sum / count\n", "\n", "# Build model/optimizer/criterion\n", "NN = Net()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "    alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r_exp\n", ")\n", "\n", "# Training traces\n", "RES = []\n", "dt_trace, batch_trace = [], []\n", "tkin_trace, tconf_trace, eval_epoch_idx = [], [], []\n", "zeta_batch_trace = []\n", "\n", "# adaptive-batch state\n", "batch_size = init_B\n", "zeta_b     = 0.0\n", "rho_b      = np.exp(-alpha_batch * dtau_batch)\n", "post_burn_reset_done = False\n", "\n", "num_epochs = 4000\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "\n", "    # one-time reset to a large batch after burn-in (optional but helpful)\n", "    if epoch == burn_in_epochs and not post_burn_reset_done:\n", "        batch_size = post_burn_init_B\n", "        post_burn_reset_done = True\n", "\n", "    # shuffle indices for manual batching\n", "    idx = torch.randperm(Ntrain)\n", "    cur = 0\n", "    while cur < Ntrain:\n", "        # ---- <PERSON>E (<PERSON> <PERSON>) ----\n", "        # build a batch with the *current* adaptive batch size\n", "        end = min(cur + batch_size, Ntrain)\n", "        batch_idx = idx[cur:end]\n", "        xb = datanew.x[batch_idx]\n", "        yb = datanew.y[batch_idx].float().unsqueeze(1)\n", "\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)       # \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # record current dt (per-iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        batch_trace.append(batch_size)\n", "\n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (<PERSON> + <PERSON>) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- Adaptive Batch Update (after burn-in) ----\n", "        if epoch >= burn_in_epochs:\n", "            # monitor: use Tkin (noisy but cheap)\n", "            _, Tconf = measure_temperatures(NN, optimizer)\n", "            g_val = np.abs(Tconf - temperature) / temperature if temperature > 0 else np.abs(Tconf)\n", "            g_val *= scale_g_batch\n", "            # zeta_b update\n", "            zeta_b = rho_b * zeta_b + (1 - rho_b) * g_val / max(alpha_batch, 1e-8)\n", "            # map to sigma, clamp, then to batch size\n", "            sigma = np.clip(psi_batch(zeta_b), sigma_min, sigma_max)\n", "            new_B = int(np.round(np.clip(Ntotal / (sigma + 1.0), min_B, max_B)))\n", "            batch_size = new_B\n", "\n", "        # traces (optional)\n", "        zeta_batch_trace.append(zeta_b)\n", "\n", "        # advance\n", "        cur = end\n", "\n", "    # periodic eval every 10 epochs (to mirror your AdLaLa cadence)\n", "    if (epoch + 1) % 10 == 0:\n", "        with torch.no_grad():\n", "            # training snapshot on last minibatch\n", "            acc = accuracy_Nclass(out.detach().cpu().numpy(), yb.detach().cpu().numpy().squeeze())\n", "            # acc = accuracy_Nclass(out.detach().numpy(), yb.detach().numpy())\n", "            # test snapshot (all test at once)\n", "            out_test = NN(xtest)\n", "            loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "            acc_test  = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "\n", "            RES.append([epoch, (loss.item() / len(xb)), acc, loss_test.item(), acc_test])\n", "\n", "        <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "        tkin_trace.append(Tkin)\n", "        tconf_trace.append(Tconf)\n", "        eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBABS = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "# ---- Plots ----\n", "if len(RES_ZBABS):\n", "    epochs_plot = RES_ZBABS[:,0]\n", "    tr_loss     = RES_ZBABS[:,1]\n", "    tr_acc      = RES_ZBABS[:,2]\n", "    te_loss     = RES_ZBABS[:,3]\n", "    te_acc      = RES_ZBABS[:,4]\n", "\n", "    plt.figure(figsize=[18,10]) # Increase the size of the plots\n", "    plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "    plt.subplot(2,3,1)\n", "    plt.plot(epochs_plot, tr_loss, label='Train Loss')\n", "    plt.plot(epochs_plot, te_loss, label='Test Loss')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid()\n", "    plt.ylim([0,0.8])\n", "    plt.title(r'Adaptive Batchsize Loss function')\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,2)\n", "    plt.plot(epochs_plot, tr_acc, label='Train Acc')\n", "    plt.plot(epochs_plot, te_acc, label='Test Acc')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy (in %)')\n", "    plt.title(r'Adaptive Batchsize Prediction Accuracy')\n", "    plt.grid()\n", "    plt.ylim([40,100])\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,3)\n", "    xx = np.linspace(-2,2,100)\n", "    X,Y = np.meshgrid(xx,xx)\n", "    Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "    out = NN(torch.tensor(Z.T).float())\n", "\n", "    out=out.reshape(100,100)\n", "    plt.pcolor(xx,xx,out.data)\n", "    plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "    plt.axis('square')\n", "\n", "    kk1 = ytest.reshape(-1)==0\n", "    kk2 = ytest.reshape(-1)==1\n", "    plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "    plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "    plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "    \n", "    plt.subplot(2,3,4)\n", "    plt.plot(dt_trace, lw=1)\n", "    plt.title('Adaptive step size Δt'); plt.grid(True); plt.xlabel('Iteration')\n", "\n", "    plt.subplot(2,3,5)\n", "    plt.hist(batch_trace[burn_in_epochs:], bins=50, density=True, label='Batch Size Distribution')\n", "    plt.axvline(np.mean(batch_trace[burn_in_epochs:]), color='r', linestyle='--', label=f'Mean: {np.mean(batch_trace[burn_in_epochs:]):.2f}')\n", "    plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "    plt.xlabel('<PERSON><PERSON> Size')\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "\n", "    # Tkin/Tconf at eval cadence\n", "    plt.subplot(2,3,6)\n", "    plt.plot(eval_epoch_idx, tkin_trace, marker='o', label='Tkin')\n", "    plt.plot(eval_epoch_idx, tconf_trace, marker='s', label='Tconf')\n", "    plt.axhline(temperature, color='k', ls='--', label=f'Temperature: {temperature}')\n", "    plt.title('Temperatures'); plt.xlabel('Epoch'); plt.ylabel('Value'); plt.legend(); plt.grid(True)\n", "    plt.show()\n", "\n", "    fig, ax = plt.subplots(1,2, figsize=(16,4))\n", "    ax[0].plot(batch_trace)\n", "    ax[0].set_title('Adaptive batch size B'); ax[0].grid(True); ax[0].set_xlabel('Iteration')\n", "\n", "    ax[1].plot(zeta_batch_trace)\n", "    ax[1].set_title(r'$\\zeta_{\\sigma}$'); ax[1].grid(True); ax[1].set_xlabel('Iteration')\n", "    plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["epoch 499/4000, test loss = 0.2478, train acc = 100.00%, test acc = 88.30%\n", "epoch 999/4000, test loss = 0.0999, train acc = 96.00%, test acc = 96.30%\n", "epoch 1499/4000, test loss = 0.2146, train acc = 88.24%, test acc = 89.70%\n", "epoch 1999/4000, test loss = 0.1491, train acc = 100.00%, test acc = 94.60%\n", "epoch 2499/4000, test loss = 0.0848, train acc = 100.00%, test acc = 96.50%\n", "epoch 2999/4000, test loss = 0.0793, train acc = 100.00%, test acc = 96.40%\n", "epoch 3499/4000, test loss = 0.1037, train acc = 100.00%, test acc = 95.90%\n", "epoch 3999/4000, test loss = 5.1790, train acc = 100.00%, test acc = 86.90%"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# ===== ZBAOABZ + Adaptive Batch Size on Two-Spirals =====\n", "\n", "# --- Hyperparameters ---\n", "# ZBAOABZ (step-size) control\n", "dtau         = 4e-2\n", "gamma        = 1.0\n", "alpha        = 5.0\n", "alpha2       = 1.0 / Ntrain\n", "m, M         = 0.1, 10.0\n", "temperature  = 1e-4\n", "r_exp        = 0.25\n", "weight_decay = 0\n", "\n", "# Adaptive batch-size control (simple version for spirals)\n", "burn_in_epochs   = 1000\n", "min_B            = 10\n", "max_B            = 200\n", "init_B           = 25                # start at your old batch size\n", "post_burn_init_B = min_B             # reset once after burn-in\n", "alpha_batch      = 5.0\n", "dtau_batch       = 0.05\n", "scale_g_batch    = 1             # scales Tkin monitor\n", "r_batch          = 0.5              # curvature of mapping\n", "steepness_batch  = 0.05\n", "threshold_batch  = 1.0\n", "\n", "# helper: monotone mapping between sigma and batch size (inverse: B = N/(sigma+1))\n", "Ntotal     = Ntrain\n", "sigma_from_B = lambda B: (Ntotal - B) / max(B, 1)\n", "sigma_min  = sigma_from_B(max_B)\n", "sigma_max  = sigma_from_B(min_B)\n", "\n", "\n", "def psi_batch(zeta_b):\n", "    \"\"\"Map auxiliary zeta_b to sigma via atan (decreasing).\"\"\"\n", "    mid   = 0.5 * (sigma_min + sigma_max)\n", "    span  = (sigma_max - sigma_min)\n", "    # arctan with sign + exponent shaping\n", "    val   = np.arctan(steepness_batch * np.sign(zeta_b) * (abs(zeta_b)**r_batch))\n", "    return mid - (span / np.pi) * val\n", "\n", "# measure Tkin / Tconf\n", "@torch.no_grad()\n", "def measure_temperatures(model, optimizer):\n", "    kin_sum = 0.0\n", "    conf_sum = 0.0\n", "    count = 0\n", "    for p in model.parameters():\n", "        st = optimizer.state.get(p, None)\n", "        if st is not None and 'momentum_buffer' in st:\n", "            buf = st['momentum_buffer']\n", "            kin_sum += (buf * buf).sum().item()\n", "            count   += buf.numel()\n", "        if p.grad is not None:\n", "            conf_sum += (p.data * p.grad).sum().item()\n", "    if count == 0:\n", "        return float('nan'), float('nan')\n", "    return kin_sum / count, conf_sum / count\n", "\n", "# Build model/optimizer/criterion\n", "NN = Net()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "    alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r_exp\n", ")\n", "\n", "# Training traces\n", "RES = []\n", "dt_trace, batch_trace = [], []\n", "tkin_trace, tconf_trace, eval_epoch_idx = [], [], []\n", "zeta_batch_trace = []\n", "\n", "# adaptive-batch state\n", "batch_size = init_B\n", "zeta_b     = 0.0\n", "rho_b      = np.exp(-alpha_batch * dtau_batch)\n", "post_burn_reset_done = False\n", "\n", "num_epochs = 4000\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "\n", "    # one-time reset to a large batch after burn-in (optional but helpful)\n", "    if epoch == burn_in_epochs and not post_burn_reset_done:\n", "        batch_size = post_burn_init_B\n", "        post_burn_reset_done = True\n", "\n", "    # shuffle indices for manual batching\n", "    idx = torch.randperm(Ntrain)\n", "    cur = 0\n", "    while cur < Ntrain:\n", "        # ---- <PERSON>E (<PERSON> <PERSON>) ----\n", "        # build a batch with the *current* adaptive batch size\n", "        end = min(cur + batch_size, Ntrain)\n", "        batch_idx = idx[cur:end]\n", "        xb = datanew.x[batch_idx]\n", "        yb = datanew.y[batch_idx].float().unsqueeze(1)\n", "\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)       # \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # record current dt (per-iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        batch_trace.append(batch_size)\n", "\n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (<PERSON> + <PERSON>) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- Adaptive Batch Update (after burn-in) ----\n", "        if epoch >= burn_in_epochs:\n", "            # monitor: use Tkin (noisy but cheap)\n", "            _, Tconf = measure_temperatures(NN, optimizer)\n", "            g_val = np.abs(Tconf - temperature) / temperature if temperature > 0 else np.abs(Tconf)\n", "            g_val *= scale_g_batch\n", "            # zeta_b update\n", "            zeta_b = rho_b * zeta_b + (1 - rho_b) * g_val / max(alpha_batch, 1e-8)\n", "            # map to sigma, clamp, then to batch size\n", "            sigma = np.clip(psi_batch(zeta_b), sigma_min, sigma_max)\n", "            new_B = int(np.round(np.clip(Ntotal / (sigma + 1.0), min_B, max_B)))\n", "            batch_size = new_B\n", "\n", "        # traces (optional)\n", "        zeta_batch_trace.append(zeta_b)\n", "\n", "        # advance\n", "        cur = end\n", "\n", "    # periodic eval every 10 epochs (to mirror your AdLaLa cadence)\n", "    if (epoch + 1) % 10 == 0:\n", "        with torch.no_grad():\n", "            # training snapshot on last minibatch\n", "            acc = accuracy_Nclass(out.detach().cpu().numpy(), yb.detach().cpu().numpy().squeeze())\n", "            # acc = accuracy_Nclass(out.detach().numpy(), yb.detach().numpy())\n", "            # test snapshot (all test at once)\n", "            out_test = NN(xtest)\n", "            loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "            acc_test  = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "\n", "            RES.append([epoch, (loss.item() / len(xb)), acc, loss_test.item(), acc_test])\n", "\n", "        <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "        tkin_trace.append(Tkin)\n", "        tconf_trace.append(Tconf)\n", "        eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBABS = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "# ---- Plots ----\n", "if len(RES_ZBABS):\n", "    epochs_plot = RES_ZBABS[:,0]\n", "    tr_loss     = RES_ZBABS[:,1]\n", "    tr_acc      = RES_ZBABS[:,2]\n", "    te_loss     = RES_ZBABS[:,3]\n", "    te_acc      = RES_ZBABS[:,4]\n", "\n", "    plt.figure(figsize=[18,10]) # Increase the size of the plots\n", "    plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "    plt.subplot(2,3,1)\n", "    plt.plot(epochs_plot, tr_loss, label='Train Loss')\n", "    plt.plot(epochs_plot, te_loss, label='Test Loss')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid()\n", "    plt.ylim([0,0.8])\n", "    plt.title(r'Adaptive Batchsize Loss function')\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,2)\n", "    plt.plot(epochs_plot, tr_acc, label='Train Acc')\n", "    plt.plot(epochs_plot, te_acc, label='Test Acc')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy (in %)')\n", "    plt.title(r'Adaptive Batchsize Prediction Accuracy')\n", "    plt.grid()\n", "    plt.ylim([40,100])\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,3)\n", "    xx = np.linspace(-2,2,100)\n", "    X,Y = np.meshgrid(xx,xx)\n", "    Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "    out = NN(torch.tensor(Z.T).float())\n", "\n", "    out=out.reshape(100,100)\n", "    plt.pcolor(xx,xx,out.data)\n", "    plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "    plt.axis('square')\n", "\n", "    kk1 = ytest.reshape(-1)==0\n", "    kk2 = ytest.reshape(-1)==1\n", "    plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "    plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "    plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "    \n", "    plt.subplot(2,3,4)\n", "    plt.plot(dt_trace, lw=1)\n", "    plt.title('Adaptive step size Δt'); plt.grid(True); plt.xlabel('Iteration')\n", "\n", "    plt.subplot(2,3,5)\n", "    plt.hist(batch_trace[burn_in_epochs:], bins=50, density=True, label='Batch Size Distribution')\n", "    plt.axvline(np.mean(batch_trace[burn_in_epochs:]), color='r', linestyle='--', label=f'Mean: {np.mean(batch_trace[burn_in_epochs:]):.2f}')\n", "    plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "    plt.xlabel('<PERSON><PERSON> Size')\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "\n", "    # Tkin/Tconf at eval cadence\n", "    plt.subplot(2,3,6)\n", "    plt.plot(eval_epoch_idx, tkin_trace, marker='o', label='Tkin')\n", "    plt.plot(eval_epoch_idx, tconf_trace, marker='s', label='Tconf')\n", "    plt.axhline(temperature, color='k', ls='--', label=f'Temperature: {temperature}')\n", "    plt.title('Temperatures'); plt.xlabel('Epoch'); plt.ylabel('Value'); plt.legend(); plt.grid(True)\n", "    plt.show()\n", "\n", "    fig, ax = plt.subplots(1,2, figsize=(16,4))\n", "    ax[0].plot(batch_trace)\n", "    ax[0].set_title('Adaptive batch size B'); ax[0].grid(True); ax[0].set_xlabel('Iteration')\n", "\n", "    ax[1].plot(zeta_batch_trace)\n", "    ax[1].set_title(r'$\\zeta_{\\sigma}$'); ax[1].grid(True); ax[1].set_xlabel('Iteration')\n", "    plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["epoch 499/4000, test loss = 0.3185, train acc = 92.00%, test acc = 81.60%\n", "epoch 999/4000, test loss = 0.2815, train acc = 80.00%, test acc = 79.70%\n", "epoch 1499/4000, test loss = 0.4060, train acc = 84.62%, test acc = 80.80%\n", "epoch 1999/4000, test loss = 2.2412, train acc = 100.00%, test acc = 90.90%\n", "epoch 2499/4000, test loss = 0.1188, train acc = 90.00%, test acc = 92.50%\n", "epoch 2999/4000, test loss = 0.1062, train acc = 90.00%, test acc = 93.90%\n", "epoch 3499/4000, test loss = 0.0774, train acc = 95.00%, test acc = 95.90%\n", "epoch 3999/4000, test loss = 0.0893, train acc = 100.00%, test acc = 93.90%"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*****"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# ===== ZBAOABZ + Adaptive Batch Size on Two-Spirals =====\n", "\n", "# --- Hyperparameters ---\n", "# ZBAOABZ (step-size) control\n", "dtau         = 4e-2\n", "gamma        = 1.0\n", "alpha        = 5.0\n", "alpha2       = 1.0 / Ntrain\n", "m, M         = 0.1, 10.0\n", "temperature  = 1e-4\n", "r_exp        = 0.25\n", "weight_decay = 0\n", "\n", "# Adaptive batch-size control (simple version for spirals)\n", "burn_in_epochs   = 1000\n", "min_B            = 10\n", "max_B            = 200\n", "init_B           = 25                # start at your old batch size\n", "post_burn_init_B = min_B             # reset once after burn-in\n", "alpha_batch      = 5.0\n", "dtau_batch       = 0.05\n", "scale_g_batch    = 1             # scales Tkin monitor\n", "r_batch          = 0.5              # curvature of mapping\n", "steepness_batch  = 0.1\n", "threshold_batch  = 1.0\n", "\n", "# helper: monotone mapping between sigma and batch size (inverse: B = N/(sigma+1))\n", "Ntotal     = Ntrain\n", "sigma_from_B = lambda B: (Ntotal - B) / max(B, 1)\n", "sigma_min  = sigma_from_B(max_B)\n", "sigma_max  = sigma_from_B(min_B)\n", "\n", "\n", "def psi_batch(zeta_b):\n", "    \"\"\"Map auxiliary zeta_b to sigma via atan (decreasing).\"\"\"\n", "    mid   = 0.5 * (sigma_min + sigma_max)\n", "    span  = (sigma_max - sigma_min)\n", "    # arctan with sign + exponent shaping\n", "    val   = np.arctan(steepness_batch * np.sign(zeta_b) * (abs(zeta_b)**r_batch))\n", "    return mid - (span / np.pi) * val\n", "\n", "# measure Tkin / Tconf\n", "@torch.no_grad()\n", "def measure_temperatures(model, optimizer):\n", "    kin_sum = 0.0\n", "    conf_sum = 0.0\n", "    count = 0\n", "    for p in model.parameters():\n", "        st = optimizer.state.get(p, None)\n", "        if st is not None and 'momentum_buffer' in st:\n", "            buf = st['momentum_buffer']\n", "            kin_sum += (buf * buf).sum().item()\n", "            count   += buf.numel()\n", "        if p.grad is not None:\n", "            conf_sum += (p.data * p.grad).sum().item()\n", "    if count == 0:\n", "        return float('nan'), float('nan')\n", "    return kin_sum / count, conf_sum / count\n", "\n", "# Build model/optimizer/criterion\n", "NN = Net()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = ZBAOABZ(\n", "    NN.parameters(),\n", "    dtau=dtau, weight_decay=weight_decay, gamma=gamma,\n", "    alpha=alpha, alpha2=alpha2, m=m, M=M, temperature=temperature, r=r_exp\n", ")\n", "\n", "# Training traces\n", "RES = []\n", "dt_trace, batch_trace = [], []\n", "tkin_trace, tconf_trace, eval_epoch_idx = [], [], []\n", "zeta_batch_trace = []\n", "\n", "# adaptive-batch state\n", "batch_size = init_B\n", "zeta_b     = 0.0\n", "rho_b      = np.exp(-alpha_batch * dtau_batch)\n", "post_burn_reset_done = False\n", "\n", "num_epochs = 4000\n", "for epoch in range(num_epochs):\n", "    NN.train()\n", "\n", "    # one-time reset to a large batch after burn-in (optional but helpful)\n", "    if epoch == burn_in_epochs and not post_burn_reset_done:\n", "        batch_size = post_burn_init_B\n", "        post_burn_reset_done = True\n", "\n", "    # shuffle indices for manual batching\n", "    idx = torch.randperm(Ntrain)\n", "    cur = 0\n", "    while cur < Ntrain:\n", "        # ---- <PERSON>E (<PERSON> <PERSON>) ----\n", "        # build a batch with the *current* adaptive batch size\n", "        end = min(cur + batch_size, Ntrain)\n", "        batch_idx = idx[cur:end]\n", "        xb = datanew.x[batch_idx]\n", "        yb = datanew.y[batch_idx].float().unsqueeze(1)\n", "\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)       # \"sum then /datasize\" style\n", "        loss.backward()\n", "        optimizer.step_pre()\n", "\n", "        # record current dt (per-iteration)\n", "        dt_trace.append(optimizer.get_dt())\n", "        batch_trace.append(batch_size)\n", "\n", "        # ---- BAOA ----\n", "        optimizer.step_BAOA()\n", "\n", "        # ---- recompute grads on updated params ----\n", "        NN.zero_grad(set_to_none=True)\n", "        out = NN(xb)\n", "        loss = criterion(out, yb) * len(xb)\n", "        loss.backward()\n", "\n", "        # ---- B ----\n", "        optimizer.step_B()\n", "\n", "        # ---- POST (<PERSON> + <PERSON>) ----\n", "        optimizer.step_post()\n", "\n", "        # ---- Adaptive Batch Update (after burn-in) ----\n", "        if epoch >= burn_in_epochs:\n", "            # monitor: use Tkin (noisy but cheap)\n", "            _, Tconf = measure_temperatures(NN, optimizer)\n", "            g_val = np.abs(Tconf - temperature) / temperature if temperature > 0 else np.abs(Tconf)\n", "            g_val *= scale_g_batch\n", "            # zeta_b update\n", "            zeta_b = rho_b * zeta_b + (1 - rho_b) * g_val / max(alpha_batch, 1e-8)\n", "            # map to sigma, clamp, then to batch size\n", "            sigma = np.clip(psi_batch(zeta_b), sigma_min, sigma_max)\n", "            new_B = int(np.round(np.clip(Ntotal / (sigma + 1.0), min_B, max_B)))\n", "            batch_size = new_B\n", "\n", "        # traces (optional)\n", "        zeta_batch_trace.append(zeta_b)\n", "\n", "        # advance\n", "        cur = end\n", "\n", "    # periodic eval every 10 epochs (to mirror your AdLaLa cadence)\n", "    if (epoch + 1) % 10 == 0:\n", "        with torch.no_grad():\n", "            # training snapshot on last minibatch\n", "            acc = accuracy_Nclass(out.detach().cpu().numpy(), yb.detach().cpu().numpy().squeeze())\n", "            # acc = accuracy_Nclass(out.detach().numpy(), yb.detach().numpy())\n", "            # test snapshot (all test at once)\n", "            out_test = NN(xtest)\n", "            loss_test = criterion(out_test, ytest.float().unsqueeze(1))\n", "            acc_test  = accuracy_Nclass(out_test.detach().numpy(), ytest.detach().numpy())\n", "\n", "            RES.append([epoch, (loss.item() / len(xb)), acc, loss_test.item(), acc_test])\n", "\n", "        <PERSON><PERSON>, Tconf = measure_temperatures(NN, optimizer)\n", "        tkin_trace.append(Tkin)\n", "        tconf_trace.append(Tconf)\n", "        eval_epoch_idx.append(epoch)\n", "\n", "    if (epoch + 1) % 500 == 0:\n", "        print(f\"epoch {epoch}/{num_epochs}, \"\n", "              f\"test loss = {loss_test.item():.4f}, \"\n", "              f\"train acc = {acc:.2f}%, test acc = {acc_test:.2f}%\")\n", "\n", "RES_ZBABS = np.vstack(RES) if len(RES) else np.zeros((0,5))\n", "\n", "# ---- Plots ----\n", "if len(RES_ZBABS):\n", "    epochs_plot = RES_ZBABS[:,0]\n", "    tr_loss     = RES_ZBABS[:,1]\n", "    tr_acc      = RES_ZBABS[:,2]\n", "    te_loss     = RES_ZBABS[:,3]\n", "    te_acc      = RES_ZBABS[:,4]\n", "\n", "    plt.figure(figsize=[17,10]) # Increase the size of the plots\n", "    plt.rcParams.update({'font.size': 18}) # Increase the size of the text in the plots\n", "    plt.subplot(2,3,1)\n", "    plt.plot(epochs_plot, tr_loss, label='Train Loss')\n", "    plt.plot(epochs_plot, te_loss, label='Test Loss')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid()\n", "    plt.ylim([0,0.8])\n", "    plt.title(r'Adaptive Batchsize Loss function')\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,2)\n", "    plt.plot(epochs_plot, tr_acc, label='Train Acc')\n", "    plt.plot(epochs_plot, te_acc, label='Test Acc')\n", "    plt.legend()\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy (in %)')\n", "    plt.title(r'Adaptive Batchsize Prediction Accuracy')\n", "    plt.grid()\n", "    plt.ylim([40,100])\n", "    plt.tight_layout()\n", "\n", "    plt.subplot(2,3,3)\n", "    xx = np.linspace(-2,2,100)\n", "    X,Y = np.meshgrid(xx,xx)\n", "    Z = np.vstack([X<PERSON>reshape(-1),Y.reshape(-1)])\n", "    out = NN(torch.tensor(Z.T).float())\n", "\n", "    out=out.reshape(100,100)\n", "    plt.pcolor(xx,xx,out.data)\n", "    plt.contour(xx,xx,out.data,[0.5],alpha=0.5)\n", "    plt.axis('square')\n", "\n", "    kk1 = ytest.reshape(-1)==0\n", "    kk2 = ytest.reshape(-1)==1\n", "    plt.scatter(xtest[kk1,0],xtest[kk1,1])\n", "    plt.scatter(xtest[kk2,0],xtest[kk2,1])\n", "    plt.title(r'Output classification plane (T=$10^{-4}$)')\n", "    \n", "    plt.subplot(2,3,4)\n", "    plt.plot(dt_trace, lw=1)\n", "    plt.title('Adaptive step size Δt'); plt.grid(True); plt.xlabel('Iteration')\n", "\n", "    plt.subplot(2,3,5)\n", "    plt.hist(batch_trace[burn_in_epochs:], bins=50, density=True, label='Batch Size Distribution')\n", "    plt.axvline(np.mean(batch_trace[burn_in_epochs:]), color='r', linestyle='--', label=f'Mean: {np.mean(batch_trace[burn_in_epochs:]):.2f}')\n", "    plt.title('Histogram of Adaptive <PERSON><PERSON> (Post Burn-in)')\n", "    plt.xlabel('<PERSON><PERSON> Size')\n", "    plt.ylabel('Density')\n", "    plt.legend()\n", "\n", "    # Tkin/Tconf at eval cadence\n", "    plt.subplot(2,3,6)\n", "    plt.plot(eval_epoch_idx, tkin_trace, marker='o', label='Tkin')\n", "    plt.plot(eval_epoch_idx, tconf_trace, marker='s', label='Tconf')\n", "    plt.axhline(temperature, color='k', ls='--', label=f'Temperature: {temperature}')\n", "    plt.title('Temperatures'); plt.xlabel('Epoch'); plt.ylabel('Value'); plt.legend(); plt.grid(True)\n", "    plt.show()\n", "\n", "    fig, ax = plt.subplots(1,2, figsize=(16,4))\n", "    ax[0].plot(batch_trace)\n", "    ax[0].set_title('Adaptive batch size B'); ax[0].grid(True); ax[0].set_xlabel('Iteration')\n", "\n", "    ax[1].plot(zeta_batch_trace)\n", "    ax[1].set_title(r'$\\zeta_{\\sigma}$'); ax[1].grid(True); ax[1].set_xlabel('Iteration')\n", "    plt.tight_layout(); plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["epoch 3999/4000, test loss = 0.0893, train acc = 100.00%, test acc = 93.90%\n", "epoch 3999/4000, test loss = 5.1790, train acc = 100.00%, test acc = 86.90%\n", "epoch 3999/4000, test loss = 0.1130, train acc = 100.00%, test acc = 98.20%\n", "epoch 3999/4000, test loss = 0.0214, train acc = 100.00%, test acc = 99.20%\n", "epoch 3999/4000, test loss = 0.2926, train acc = 100.00%, test acc = 90.70%\n", "epoch 3999/4000, test loss = 0.2488, train acc = 100.00%, test acc = 91.30%\n", "epoch 3999/4000, test loss = 0.2130, train acc = 100.00%, test acc = 95.80%\n", "\n", "# threshold_batch  = 0.75, r_batch = 1, steepness_batch  = 0.1, alpha_batch = 5.0, dtau_batch = 0.05\n", "epoch 3999/4000, test loss = 0.1391, train acc = 100.00%, test acc = 99.10%\n", "epoch 3999/4000, test loss = 0.2005, train acc = 93.33%, test acc = 87.90%\n", "epoch 3999/4000, test loss = 0.0484, train acc = 100.00%, test acc = 98.20%   # mean batch = 35\n", "epoch 3999/4000, test loss = 0.3181, train acc = 96.15%, test acc = 82.10%\n", "epoch 3999/4000, test loss = 0.0610, train acc = 100.00%, test acc = 97.30%\n", "epoch 3999/4000, test loss = 0.0216, train acc = 100.00%, test acc = 99.00%\n", "\n", "epoch 3999/4000, test loss = 0.0317, train acc = 100.00%, test acc = 98.40%\n", "epoch 3999/4000, test loss = 0.2026, train acc = 100.00%, test acc = 97.10%\n", "epoch 3999/4000, test loss = 0.0977, train acc = 100.00%, test acc = 95.20%\n", "epoch 3999/4000, test loss = 0.0414, train acc = 100.00%, test acc = 99.10%\n", "epoch 3999/4000, test loss = 0.0375, train acc = 100.00%, test acc = 98.70%\n", "epoch 3999/4000, test loss = 0.0669, train acc = 100.00%, test acc = 97.80%\n", "epoch 3999/4000, test loss = 0.1863, train acc = 100.00%, test acc = 95.60%\n", "epoch 3999/4000, test loss = 0.0313, train acc = 100.00%, test acc = 98.20%\n", "epoch 3999/4000, test loss = 0.0181, train acc = 100.00%, test acc = 99.60%\n", "epoch 3999/4000, test loss = 0.0977, train acc = 100.00%, test acc = 98.70%\n", "epoch 3999/4000, test loss = 0.0276, train acc = 100.00%, test acc = 99.10%\n", "epoch 3999/4000, test loss = 0.0260, train acc = 100.00%, test acc = 99.50%\n", "epoch 3999/4000, test loss = 0.0163, train acc = 100.00%, test acc = 99.30%\n", "epoch 3999/4000, test loss = 0.2160, train acc = 100.00%, test acc = 93.40%\n", "epoch 3999/4000, test loss = 0.1194, train acc = 100.00%, test acc = 95.50%\n", "epoch 3999/4000, test loss = 2.1969, train acc = 100.00%, test acc = 90.10%\n", "epoch 3999/4000, test loss = 0.1549, train acc = 100.00%, test acc = 96.70%\n", "epoch 3999/4000, test loss = 0.0396, train acc = 100.00%, test acc = 98.30%\n", "epoch 3999/4000, test loss = 0.0293, train acc = 100.00%, test acc = 99.00%\n", "epoch 3999/4000, test loss = 0.0187, train acc = 100.00%, test acc = 99.40%\n", "\n", "epoch 3999/4000, test loss = 0.2760, train acc = 95.65%, test acc = 85.70%\n", "epoch 3999/4000, test loss = 0.1275, train acc = 95.65%, test acc = 93.70%\n", "epoch 3999/4000, test loss = 0.4114, train acc = 97.62%, test acc = 97.00%\n", "epoch 3999/4000, test loss = 0.1234, train acc = 100.00%, test acc = 96.50%\n", "epoch 3999/4000, test loss = 0.7849, train acc = 90.00%, test acc = 83.60%\n", "epoch 3999/4000, test loss = 0.3644, train acc = 71.43%, test acc = 77.00%   # stable in temperature\n", "epoch 3999/4000, test loss = 0.0656, train acc = 100.00%, test acc = 97.60%\n", "epoch 3999/4000, test loss = 0.0921, train acc = 100.00%, test acc = 95.70%\n", "epoch 3999/4000, test loss = 0.0925, train acc = 100.00%, test acc = 97.00%\n", "epoch 3999/4000, test loss = 0.1107, train acc = 89.47%, test acc = 95.90%\n", "epoch 3999/4000, test loss = 0.2327, train acc = 71.43%, test acc = 87.50%\n", "epoch 3999/4000, test loss = 0.3096, train acc = 91.67%, test acc = 84.60%\n", "epoch 3999/4000, test loss = 0.0270, train acc = 95.24%, test acc = 98.70%\n", "epoch 3999/4000, test loss = 0.3659, train acc = 75.00%, test acc = 74.70%\n", "epoch 3999/4000, test loss = 0.2077, train acc = 100.00%, test acc = 90.50%\n", "epoch 3999/4000, test loss = 0.0317, train acc = 100.00%, test acc = 98.00%\n", "epoch 3999/4000, test loss = 0.2554, train acc = 100.00%, test acc = 83.20%"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": [" "]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}