{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "bGU6NwlsXFSt"}, "outputs": [], "source": ["#@title Import Dependencies\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.datasets as dsets\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import itertools\n", "import pickle\n", "import numpy as np\n", "from numpy import random\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "from typing import TypeVar, Dict\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.optim.optimizer import Optimizer\n", "\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "torch.set_float32_matmul_precision('high')\n", "\n", "import copy\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["!pip install scipy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["input_size = 28*28*1 # img_size = (28,28) ---> 28*28=784 in total\n", "batch_size = 200 # the size of input data took for one iteration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lCsBCXMwbpH5"}, "outputs": [], "source": ["transform=transforms.Compose([transforms.ToTensor()])\n", "\n", "train_data = dsets.FashionMNIST(root = './data', train=True, transform = transform, download = True)\n", "test_data = dsets.FashionMNIST(root = './data', train=False, transform = transform, download = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rfDPBdnYgfGp"}, "outputs": [], "source": ["#@title Loading the data\n", "\n", "train_gen = torch.utils.data.DataLoader(dataset = train_data,\n", "                                             batch_size = batch_size,\n", "                                             shuffle = True)\n", "\n", "test_gen = torch.utils.data.DataLoader(dataset = test_data,\n", "                                      batch_size = batch_size,\n", "                                      shuffle = False)\n", "\n", "no_batches=len(train_gen)\n", "test_no_batches=len(test_gen)\n", "train_data_len=len(train_data)\n", "test_data_len=len(test_data)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#cvmx=torch.zeros([3*64*64,3*64*64],device=device)\n", "images_list=[]\n", "labels_list=[]\n", "no_batches=len(train_gen)\n", "#images_mean=torch.zeros(3,64,64,device=device)\n", "for i ,(images,labels) in enumerate(train_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    # images_mean=images_mean+images.mean(0)\n", "    # im=torch.reshape(images,[images.shape[0],3*64*64])\n", "    # cvmx+=torch.matmul(torch.transpose(im,0,1),im)\n", "    if(i<(len(train_gen))):\n", "        images_list.append(images)\n", "        labels_list.append(labels)\n", "\n", "\n", "\n", "test_images_list=[]\n", "test_labels_list=[]\n", "test_no_batches=len(test_gen)\n", "for i ,(images,labels) in enumerate(test_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    if(i<(len(test_gen))):\n", "        test_images_list.append(images)\n", "        test_labels_list.append(labels)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fL-Y<PERSON><PERSON><PERSON><PERSON>_"}, "outputs": [], "source": ["#@title Define model class\n", "import torch.nn as nn\n", "import torch\n", "import torch.nn.functional as F\n", "\n", "from typing import TypeVar, <PERSON><PERSON>\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "class NeuralNet(torch.nn.Module):\n", "    \"\"\"\n", "    base class for all NN classifiers\n", "    \"\"\"\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def initialize_weights(self):\n", "        for m in self.modules():\n", "            if isinstance(m, torch.nn.Conv2d):\n", "                torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None:\n", "                    torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.BatchNorm2d):\n", "                torch.nn.init.constant_(m.weight, 1)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.Linear):\n", "                torch.nn.init.normal_(m.weight, 0, 0.01)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "\n", "\n", "class CNN(NeuralNet):\n", "    \"\"\"\n", "    CNN for (binary) classification for CelebA, CheXpert\n", "    \"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 3,\n", "                 num_classes: int = 2,\n", "                 flattened_size: int = 16384,\n", "                 low_rank: int = 32,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(CNN, self).__init__()\n", "\n", "        self.conv_layer = nn.Sequential(\n", "            # Conv Layer block 1\n", "            nn.Conv2d(in_channels=in_channels, out_channels=32, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.<PERSON><PERSON><PERSON>orm2d(32,momentum=1.0),\n", "            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "            nn.<PERSON><PERSON><PERSON>orm2d(64,momentum=1.0),\n", "            # Conv Layer block 2\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.<PERSON><PERSON><PERSON>orm2d(64,momentum=1.0),\n", "            nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.<PERSON>chNorm2d(128,momentum=1.0),\n", "            nn.Conv2d(in_channels=128, out_channels=128, kernel_size=3, padding=1),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "            # Conv Layer block 3\n", "            nn.<PERSON>chNorm2d(128,momentum=1.0),\n", "            nn.Conv2d(in_channels=128, out_channels=256, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.<PERSON>ch<PERSON>orm2d(256,momentum=1.0),\n", "            nn.Conv2d(in_channels=256, out_channels=256, kernel_size=3, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2),\n", "        )\n", "\n", "        self.fc_layer = nn.Sequential(\n", "            nn.<PERSON>(),\n", "            nn.BatchNorm1d(flattened_size,momentum=1.0),\n", "            nn.Linear(flattened_size, low_rank),\n", "            nn.BatchNorm1d(low_rank,momentum=1.0),\n", "            nn.<PERSON><PERSON>(low_rank,512),            \n", "            nn.Softplus(beta=1.0),\n", "            nn.BatchNorm1d(512,momentum=1.0),\n", "        )\n", "\n", "        self.last_layer=nn.Sequential(\n", "            nn.Linear(512, num_classes)\n", "        )\n", "\n", "    def forward(self, x: Tensor) -> Tensor:\n", "        \"\"\"Perform forward.\"\"\"\n", "\n", "        # conv layers\n", "        x = self.conv_layer(x)\n", "\n", "        # fc layer\n", "        x = self.fc_layer(x)\n", "\n", "        # last layer\n", "        x=self.last_layer(x)\n", "\n", "        return x\n", "\n", "    def classify(self, x: Tensor) -> <PERSON><PERSON>[Tensor, Tensor, Tensor]:\n", "        net_out = self.forward(x)\n", "        acc = F.softmax(net_out, dim=1)\n", "        class_idx = torch.max(net_out, 1)[1]\n", "\n", "        return acc, acc[0, class_idx], class_idx\n", "    \n", "\n", "\n", "\n", "\n", "class CelebA_CNN(CNN):\n", "    \"\"\"CNN.\"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 3,\n", "                 num_classes: int = 2,\n", "                 #num_classes: int = 1,\n", "                 flattened_size: int = 16384):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(CelebA_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                         flattened_size=flattened_size)\n", "\n", "class Fashion_MNIST_CNN(CNN):\n", "    \"\"\"CNN.\"\"\"\n", "\n", "    def __init__(self,\n", "                 in_channels: int = 1,\n", "                 num_classes: int = 10,\n", "                 #flattened_size: int = 6272,\n", "                 flattened_size: int = 2304,\n", "                 low_rank: int = 64,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(Fashion_MNIST_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                         flattened_size=flattened_size)\n", "\n", "\n", "\n", "\n", "class CheXpert_CNN(CNN):\n", "    def __init__(self,\n", "                 in_channels: int = 1,\n", "                 num_classes: int = 2,\n", "                 flattened_size: int = 65536):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(Che<PERSON>pert_CNN, self).__init__(in_channels=in_channels, num_classes=num_classes,\n", "                                           flattened_size=flattened_size)\n", "\n", "\n", "class Net(nn.Module):\n", "  def __init__(self, input_size, hidden_size, num_classes):\n", "    super(Net,self).__init__()\n", "    self.fc1 = nn.Linear(input_size, hidden_size)\n", "    self.relu = nn.ReLU()\n", "    self.fc2 = nn.Linear(hidden_size, num_classes)\n", "\n", "  def forward(self,x):\n", "    out = self.fc1(x)\n", "    out = self.relu(out)\n", "    out = self.fc2(out)\n", "    return out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# net=Fashion_MNIST_CNN()\n", "# n=0\n", "# for par in net.parameters():\n", "#     n+=par.numel()\n", "\n", "# n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ePLIwvAFj2zH"}, "outputs": [], "source": ["#@title Define loss-function & optimizer\n", "loss_function = nn.CrossEntropyLoss()\n", "\n", "\n", "def images_regulariser(): \n", "    li_reg_loss = 0\n", "    penalized     = [p for name,p in net.named_parameters() if 'bias' not in name]\n", "    not_penalized = [p for name,p in net.named_parameters() if 'bias' in name]\n", "    for p in penalized:\n", "        li_reg_loss += (p**2).sum()*0.5\n", "    #for p in net.parameters():\n", "#        li_reg_loss += (p**2).sum()*0.5\n", "    reg=li_reg_loss/(train_data_len)*l2regconst\n", "    return(reg)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def addnet(net,net2):\n", "    for param1, param2 in zip(net.parameters(), net2.parameters()):\n", "     param1.data += param2.data\n", "\n", "def multiplynet(net,a):\n", "   for param1 in net.parameters():\n", "     param1.data *=a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "@dataclass\n", "class hclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    etam1g: Tensor\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc):\n", "    xi1=torch.randn(x.size(),device=device)\n", "    xi2=torch.randn(x.size(),device=device)\n", "\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n", "\n", "def bounce(x,v,xstar,width):\n", "    vsign=(((x-xstar+width)/(2*width)).floor()% 2)*(-2)+1\n", "    vn=v*vsign\n", "    xn=((x-xstar-width)% (4*width)-2*width).abs()-width+xstar\n", "\n", "    return([xn, vn])\n", "\n", "def bouncenet():\n", "    for p,p_star in zip(net.parameters(),net_star.parameters()):\n", "        [p.data, p.v]=bounce(p.data, p.v, p_star.data, 6/torch.sqrt(l2regconst_extra))\n", "\n", "def UBU_step(hper2c,images,labels,batch_it):   \n", "    with torch.no_grad():\n", "        for p in list(net.parameters()):    \n", "\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "        bouncenet()\n", "\n", "    outputsU = net(images)\n", "    loss_likelihood = loss_function(outputsU, labels)  \n", "\n", "\n", "    grads_reg=[torch.zeros_like(par) for par in net.parameters()]\n", "    net_pars=list(net.parameters())\n", "    with torch.no_grad():\n", "        for it in range(len_params):\n", "            if(list_no_bias[it]):\n", "                grads_reg[it]=net_pars[it].data*l2regconst\n", "\n", "    net.zero_grad()\n", "    #loss.backward()\n", "    loss_likelihood.backward()\n", "    with torch.no_grad():\n", "        grads_likelihood=[par.grad*batch_size for par in net.parameters()]\n", "    \n", "        #Normal, no variance reduction\n", "        # for p,p_star in zip(net.parameters(),net_star.parameters()):      \n", "        #     p.v-=hper2c.h*(p.grad*train_data_len+l2regconst_extra*(p.data-p_star.data))\n", "\n", "        for p,grad,grad_reg,p_star,grad_star,star_sum_grad in zip(list(net.parameters()),grads_likelihood,grads_reg,list(net_star.parameters()),net_star_grad_list[batch_it],net_star_full_grad):              \n", "            #Using variance reduction\n", "            p.v-=hper2c.h*(grad_reg+star_sum_grad+(grad-grad_star)*no_batches+l2regconst_extra*(p.data-p_star.data))\n", "\n", "            # maxlen=20*torch.sqrt((torch.tensor(torch.numel(p.v))).float())\n", "            # if(torch.norm(p.v)>maxlen):\n", "            #     print(\"trouble\")\n", "                #p.v=(p.v/torch.norm(p.v))*maxlen\n", "        # for it in range(len_params):\n", "        #     [list(net.parameters())[it].data,list(net.parameters())[it].v]=U(list(net.parameters())[it].data,list(net.parameters())[it].v,hper2c)        \n", "        for p in list(net.parameters()):\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "    #bouncenet()\n", "    return(loss_likelihood.data)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "u75Xa5VckuTH"}, "outputs": [], "source": ["#@title Output arrays\n", "par_runs=64\n", "num_classes=10\n", "num_epochs=60\n", "switch_to_sampling_epoch=20\n", "switch_to_swa_epoch=15\n", "\n", "num_swag_epochs=switch_to_sampling_epoch-switch_to_swa_epoch\n", "\n", "training_size=no_batches*batch_size\n", "test_size=test_data_len\n", "labels_arr=torch.zeros(training_size)\n", "test_labels_arr=torch.zeros(test_size)\n", "test_prob_arr=torch.zeros([test_size,num_classes,num_epochs,par_runs])\n", "Fashion_MNIST_CNN=torch.compile(Fashion_MNIST_CNN)\n", "images_regulariser=torch.compile(images_regulariser)\n", "loss_function=torch.compile(loss_function)\n", "UBU_step=torch.compile(UBU_step)\n", "\n", "lr = 1e-2\n", "lr_swag=1e-3\n", "h=2.5e-4\n", "l2regconst=torch.tensor(1).cuda().detach()\n", "l2regconst_extra=torch.tensor(50).cuda().detach()\n", "gam=torch.sqrt(l2regconst_extra)\n", "hper2c=hper2const(torch.tensor(h/2),gam)\n", "\n", "for par_it in range(par_runs):\n", "  print(\"par_it:\",par_it,\"\\n\")\n", "  #@title Build the model\n", "  net = Fashion_MNIST_CNN().cuda()\n", "  net.train()\n", "  optimizer = torch.optim.Adam( net.parameters(), lr=lr)\n", "  \n", "  lr_scheduler = torch.optim.lr_scheduler.PolynomialLR(optimizer=optimizer, total_iters=switch_to_swa_epoch,power=1)\n", "\n", "\n", "  #@title Training the model\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=torch.zeros(1).cuda().detach()\n", "    #l2regconst=torch.min(torch.tensor(1+epoch),torch.tensor(switch_to_swa_epoch)).detach()\n", "    net.train()\n", "    if(epoch==(switch_to_swa_epoch-1)):\n", "      net_star2=copy.deepcopy(net)\n", "      net2=copy.deepcopy(net)\n", "      multiplynet(net2,0)\n", "      optimizer=torch.optim.Adam(net.parameters(),lr=lr_swag)\n", "\n", "    if(epoch>=switch_to_sampling_epoch and (epoch-switch_to_sampling_epoch)%2==0):\n", "       rperm=random.permutation(list(range(no_batches)))\n", "\n", "      \n", "    for i in range(no_batches):\n", "      if(epoch<switch_to_sampling_epoch):\n", "        b=torch.randint(high=no_batches,size=(1,1))\n", "      else:\n", "        it=(epoch-switch_to_sampling_epoch)*no_batches+i\n", "        b=rperm[ind_create(it)]\n", "\n", "      images=images_list[b]\n", "      labels=labels_list[b]\n", "\n", "\n", "      if(epoch<switch_to_sampling_epoch):\n", "        outputs = net(images)    \n", "        loss_likelihood = loss_function(outputs, labels)\n", "        sum_loss=sum_loss+loss_likelihood.detach()    \n", "        reg=images_regulariser()\n", "        loss=loss_likelihood+reg\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        if(epoch>=(switch_to_swa_epoch)):\n", "          addnet(net2,net)\n", "      else:\n", "        loss_likelihood=UBU_step(hper2c,images,labels,b).detach()\n", "        sum_loss=sum_loss+loss_likelihood\n", "\n", "\n", "\n", "\n", "    #if (i+1) % (no_batches) == 0:\n", "    #print(\"Reg:\",reg)\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0\n", "    \n", "\n", "\n", "\n", "\n", "\n", "    #for imagest,labelst in eval_gen:\n", "    if epoch==(switch_to_sampling_epoch-1):\n", "      multiplynet(net2,1/(num_swag_epochs*no_batches))\n", "      multiplynet(net,0)\n", "      addnet(net,net2)\n", "      del net2\n", "\n", "      net_star=copy.deepcopy(net)\n", "      len_params=len(list(net_star.parameters()))\n", "\n", "      #Variance reduction - saving gradients at each batch at x_star\n", "      net_star_grad_list=[]\n", "      net_star_full_grad=[torch.zeros_like(par, device=device) for par in list(net_star.parameters())]\n", "      for i in range(no_batches):\n", "          images=images_list[i]\n", "          labels=labels_list[i]\n", "          outputs=net_star(images)\n", "          loss_likelihood = loss_function(outputs, labels)\n", "          reg=images_regulariser()\n", "          net_star.zero_grad()\n", "          loss_likelihood.backward()\n", "          grads=[par.grad*batch_size for par in list(net_star.parameters())]\n", "          net_star_grad_list.append(grads)\n", "          for g, gi in zip(net_star_full_grad,grads):\n", "            g+=gi          \n", "\n", "      len_params=len(list(net_star.parameters()))\n", "      list_no_bias=torch.zeros(len_params)\n", "      pit=0\n", "      for name, p in net_star.named_parameters():\n", "          if 'bias' not in name:\n", "              list_no_bias[pit]=1.0\n", "          pit+=1\n", "\n", "      #Initialise velocities\n", "      for par in list(net.parameters()):\n", "        par.v = torch.randn_like(par,device=device)      \n", "\n", "\n", "    with torch.no_grad():\n", "      net(torch.cat(images_list[0:50],dim=0).detach())        \n", "      net.eval()\n", "\n", "\n", "\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:,epoch,par_it]=torch.softmax(outputt,dim=1)\n", "    \n", "\n", "\n", "    \n", "\n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "\n", "    if(epoch<=switch_to_swa_epoch):\n", "      lr_scheduler.step()\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "    \n", "  filepath=\"output_fashion_SMS_UBU.pickle\"\n", "  with open(filepath,\"wb\") as file:\n", "    pickle.dump([labels_arr.numpy(),test_labels_arr.numpy(),test_prob_arr.numpy()],file)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n=0\n", "for par in net.parameters():\n", "    n+=par.numel()\n", "n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rps_single(probs, true_label,num_classes):\n", "    outcome=torch.zeros(num_classes)\n", "    outcome[true_label.int()]=1.0\n", "    cum_probs = torch.cumsum(probs,0)\n", "    cum_outcomes = torch.cumsum(outcome,0)\n", "    \n", "    sum_rps = 0\n", "    for i in range(len(outcome)):         \n", "        sum_rps+= (cum_probs[i] - cum_outcomes[i])**2\n", "    \n", "    return sum_rps/(num_classes-1)\n", "\n", "def rps_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    rps_vec=torch.zeros(test_data_len)\n", "    for it in range(test_data_len):\n", "        rps_vec[it]=rps_single(test_probs[it,:].reshape(num_classes),true_labels[it],num_classes)\n", "    return rps_vec\n", "\n", "def nll_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    res=0\n", "    for it in range(test_data_len):\n", "        res-=torch.max(torch.tensor([torch.log(test_probs[it,true_labels[it].int()]),-100]))\n", "    return res/test_data_len\n", "\n", "\n", "def adaptive_calibration_error(test_probs,true_labels, test_data_len, num_classes,num_bins=20):\n", "    o=torch.tensor(0.0).detach()\n", "    for k in range(num_classes):\n", "        ind=torch.argsort(test_probs[:,k],stable=True)        \n", "        testprobsk=test_probs[:,k]\n", "        sorted_probs=testprobsk[ind]\n", "        sorted_true_labels=true_labels[ind]\n", "\n", "        true_label_is_k = (sorted_true_labels==k).clone().detach().float()\n", "        bins=(torch.tensor(range(test_data_len))/torch.tensor(test_data_len/num_bins)).floor()\n", "\n", "        for b in range(num_bins):\n", "            mask = (bins == b)\n", "            if torch.any(mask):\n", "                o += (true_label_is_k[mask] - sorted_probs[mask]).mean().abs()\n", "\n", "    return o / (num_bins*num_classes)\n", "\n", "\n", "def compute_acc_ace_rps_no_bayes(es,par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_swa_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Non-Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "def compute_acc_ace_rps_swa(es,par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_sampling_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"SWA, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "#Bayesian\n", "def compute_acc_ace_rps_bayes(es,par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr): \n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,(switch_to_sampling_epoch+burnin_epochs):num_epochs,it*es:(it+1)*es]).mean(-1).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#no bayesian\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_no_bayes(pow(2,it),par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "cal_no_bayes=[acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std]\n", "\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_no_bayes_SMS_UBU.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "     \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#SWA\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_swa(pow(2,it),par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n", "cal_swa=[acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std]\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_swa_SMS_UBU.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Bayesian\n", "burnin_epochs=10\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_bayes(pow(2,it),par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_bayes_SMS_UBU.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def GRdiagnostics(res):\n", "#   J=res.shape[0] #Number of chains\n", "#   L=res.shape[1] #Number of samples after burnin\n", "#   res_means=res.mean(dim=1)\n", "#   res_mean=res_means.mean()\n", "#   B=(res_means-res_mean).pow(2).sum()*L/(J-1)\n", "#   W=(res_means.reshape([J,1])@torch.ones([1,L])-res).pow(2).sum()/(J*(L-1))\n", "#   R=(W*(L-1)/L+B/L)/W\n", "#   return R\n", "\n", "\n", "# par_chains=4\n", "# no_GR_epochs=40\n", "# test_prob_GR_arr=torch.zeros([test_size,num_classes])\n", "# nll_GR_arr=torch.zeros([par_chains,no_GR_epochs])\n", "# for chain in range(par_chains):\n", "#     net=copy.deepcopy(net_star)\n", "#     net.eval()\n", "#     for par in list(net.parameters()):\n", "#       par.v = torch.randn_like(par,device=device)          \n", "#     for epoch in range(no_GR_epochs):\n", "#       print(\"chain: \",chain, \"/epoch:\",epoch)\n", "#       if(epoch % 2 == 1):\n", "#         irange=range(no_batches-1,-1,-1)\n", "#       else:\n", "#         irange=range(no_batches)\n", "#       for b in irange:\n", "#         images=images_list[b]\n", "#         labels=labels_list[b]\n", "#         UBU_step(hper2c,images,labels,b)\n", "\n", "#       for testit in range(test_no_batches):\n", "#         imagest=test_images_list[testit]\n", "#         labelst=test_labels_list[testit]\n", "#         actual_batch_size=len(imagest)\n", "#         outputt = net(imagest).detach()\n", "#         test_prob_GR_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:]=torch.softmax(outputt,dim=1)\n", "      \n", "#       nll_GR_arr[chain,epoch]=nll_calc(test_prob_GR_arr,test_labels_arr)\n", "#       print(\"NLL:\", nll_GR_arr[chain,epoch])\n", "\n", "# print(GRdiagnostics(nll_GR_arr))"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Pytorch MNIST.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}