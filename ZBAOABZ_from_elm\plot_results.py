import numpy as np
import matplotlib.pyplot as plt

# Load data
data = np.loadtxt('zbaoabz_results.txt')
x_vals = data[:, 0]
theta_vals = data[:, 1]
zeta_vals = data[:, 2]
dt_vals = data[:, 3]

# Create plots
fig, axs = plt.subplots(1, 2, figsize=(12, 5))

# Left plot: trajectory in x-theta space
axs[0].plot(x_vals, theta_vals, 'b.', alpha=0.3, markersize=1)
axs[0].set_title("SamAdams trajectory in Neal's Funnel")
axs[0].set_xlabel("x")
axs[0].set_ylabel("theta")
axs[0].grid(True, alpha=0.3)

# Right plot: adaptive stepsize distribution
axs[1].hist(dt_vals, bins=50, density=True, alpha=0.7, color='blue')
mean_dt = np.mean(dt_vals)
axs[1].axvline(x=mean_dt, color='red', label=f"mean ∆t = {mean_dt:.6f}")
axs[1].set_title("Histogram of adaptive ∆t")
axs[1].set_xlabel("stepsize ∆t")
axs[1].set_ylabel("density")
axs[1].legend()
axs[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('zbaoabz_results.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"Total samples: {len(x_vals)}")
print(f"Mean stepsize: {mean_dt:.6f}")
print(f"Min stepsize: {np.min(dt_vals):.6f}")
print(f"Max stepsize: {np.max(dt_vals):.6f}")
