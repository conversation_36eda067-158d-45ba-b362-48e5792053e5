{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "bGU6NwlsXFSt"}, "outputs": [], "source": ["#@title Import Dependencies\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.datasets as dsets\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import itertools\n", "import pickle\n", "import numpy as np\n", "from numpy import random\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from PIL import Image\n", "from matplotlib import pyplot as plt\n", "import matplotlib\n", "from typing import TypeVar, Dict\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.optim.optimizer import Optimizer\n", "\n", "from counterfactuals.utils import make_dir, get_transforms, torch_to_image, expl_to_image\n", "from counterfactuals.plot import plot_grid_part\n", "from counterfactuals.generative_models.base import GenerativeModel\n", "from counterfactuals.classifiers.base import NeuralNet\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "#matplotlib.use('Agg')\n", "\n", "import click\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "from counterfactuals.classifiers.base import NeuralNet\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "import counterfactuals.classifiers.cnn as classifiers\n", "import counterfactuals.classifiers.unet as unet\n", "from counterfactuals.utils import load_checkpoint\n", "from counterfactuals.data import get_data_info\n", "from counterfactuals.generative_models.factory import get_generative_model\n", "import gdown\n", "import copy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["#@title Define Hyperparameters\n", "\n", "# class_names = [\"5_o_Clock_Shadow\", \"Arched_Eyebrows\", \"Attractive\", \"Bags_Under_Eyes\", \"Bald\", \"<PERSON><PERSON>\",\n", "#                 \"<PERSON>_Li<PERSON>\", \"Big_Nose\", \"Black_Hair\", \"Blond_Hair\", \"Blurry\", \"Brown_Hair\", \"Bushy_Eyebrows\",\n", "#                 \"<PERSON><PERSON>\", \"<PERSON>_<PERSON>\", \"Eyeglass<PERSON>\", \"<PERSON><PERSON>\", \"Gray_Hair\", \"Heavy_Makeup\", \"High_Cheekbones\",\n", "#                 \"Male\", \"Mouth_Slightly_Open\", \"<PERSON><PERSON>\", \"<PERSON>rrow_Eyes\", \"No_Beard\", \"Oval_Face\", \"<PERSON><PERSON>_Skin\",\n", "#                 \"<PERSON>y_Nose\", \"<PERSON><PERSON>_Hairline\", \"Rosy_Cheeks\", \"Sideburns\", \"Smiling\", \"Straight_Hair\",\n", "#                 \"Wavy_<PERSON>\", \"Wearing_Earrings\", \"Wearing_Hat\", \"Wearing_Lipstick\", \"Wearing_Necklace\",\n", "#                 \"<PERSON><PERSON>_<PERSON><PERSON>\", \"<PERSON>\"]\n", "# num_classes = 40\n", "# data_shape = [3, 64, 64]\n", "# n_bits = 5\n", "# temp = 0.7\n", "# data_mean = [0.485, 0.456, 0.406]\n", "# data_std = [0.229, 0.224, 0.225]\n", "\n", "input_size = 28*28*1 # img_size = (28,28) ---> 28*28=784 in total\n", "batch_size = 200 # the size of input data took for one iteration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lCsBCXMwbpH5"}, "outputs": [], "source": ["# from torchvision.transforms import v2\n", "# transform_RandomErasing=transforms.Compose([v2.RandomErasing(),\n", "#                               transforms.ToTensor()])\n", "#from torchvision.transforms import v2\n", "#transform=transforms.Compose([transforms.ToTensor(),transforms.Normalize(mean=0,std=1.0)])\n", "transform=transforms.Compose([transforms.ToTensor()])\n", "train_data = dsets.FashionMNIST(root = './data', train=True, transform = transform, download = True)\n", "test_data = dsets.FashionMNIST(root = './data', train=False, transform = transform, download = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rfDPBdnYgfGp"}, "outputs": [], "source": ["#@title Loading the data\n", "\n", "train_gen = torch.utils.data.DataLoader(dataset = train_data,\n", "                                             batch_size = batch_size,\n", "                                             shuffle = True)\n", "\n", "test_gen = torch.utils.data.DataLoader(dataset = test_data,\n", "                                      batch_size = batch_size,\n", "                                      shuffle = False)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#cvmx=torch.zeros([3*64*64,3*64*64],device=device)\n", "images_list=[]\n", "labels_list=[]\n", "no_batches=len(train_gen)\n", "#images_mean=torch.zeros(3,64,64,device=device)\n", "for i ,(images,labels) in enumerate(train_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    # images_mean=images_mean+images.mean(0)\n", "    # im=torch.reshape(images,[images.shape[0],3*64*64])\n", "    # cvmx+=torch.matmul(torch.transpose(im,0,1),im)\n", "    if(i<(len(train_gen))):\n", "        images_list.append(images)\n", "        labels_list.append(labels)\n", "\n", "\n", "\n", "test_images_list=[]\n", "test_labels_list=[]\n", "test_no_batches=len(test_gen)\n", "for i ,(images,labels) in enumerate(test_gen):\n", "    images = Variable(images).cuda().detach()\n", "    labels=Variable(labels).cuda().detach()\n", "    if(i<(len(test_gen))):\n", "        test_images_list.append(images)\n", "        test_labels_list.append(labels)\n", "\n", "train_data_len=len(train_data)\n", "test_data_len=len(test_data)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fL-Y<PERSON><PERSON><PERSON><PERSON>_"}, "outputs": [], "source": ["#@title Define model class\n", "import torch.nn as nn\n", "import torch\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "class NeuralNet(torch.nn.Module):\n", "    \"\"\"\n", "    base class for all NN classifiers\n", "    \"\"\"\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def initialize_weights(self):\n", "        for m in self.modules():\n", "            if isinstance(m, torch.nn.Conv2d):\n", "                torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None:\n", "                    torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.BatchNorm2d):\n", "                torch.nn.init.constant_(m.weight, 1)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.Linear):\n", "                torch.nn.init.normal_(m.weight, 0, 0.01)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "\n", "class multinomial(NeuralNet):\n", "    \"\"\"\n", "    CNN for (binary) classification for CelebA, CheXpert\n", "    \"\"\"\n", "\n", "    def __init__(self,\n", "                 num_classes: int = 10,\n", "                 flattened_size: int = 28*28):\n", "        \"\"\"Builder.\"\"\"\n", "        super(multinomial, self).__init__()\n", "\n", "        self.last_layer=nn.Sequential(\n", "            nn.<PERSON>(),\n", "            nn.Linear(flattened_size, num_classes)\n", "        )\n", "\n", "    def forward(self, x: Tensor) -> Tensor:\n", "        \"\"\"Perform forward.\"\"\"\n", "\n", "        x=self.last_layer(x)\n", "\n", "        return x\n", "\n", "    def classify(self, x: Tensor) -> <PERSON><PERSON>[Tensor, Tensor, Tensor]:\n", "        net_out = self.forward(x)\n", "        acc = F.softmax(net_out, dim=1)\n", "        class_idx = torch.max(net_out, 1)[1]\n", "\n", "        return acc, acc[0, class_idx], class_idx\n", "    \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# net=Fashion_MNIST_CNN()\n", "# n=0\n", "# for par in net.parameters():\n", "#     n+=par.numel()\n", "\n", "# n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ePLIwvAFj2zH"}, "outputs": [], "source": ["#@title Define loss-function & optimizer\n", "loss_function = nn.CrossEntropyLoss()\n", "\n", "\n", "def images_regulariser(net): \n", "    li_reg_loss = 0\n", "    penalized     = [p for name,p in net.named_parameters() if 'bias' not in name]\n", "    not_penalized = [p for name,p in net.named_parameters() if 'bias' in name]\n", "    for p in penalized:\n", "        li_reg_loss += (p**2).sum()*0.5\n", "    #for p in net.parameters():\n", "#        li_reg_loss += (p**2).sum()*0.5\n", "    reg=li_reg_loss/(train_data_len)*l2regconst\n", "    return(reg)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def addnet(net,net2):\n", "    for param1, param2 in zip(net.parameters(), net2.parameters()):\n", "     param1.data += param2.data\n", "\n", "def multiplynet(net,a):\n", "   for param1 in net.parameters():\n", "     param1.data *=a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "@dataclass\n", "class hclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    etam1g: Tensor\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "@dataclass\n", "class BAOABhclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    xc1: <PERSON>sor\n", "    xc2: <PERSON>sor\n", "    xc3: <PERSON>sor\n", "    vc1: <PERSON>sor\n", "    vc2: <PERSON>sor\n", "    vc3: <PERSON><PERSON>\n", "\n", "\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def BAOAB_hconst(h,gam):\n", "    with torch.no_grad():\n", "        hh=copy.deepcopy(h).detach().double()\n", "        gamm=copy.deepcopy(gam).detach().double()\n", "        gh=gamm*hh\n", "        eta=(torch.exp(-gh/2))\n", "        xc1=hh/2*(1+eta)\n", "        xc2=(hh*hh/4)*(1+eta)\n", "        xc3=hh/2*torch.sqrt(-torch.expm1(-gh))\n", "        vc1=eta*(hh/2)\n", "        vc2=(hh/2)\n", "        vc3=torch.sqrt(-torch.expm1(-gh))\n", "\n", "        hc=BAOABhclass(h=hh.float(),eta=eta.float(),xc1=xc1.float(),xc2=xc2.float(),xc3=xc3.float(),vc1=vc1.float(),vc2=vc2.float(),vc3=vc3.float())\n", "        return(hc)\n", "\n", "\n", "def U(x,v,hc,xi1,xi2):\n", "\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n", "\n", "def bounce(x,v,xstar,width):\n", "    vsign=(((x-xstar+width)/(2*width)).floor()% 2)*(-2)+1\n", "    vn=v*vsign\n", "    xn=((x-xstar-width)% (4*width)-2*width).abs()-width+xstar  \n", "    return([xn, vn])\n", "\n", "def bouncenet():\n", "    for p,p_star in zip(net.parameters(),net_star.parameters()):\n", "        [p.data, p.v]=bounce(p.data, p.v, p_star.data, 6/torch.sqrt(l2regconst_extra))\n", "\n", "def svrg_grad(net, batch_it):\n", "    outputsU = net(images_list[batch_it])\n", "    loss_likelihood = loss_function(outputsU, labels_list[batch_it])  \n", "\n", "\n", "    grads_reg=[torch.zeros_like(par) for par in net.parameters()]\n", "    net_pars=list(net.parameters())\n", "    with torch.no_grad():\n", "        for it in range(len_params):\n", "            if(list_no_bias[it]):\n", "                grads_reg[it]=net_pars[it].data*l2regconst\n", "\n", "    net.zero_grad()\n", "    loss_likelihood.backward()\n", "    with torch.no_grad():\n", "        grads_likelihood=[par.grad*batch_size for par in net.parameters()]\n", "\n", "        svrg_grads=[]\n", "        for p,grad,grad_reg,p_star,grad_star,star_sum_grad in zip(list(net.parameters()),grads_likelihood,grads_reg,list(net_star.parameters()),net_star_grad_list[batch_it],net_star_full_grad):              \n", "            svrg_grads.append(grad_reg+star_sum_grad+(grad-grad_star)*no_batches+l2regconst_extra*(p.data-p_star.data))\n", "    return svrg_grads,loss_likelihood.data\n", "\n", "\n", "def UBU_step(hper2c,batch_it):   \n", "    with torch.no_grad():\n", "        for p in list(net.parameters()):\n", "            xi1=torch.randn_like(p.data,device=device)\n", "            xi2=torch.randn_like(p.data,device=device)\n", "            [p.data,p.v]=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "\n", "    svrg_grads,loss_likelihood_data=svrg_grad(net, batch_it)\n", "\n", "    with torch.no_grad():\n", "\n", "        for p,grad in zip(net.parameters(), svrg_grads):              \n", "            #Using variance reduction\n", "            p.v-=hper2c.h*grad\n", "      \n", "        for p in list(net.parameters()):\n", "            xi1=torch.randn_like(p.data,device=device)\n", "            xi2=torch.randn_like(p.data,device=device)\n", "            [p.data,p.v]=U(p.data,p.v,hper2c,xi1,xi2)\n", "\n", "    return(loss_likelihood_data)\n", "\n", "\n", "\n", "def UBU_step2(net, net2, hper4c,batch_it_list):   \n", "    with torch.no_grad():\n", "        for p,q in zip(net.parameters(),net2.parameters()):\n", "            xi1=torch.randn_like(p.data,device=device)\n", "            xi2=torch.randn_like(p.data,device=device)\n", "            [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "            [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "\n", "    svrg_grads2,_=svrg_grad(net2, batch_it_list[0])\n", "   \n", "    with torch.no_grad():\n", "        for q,gradq in zip(net2.parameters(), svrg_grads2):              \n", "            q.v-=hper4c.h*gradq\n", "\n", "    with torch.no_grad():\n", "        for p,q in zip(net.parameters(),net2.parameters()):\n", "            xi1=torch.randn_like(p.data,device=device)\n", "            xi2=torch.randn_like(p.data,device=device)\n", "            [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "            [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "\n", "    svrg_grads,loss_likelihood_data=svrg_grad(net, batch_it_list[2])\n", "\n", "    with torch.no_grad():\n", "        for p,grad in zip(net.parameters(), svrg_grads):              \n", "            p.v-=2*hper4c.h*grad\n", "\n", "    with torch.no_grad():\n", "        for p,q in zip(net.parameters(),net2.parameters()):\n", "            xi1=torch.randn_like(p.data,device=device)\n", "            xi2=torch.randn_like(p.data,device=device)\n", "            [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "            [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "\n", "    svrg_grads2,_=svrg_grad(net2, batch_it_list[1])\n", "\n", "    with torch.no_grad():\n", "        for q,grad in zip(net2.parameters(), svrg_grads2):              \n", "            q.v-=hper4c.h*grad\n", "\n", "    with torch.no_grad():\n", "        for p,q in zip(net.parameters(),net2.parameters()):\n", "            xi1=torch.randn_like(p.data,device=device)\n", "            xi2=torch.randn_like(p.data,device=device)\n", "            [p.data,p.v]=U(p.data,p.v,hper4c,xi1,xi2)\n", "            [q.data,q.v]=U(q.data,q.v,hper4c,xi1,xi2)\n", "    \n", "    return(loss_likelihood_data)\n", "\n", "def EM_step2(net, net2, h, gam, batch_it_list):   \n", "    svrg_grads2,_=svrg_grad(net2, batch_it_list[0])\n", "    svrg_grads,loss_likelihood_data=svrg_grad(net, batch_it_list[2])   \n", "    sqrt2=torch.tensor(2).sqrt().detach()\n", "\n", "    with torch.no_grad():\n", "        for p,gradp in zip(net2.parameters(), svrg_grads2):              \n", "            p.xi=torch.randn_like(p.data,device=device)\n", "            p.data+=p.v*h/2\n", "            p.v-=(h/2)*gradp+gam*(h/2)*p.v-torch.sqrt(2*gam*h/2)*p.xi\n", "\n", "    svrg_grads2,_=svrg_grad(net2, batch_it_list[1])\n", "\n", "    with torch.no_grad():\n", "        for p,gradp in zip(net2.parameters(), svrg_grads2):              \n", "            p.xi2=torch.randn_like(p.data,device=device)\n", "            p.data+=p.v*h/2\n", "            p.v-=(h/2)*gradp+gam*(h/2)*p.v-torch.sqrt(2*gam*h/2)*p.xi2\n", "\n", "        for p,q,gradp in zip(net.parameters(),net2.parameters(), svrg_grads):              \n", "            p.data+=p.v*h\n", "            p.v-=(h)*gradp+gam*(h)*p.v-torch.sqrt(2*gam*h)*(q.xi+q.xi2)/sqrt2\n", "\n", "\n", "    return(loss_likelihood_data)\n", "\n", "\n", "def BAOAB_step(net,hc,batch_it,last_grad):   \n", "    \n", "    #xn=x+xc1*v-xc2*grad+xc3*xip\n", "    with torch.no_grad():\n", "        for p,grad in zip(net.parameters(), last_grad):\n", "            p.xi=torch.randn_like(p.data,device=device)\n", "            p.data=p.data+hc.xc1*p.v-hc.xc2*grad+hc.xc3*p.xi\n", "\n", "    svrg_grads,loss_likelihood_data=svrg_grad(net, batch_it)\n", "\n", "    with torch.no_grad():\n", "        for p,grad,gradn in zip(net.parameters(), last_grad,svrg_grads):              \n", "            p.v=hc.eta*p.v-hc.vc1*grad-hc.vc2*gradn+hc.vc3*p.xi\n", "\n", "    return(loss_likelihood_data,svrg_grads)\n", "\n", "\n", "\n", "def BAOAB_step2(net, net2, hc, hper2c, batch_it_list,last_grad,last_grad2):   \n", "\n", "    with torch.no_grad():\n", "        for p,grad in zip(net2.parameters(), last_grad2):\n", "            p.xi=torch.randn_like(p.data,device=device)\n", "            p.data=p.data+hper2c.xc1*p.v-hper2c.xc2*grad+hper2c.xc3*p.xi\n", "\n", "    svrg_grads2,_=svrg_grad(net2, batch_it_list[0])\n", "\n", "    with torch.no_grad():\n", "        for p,grad,gradn in zip(net2.parameters(), last_grad2,svrg_grads2):              \n", "            p.v=hper2c.eta*p.v-hper2c.vc1*grad-hper2c.vc2*gradn+hper2c.vc3*p.xi\n", "\n", "    with torch.no_grad():\n", "        for p,grad in zip(net2.parameters(), svrg_grads2):\n", "            p.xi2=torch.randn_like(p.data,device=device)\n", "            p.data=p.data+hper2c.xc1*p.v-hper2c.xc2*grad+hper2c.xc3*p.xi2\n", "\n", "    svrg_grads2n,_=svrg_grad(net2, batch_it_list[1])\n", "\n", "    with torch.no_grad():\n", "        for p,grad,gradn in zip(net2.parameters(), svrg_grads2,svrg_grads2n):              \n", "            p.v=hper2c.eta*p.v-hper2c.vc1*grad-hper2c.vc2*gradn+hper2c.vc3*p.xi2\n", "\n", "    sqrt2=torch.tensor(2).sqrt().detach()\n", "    with torch.no_grad():\n", "        for p,q,grad in zip(net.parameters(),net2.parameters(), last_grad):\n", "            p.data=p.data+hc.xc1*p.v-hc.xc2*grad+hc.xc3*(q.xi+q.xi2)/sqrt2\n", "\n", "    svrg_grads,loss_likelihood_data=svrg_grad(net, batch_it_list[2])\n", "\n", "    with torch.no_grad():\n", "        for p,q,grad,gradn in zip(net.parameters(),net2.parameters(), last_grad,svrg_grads):              \n", "            p.v=hc.eta*p.v-hc.vc1*grad-hc.vc2*gradn+hc.vc3*(q.xi+q.xi2)/sqrt2\n", "\n", "    return(loss_likelihood_data,svrg_grads,svrg_grads2n)\n", "\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#net = Fashion_MNIST_CNN().cuda()\n", "#net2=copy.deepcopy(net)\n", "#addnet(net2,net)\n", "#multiplynet(net2,1/10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filepath=\"output_fashion_low_rank_9n.pickle\"\n", "# #filepath=\"output_fashion_low_rank_long.pickle\"\n", "# with open(filepath,\"rb\") as file:\n", "#    [labels_arr,test_labels_arr,test_prob_arr]=pickle.load(file)\n", "# labels_arr=torch.tensor(labels_arr).detach()\n", "# test_labels_arr=torch.tensor(test_labels_arr).detach()\n", "# test_prob_arr=torch.tensor(test_prob_arr).detach()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "u75Xa5VckuTH"}, "outputs": [], "source": ["\n", "\n", "#@title Output arrays\n", "num_classes=10\n", "\n", "training_size=no_batches*batch_size\n", "test_size=test_data_len\n", "\n", "\n", "l2regconst=torch.tensor(50).detach()\n", "l2regconst_extra=torch.tensor(0).detach()\n", "gam=torch.sqrt(l2regconst)\n", "#hper2c=hper2const(torch.tensor(h/2),gam)\n", "all_images=torch.cat(images_list,dim=0).detach()\n", "all_labels=torch.cat(labels_list,dim=0).detach()\n", "# net = multinomial().cuda()\n", "# net2 = multinomial().cuda()\n", "# net.train()\n", "# net2.train()\n", "\n", "def find_MAP(num_steps):\n", "  net = multinomial().cuda()\n", "\n", "  def lpost():\n", "    outputs = net(all_images)    \n", "    loss_likelihood = loss_function(outputs, all_labels)\n", "    reg=images_regulariser(net)\n", "    loss=loss_likelihood+reg\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    return(loss)\n", "\n", "  optimizer = torch.optim.LBFGS(net.parameters(), history_size=30, max_iter=20)\n", "  for epoch in range(num_steps):\n", "    optimizer.step(lpost)\n", "  \n", "  net_star=copy.deepcopy(net)\n", "  len_params=len(list(net_star.parameters()))\n", "  #Variance reduction - saving gradients at each batch at x_star\n", "  net_star_grad_list=[]\n", "  net_star_full_grad=[torch.zeros_like(par, device=device) for par in list(net_star.parameters())]\n", "  for i in range(no_batches):\n", "      images=images_list[i]\n", "      labels=labels_list[i]\n", "      outputs=net_star(images)\n", "      loss_likelihood = loss_function(outputs, labels)\n", "      reg=images_regulariser(net)\n", "      net_star.zero_grad()\n", "      loss_likelihood.backward()\n", "      grads=[par.grad*batch_size for par in list(net_star.parameters())]\n", "      net_star_grad_list.append(grads)\n", "      for g, gi in zip(net_star_full_grad,grads):\n", "        g+=gi          \n", "\n", "  len_params=len(list(net_star.parameters()))\n", "  list_no_bias=torch.zeros(len_params)\n", "  pit=0\n", "  for name, p in net_star.named_parameters():\n", "      if 'bias' not in name:\n", "          list_no_bias[pit]=1.0\n", "      pit+=1\n", "  return net_star, net_star_grad_list, net_star_full_grad, len_params, list_no_bias\n", "\n", "  \n", "\n", "def SMS_UBU2(num_epochs,h,gam):\n", "  net=copy.deepcopy(net_star)\n", "  net2=copy.deepcopy(net_star)\n", "\n", "  with torch.no_grad():\n", "    hper4c=hper2const(torch.tensor(h/2),gam)\n", "    test_labels_arr=torch.zeros(test_size).detach()\n", "    test_prob_arr=torch.zeros([test_size,num_classes,num_epochs]).detach()\n", "    test_prob_arr2=torch.zeros([test_size,num_classes,num_epochs]).detach()  \n", "  #Initialise velocities\n", "    for par,par2 in zip(net.parameters(), net2.parameters()):\n", "      par.v = torch.randn_like(par,device=device).detach()\n", "      par2.v=copy.deepcopy(par.v).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    if(epoch%2==0):\n", "      rperm=random.permutation(list(range(no_batches)))      \n", "    rperm2=random.permutation(list(range(no_batches)))\n", "    for i in range(no_batches):\n", "      it=epoch*no_batches+i\n", "      \n", "      ind=ind_create(2*it)\n", "      ind2=ind_create(2*it+1)            \n", "      indc=ind_create(it)          \n", "      batch_it_list=[rperm2[ind], rperm2[ind2], rperm[indc]]\n", "\n", "      loss_likelihood=UBU_step2(net,net2,hper4c,batch_it_list)\n", "      sum_loss=sum_loss+loss_likelihood\n", "\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0  \n", "\n", "    with torch.no_grad():\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        outputt2= net2(imagest).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "        #_, predictedt2 = torch.max(outputt2,1)\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt,dim=1)\n", "\n", "        test_prob_arr2[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt2,dim=1)\n", "    \n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  return(test_labels_arr,test_prob_arr,test_prob_arr2)\n", "  \n", "def SG_UBU2(num_epochs,h,gam):\n", "  net=copy.deepcopy(net_star)\n", "  net2=copy.deepcopy(net_star)\n", "  with torch.no_grad():\n", "    hper4c=hper2const(torch.tensor(h/2),gam)\n", "    test_labels_arr=torch.zeros(test_size)\n", "    test_prob_arr=torch.zeros([test_size,num_classes,num_epochs])\n", "    test_prob_arr2=torch.zeros([test_size,num_classes,num_epochs])  \n", "  #Initialise velocities\n", "    for par,par2 in zip(net.parameters(), net2.parameters()):\n", "      par.v = torch.randn_like(par,device=device).detach()\n", "      par2.v=copy.deepcopy(par.v).detach()\n", "\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    for i in range(no_batches):\n", "      ind=torch.randint(high=no_batches,size=(1,1))\n", "      ind2=torch.randint(high=no_batches,size=(1,1))\n", "      if(torch.rand(1)<0.5):\n", "        indc=ind\n", "      else:\n", "        indc=ind2\n", "      batch_it_list=[ind, ind2, indc]\n", "\n", "      loss_likelihood=UBU_step2(net,net2,hper4c,batch_it_list)\n", "      sum_loss=sum_loss+loss_likelihood\n", "\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0  \n", "\n", "    with torch.no_grad():\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        outputt2= net2(imagest).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "        #_, predictedt2 = torch.max(outputt2,1)\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt,dim=1)\n", "\n", "        test_prob_arr2[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt2,dim=1)\n", "    \n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  return(test_labels_arr,test_prob_arr,test_prob_arr2)\n", "\n", "def SG_EM2(num_epochs,h,gam):\n", "  net=copy.deepcopy(net_star)\n", "  net2=copy.deepcopy(net_star)\n", "  with torch.no_grad():\n", "    test_labels_arr=torch.zeros(test_size)\n", "    test_prob_arr=torch.zeros([test_size,num_classes,num_epochs])\n", "    test_prob_arr2=torch.zeros([test_size,num_classes,num_epochs])  \n", "  #Initialise velocities\n", "    for par,par2 in zip(net.parameters(), net2.parameters()):\n", "      par.v = torch.randn_like(par,device=device).detach()\n", "      par2.v=copy.deepcopy(par.v).detach()\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    for i in range(no_batches):\n", "      ind=torch.randint(high=no_batches,size=(1,1))\n", "      ind2=torch.randint(high=no_batches,size=(1,1))\n", "      if(torch.rand(1)<0.5):\n", "        indc=ind\n", "      else:\n", "        indc=ind2\n", "      batch_it_list=[ind, ind2, indc]\n", "\n", "      loss_likelihood=EM_step2(net,net2,h,gam,batch_it_list)\n", "      sum_loss=sum_loss+loss_likelihood\n", "\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0  \n", "\n", "    with torch.no_grad():\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        outputt2= net2(imagest).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "        #_, predictedt2 = torch.max(outputt2,1)\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt,dim=1)\n", "\n", "        test_prob_arr2[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt2,dim=1)\n", "    \n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  return(test_labels_arr,test_prob_arr,test_prob_arr2)\n", "\n", "def SG_BAOAB(num_epochs,h,gam):\n", "  net=copy.deepcopy(net_star)\n", "  \n", "  with torch.nograd():\n", "    hc=BAOAB_hconst(h,gam)\n", "    test_labels_arr=torch.zeros(test_size)\n", "    test_prob_arr=torch.zeros([test_size,num_classes,num_epochs])\n", "    #Initialise velocities\n", "    for par in list(net.parameters()):\n", "      par.v = torch.randn_like(par,device=device)      \n", "\n", "  \n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    for i in range(no_batches):\n", "      ind=torch.randint(high=no_batches,size=(1,1))\n", "      if(epoch==0 and i==0):\n", "        last_grad,_=svrg_grad(net,ind)\n", "      loss_likelihood,last_grad=BAOAB_step(net,hc,ind,last_grad)\n", "\n", "      sum_loss=sum_loss+loss_likelihood\n", "\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0  \n", "\n", "    with torch.no_grad():\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "        #_, predictedt2 = torch.max(outputt2,1)\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt,dim=1)\n", "    \n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  return(test_labels_arr,test_prob_arr)\n", "\n", "\n", "\n", "def SG_BAOAB2(num_epochs,h,gam):\n", "  net=copy.deepcopy(net_star)\n", "  net2=copy.deepcopy(net_star)  \n", "  with torch.no_grad():\n", "    hc=BAOAB_hconst(h,gam)\n", "    hper2c=BAOAB_hconst(h/2,gam)      \n", "    test_labels_arr=torch.zeros(test_size)\n", "    test_prob_arr=torch.zeros([test_size,num_classes,num_epochs])\n", "    test_prob_arr2=torch.zeros([test_size,num_classes,num_epochs])  \n", "  #Initialise velocities\n", "    for par,par2 in zip(net.parameters(), net2.parameters()):\n", "      par.v = torch.randn_like(par,device=device).detach()\n", "      par2.v=copy.deepcopy(par.v).detach()\n", "  \n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    for i in range(no_batches):\n", "\n", "      ind=torch.randint(high=no_batches,size=(1,1))\n", "      ind2=torch.randint(high=no_batches,size=(1,1))\n", "      if(torch.rand(1)<0.5):\n", "        indc=ind\n", "      else:\n", "        indc=ind2\n", "      batch_it_list=[ind, ind2, indc]\n", "\n", "      if(epoch==0 and i==0):\n", "        last_grad,_=svrg_grad(net,ind)\n", "        last_grad2,_=svrg_grad(net2,indc)\n", "\n", "      loss_likelihood,last_grad,last_grad2=BAOAB_step2(net,net2,hc,hper2c,batch_it_list,last_grad,last_grad2)\n", "\n", "      sum_loss=sum_loss+loss_likelihood\n", "\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0  \n", "\n", "    with torch.no_grad():\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        outputt2 = net2(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "        #_, predictedt2 = torch.max(outputt2,1)\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt,dim=1)\n", "        test_prob_arr2[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt2,dim=1)\n", "    \n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  return(test_labels_arr,test_prob_arr,test_prob_arr2)\n", "\n", "\n", "def SMS_BAOAB2(num_epochs,h,gam):\n", "  net=copy.deepcopy(net_star)\n", "  net2=copy.deepcopy(net_star)  \n", "  with torch.no_grad():\n", "    hc=BAOAB_hconst(h,gam)\n", "    hper2c=BAOAB_hconst(h/2,gam)      \n", "    test_labels_arr=torch.zeros(test_size)\n", "    test_prob_arr=torch.zeros([test_size,num_classes,num_epochs])\n", "    test_prob_arr2=torch.zeros([test_size,num_classes,num_epochs])  \n", "  #Initialise velocities\n", "    for par,par2 in zip(net.parameters(), net2.parameters()):\n", "      par.v = torch.randn_like(par,device=device).detach()\n", "      par2.v=copy.deepcopy(par.v).detach()\n", "  \n", "  for epoch in range(num_epochs):\n", "    sum_loss=0\n", "    if(epoch%2==0):\n", "      rperm=random.permutation(list(range(no_batches)))      \n", "    rperm2=random.permutation(list(range(no_batches)))\n", "    for i in range(no_batches):\n", "      b=i        \n", "      it=epoch*batch_size+b\n", "      ind=ind_create(2*it)\n", "      ind2=ind_create(2*it+1)            \n", "      indc=ind_create(it)          \n", "      batch_it_list=[rperm2[ind], rperm2[ind2], rperm[indc]]\n", "\n", "      if(epoch==0 and i==0):\n", "        last_grad,_=svrg_grad(net,ind)\n", "        last_grad2,_=svrg_grad(net2,indc)\n", "\n", "      loss_likelihood,last_grad,last_grad2=BAOAB_step2(net,net2,hc,hper2c,batch_it_list,last_grad,last_grad2)\n", "\n", "      sum_loss=sum_loss+loss_likelihood\n", "\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0  \n", "\n", "    with torch.no_grad():\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        outputt2 = net2(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "        #_, predictedt2 = torch.max(outputt2,1)\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt,dim=1)\n", "        test_prob_arr2[(testit*batch_size):(testit*batch_size+actual_batch_size),0:num_classes,epoch]=torch.softmax(outputt2,dim=1)\n", "    \n", "    #net.train()       \n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  return(test_labels_arr,test_prob_arr,test_prob_arr2)\n", "\n", "net_star, net_star_grad_list, net_star_full_grad, len_params, list_no_bias=find_MAP(40)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=0.5\n", "num_epochs=int(400*rat)\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h-1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=1\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h0.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=2\n", "num_epochs=400*rat\n", "h=torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=4\n", "num_epochs=400*rat\n", "h=torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h2.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=8\n", "num_epochs=400*rat\n", "h=torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h3.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=16\n", "num_epochs=400*rat\n", "h=torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h4.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=32\n", "num_epochs=400*rat\n", "h=torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_BAOAB_h5.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=0.5\n", "num_epochs=int(400*rat)\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h-1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=1\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h0.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=2\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=4\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h2.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=8\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h3.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=16\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h4.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=32\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_BAOAB2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_BAOAB_h5.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=0.5\n", "num_epochs=int(400*rat)\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h-1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=1\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h0.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=2\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=4\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h2.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=8\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h3.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=16\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h4.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=32\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_UBU_h5.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=0.5\n", "num_epochs=int(400*rat)\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h-1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=1\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h0.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=2\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=4\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h2.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=8\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h3.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=16\n", "num_epochs=800*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h4l.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,10*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,10*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=16\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h4.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=32\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SMS_UBU2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SMS_UBU_h5.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=0.5\n", "num_epochs=int(400*rat)\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h-1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,int(40*rat):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=1\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h0.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=2\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h1.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=4\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h2.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=8\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h3.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=16\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h4.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=32\n", "num_epochs=400*rat\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "filepath=\"output_fashion_multinomial_SG_EM_h5.pickle\"\n", "with open(filepath,\"wb\") as file:\n", "  pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rat=64\n", "num_epochs=800\n", "h= torch.tensor(1e-3)/rat\n", "test_labels_arr,test_prob_arr,test_prob_arr2=SG_EM2(num_epochs,h,gam)\n", "# filepath=\"output_fashion_multinomial_SG_EM_h5.pickle\"\n", "# with open(filepath,\"wb\") as file:\n", "#   pickle.dump([test_labels_arr,test_prob_arr, test_prob_arr2],file)\n", "\n", "test_prob=torch.Tensor(test_prob_arr[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "test_prob2=torch.Tensor(test_prob_arr2[:,:,40*rat:num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "test_prob_correct_label=torch.Tensor([test_prob[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "test_prob_correct_label2=torch.Tensor([test_prob2[it, test_labels_arr[it].int()] for it in range(test_size)])\n", "print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rps_single(probs, true_label,num_classes):\n", "    outcome=torch.zeros(num_classes)\n", "    outcome[true_label.int()]=1.0\n", "    cum_probs = torch.cumsum(probs,0)\n", "    cum_outcomes = torch.cumsum(outcome,0)\n", "    \n", "    sum_rps = 0\n", "    for i in range(len(outcome)):         \n", "        sum_rps+= (cum_probs[i] - cum_outcomes[i])**2\n", "    \n", "    return sum_rps/(num_classes-1)\n", "\n", "def rps_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    rps_vec=torch.zeros(test_data_len)\n", "    for it in range(test_data_len):\n", "        rps_vec[it]=rps_single(test_probs[it,:].reshape(num_classes),true_labels[it],num_classes)\n", "    return rps_vec\n", "\n", "def nll_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    res=0\n", "    for it in range(test_data_len):\n", "        res-=torch.max(torch.tensor([torch.log(test_probs[it,true_labels[it].int()]),-100]))\n", "    return res/test_data_len\n", "\n", "\n", "def adaptive_calibration_error(test_probs,true_labels, test_data_len, num_classes,num_bins=20):\n", "    o=torch.tensor(0.0).detach()\n", "    for k in range(num_classes):\n", "        ind=torch.argsort(test_probs[:,k],stable=True)        \n", "        testprobsk=test_probs[:,k]\n", "        sorted_probs=testprobsk[ind]\n", "        sorted_true_labels=true_labels[ind]\n", "\n", "        true_label_is_k = (sorted_true_labels==k).clone().detach().float()\n", "        bins=(torch.tensor(range(test_data_len))/torch.tensor(test_data_len/num_bins)).floor()\n", "\n", "        for b in range(num_bins):\n", "            mask = (bins == b)\n", "            if torch.any(mask):\n", "                o += (true_label_is_k[mask] - sorted_probs[mask]).mean().abs()\n", "\n", "    return o / (num_bins*num_classes)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bias_arr_full=torch.zeros([5,6,10000]).detach()\n", "bias_arr_full_sd4=torch.zeros([5,6,10000,4]).detach()\n", "bias_arr=torch.zeros([5,6]).detach()\n", "bias_arr_sd4=torch.zeros([5,6,4]).detach()\n", "bias_arr_sd=torch.zeros([5,6]).detach()\n", "training_size=no_batches*batch_size\n", "test_size=test_data_len\n", "num_classes=10"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["str_list=[\"SG_BAOAB\",\"SMS_BAOAB\",\"SG_UBU\",\"SMS_UBU\", \"SG_EM\"]\n", "\n", "for mit in range(5):\n", "    for it in range(-1,5):\n", "        str=str_list[mit]\n", "        filepath=\"output_fashion_multinomial_%s_h%d.pickle\"%(str,it)\n", "        num_epochs=int(400*(2.0**it))\n", "\n", "        with open(filepath,\"rb\") as file:\n", "            test_labels_arr,test_prob_arr, test_prob_arr2=pickle.load(file)           \n", "\n", "            test_prob=torch.Tensor(test_prob_arr[:,:,(num_epochs//5):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "            test_prob2=torch.Tensor(test_prob_arr2[:,:,(num_epochs//5):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "            test_prob_correct_label=torch.Tensor([test_prob[t, test_labels_arr[t].int()] for t in range(test_size)])\n", "            test_prob_correct_label2=torch.Tensor([test_prob2[t, test_labels_arr[t].int()] for t in range(test_size)])\n", "\n", "            #bias_arr[mit,it+1]=(test_prob_correct_label-test_prob_correct_label2).abs().mean()\n", "            bias_arr_full[mit,it+1,:]=test_prob_correct_label-test_prob_correct_label2\n", "            for bit in range(4):\n", "                test_prob=torch.Tensor(test_prob_arr[:,:,(num_epochs//5)*bit:(num_epochs//5)*(bit+1)]).mean(-1).reshape(test_size,num_classes)\n", "                test_prob2=torch.Tensor(test_prob_arr2[:,:,(num_epochs//5)*bit:(num_epochs//5)*(bit+1)]).mean(-1).reshape(test_size,num_classes)\n", "\n", "                test_prob_correct_label=torch.Tensor([test_prob[t, test_labels_arr[t].int()] for t in range(test_size)])\n", "                test_prob_correct_label2=torch.Tensor([test_prob2[t, test_labels_arr[t].int()] for t in range(test_size)])\n", "\n", "                bias_arr_full_sd4[mit,it+1,:,bit]=test_prob_correct_label-test_prob_correct_label2\n", "\n", "            #bias_arr[mit,it+1]=(test_prob-test_prob2).abs().sum(dim=1).mean()\n", "            #bias_arr[mit,it+1]=(torch.log(test_prob_correct_label)-torch.log(test_prob_correct_label2)).nanmean().abs()\n", "            \n", "for mit in range(5):\n", "    for it in range(-1,5):\n", "        bias_arr[mit,it+1]=(bias_arr_full[mit,(it+1):6,:].sum(dim=0)).abs().mean()\n", "        for bit in range(4):\n", "            bias_arr_sd4[mit,it+1,bit]=(bias_arr_full_sd4[mit,(it+1):6,:,bit].sum(dim=0)).abs().mean()\n", "        bias_arr_sd[mit,it+1]=torch.std(torch.log2(bias_arr_sd4[mit,it+1,:]))/2\n", "# print(\"Average bias in probability of true category\", (test_prob_correct_label-test_prob_correct_label2).abs().mean())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for mit in range(5):\n", "    for it in range(-1,5):\n", "        bias_arr[mit,it+1]=(bias_arr_full[mit,(it+1):6,:].sum(dim=0)).abs().mean()\n", "        for bit in range(4):\n", "            bias_arr_sd4[mit,it+1,bit]=(bias_arr_full_sd4[mit,(it+1):6,:,bit].sum(dim=0)).abs().mean()\n", "        bias_arr_sd[mit,it+1]=torch.std(torch.log2(bias_arr_sd4[mit,it+1,:]))/2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.log2(bias_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bias_arr_sd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scipy.io import savemat\n", "filepath=\"results_fashion_multinomial_bias.mat\"\n", "mdic={\"bias_arr\":bias_arr.cpu().numpy(),\"bias_arr_sd\":bias_arr_sd.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["str_list=[\"SG_BAOAB\",\"SMS_BAOAB\",\"SG_UBU\",\"SMS_UBU\", \"SG_EM\"]\n", "rps_arr=torch.zeros(5,6)\n", "acc_arr=torch.zeros(5,6)\n", "ace_arr=torch.zeros(5,6)\n", "nll_arr=torch.zeros(5,6)\n", "rps_arr4=torch.zeros(5,6,4)\n", "acc_arr4=torch.zeros(5,6,4)\n", "ace_arr4=torch.zeros(5,6,4)\n", "nll_arr4=torch.zeros(5,6,4)\n", "rps_arr_sd=torch.zeros(5,6)\n", "acc_arr_sd=torch.zeros(5,6)\n", "ace_arr_sd=torch.zeros(5,6)\n", "nll_arr_sd=torch.zeros(5,6)\n", "num_classes=10\n", "test_prob4=torch.zeros(10000,10,4)\n", "for mit in range(5):\n", "    for it in range(-1,5):\n", "        str=str_list[mit]\n", "        filepath=\"output_fashion_multinomial_%s_h%d.pickle\"%(str,it)\n", "\n", "\n", "        num_epochs=int(400*(2.0**it))\n", "\n", "        with open(filepath,\"rb\") as file:\n", "            test_labels_arr,test_prob_arr, test_prob_arr2=pickle.load(file)           \n", "\n", "            test_prob=torch.Tensor(test_prob_arr[:,:,(num_epochs//5):num_epochs]).mean(-1).reshape(test_size,num_classes)\n", "\n", "            for bit in range(4):\n", "                test_prob4[:,:,bit]=torch.Tensor(test_prob_arr[:,:,(num_epochs//5)*bit:(num_epochs//5)*(bit+1)]).mean(-1).reshape(test_size,num_classes)\n", "              \n", "        rps_arr[mit,it+1]=(rps_calc(test_prob, test_labels_arr, test_data_len,num_classes).mean())\n", "        nll_arr[mit,it+1]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        ace_arr[mit,it+1]=adaptive_calibration_error(test_prob, test_labels_arr, test_data_len,num_classes).mean()\n", "        _, predictedt = torch.max(test_prob,1)\n", "        acc_arr[mit,it+1]=(predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "        for bit in range(4):\n", "            rps_arr4[mit,it+1,bit]=rps_calc(test_prob4[:,:,bit], test_labels_arr,test_data_len,num_classes).mean()\n", "            nll_arr4[mit,it+1,bit]=nll_calc(test_prob4[:,:,bit], test_labels_arr,test_data_len,num_classes)\n", "            ace_arr4[mit,it+1,bit]=adaptive_calibration_error(test_prob4[:,:,bit], test_labels_arr, test_data_len,num_classes).mean()\n", "            _, predictedt = torch.max(test_prob4[:,:,bit],1)\n", "            acc_arr4[mit,it+1,bit]=(predictedt==test_labels_arr.reshape(1,test_size)).sum()/test_size\n", "        rps_arr_sd[mit,it+1]=torch.std(rps_arr4[mit,it+1,:])/2\n", "        nll_arr_sd[mit,it+1]=torch.std(nll_arr4[mit,it+1,:])/2\n", "        ace_arr_sd[mit,it+1]=torch.std(ace_arr4[mit,it+1,:])/2\n", "        acc_arr_sd[mit,it+1]=torch.std(acc_arr4[mit,it+1,:])/2\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_multinomial.mat\"\n", "mdic={\"rps_arr\":rps_arr.cpu().numpy(),\"rps_arr_sd\":rps_arr_sd.cpu().numpy(),\\\n", "      \"ace_arr\":ace_arr.cpu().numpy(),\"ace_arr_sd\":ace_arr_sd.cpu().numpy(),\\\n", "      \"acc_arr\":acc_arr.cpu().numpy(),\"acc_arr_sd\":acc_arr_sd.cpu().numpy(),\\\n", "      \"nll_arr\":nll_arr.cpu().numpy(),\"nll_arr_sd\":nll_arr_sd.cpu().numpy()}\n", "savemat(filepath,mdic)"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Pytorch MNIST.ipynb", "provenance": []}, "kernelspec": {"display_name": "torchenv39", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 0}