# Makefile for ZBAOABZ C++ implementation

# Compiler
CXX = g++

# Compiler flags
CXXFLAGS = -std=c++17 -O3 -march=native -Wall -Wextra

# Target executable
TARGET = zbaoabz

# Source files
SOURCES = zbaoabz_cpp.cpp

# Default target
all: $(TARGET)

# Compile the executable
$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCES)

# Run the simulation
run: $(TARGET)
	./$(TARGET)

# Clean build files
clean:
	rm -f $(TARGET) zbaoabz_results.txt plot_results.py zbaoabz_results.png

# Install dependencies (for Ubuntu/Debian)
install-deps:
	sudo apt-get update
	sudo apt-get install -y g++ make python3 python3-pip
	pip3 install numpy matplotlib

# Help
help:
	@echo "Available targets:"
	@echo "  all        - Build the executable"
	@echo "  run        - Build and run the simulation"
	@echo "  clean      - Remove build files and results"
	@echo "  install-deps - Install required dependencies"
	@echo "  help       - Show this help message"

.PHONY: all run clean install-deps help 