#include "mex.h"
#include "zbaoabz_funnel9d_core.hpp"

void mexFunction(int nlhs, mxArray* plhs[], int nrhs, const mxArray* prhs[]){
    // Inputs:
    // 0 nmax, 1 nmeas, 2 dtau, 3 gamma, 4 T, 5 grad_type
    // 6 alpha_dt, 7 scale_g, 8 m_dt, 9 M_dt, 10 r_dt, 11 z0_dt
    // 12 n_sat, 13 sigma_x2, 14 q0 (vector D), 15 v0 (vector D)
    // 16 burn_in_noise, 17 initial_sigma, 18 sigma_min, 19 sigma_max
    // 20 alpha_sigma, 21 threshold_sigma, 22 steepness_sigma
    if (nrhs != 23) mexErrMsgIdAndTxt("funnel9d_mex:nrhs", "23 inputs required.");
    if (nlhs != 9)  mexErrMsgIdAndTxt("funnel9d_mex:nlhs", "9 outputs required.");

    int idx=0;
    int    nmax   = (int)mxGetScalar(prhs[idx++]);
    int    nmeas  = (int)mxGetScalar(prhs[idx++]);
    double dtau   = mxGetScalar(prhs[idx++]);
    double gamma  = mxGetScalar(prhs[idx++]);
    double T      = mxGetScalar(prhs[idx++]);
    auto   gt     = (GradientType)(int)mxGetScalar(prhs[idx++]);

    double alpha_dt  = mxGetScalar(prhs[idx++]);
    double scale_g   = mxGetScalar(prhs[idx++]);
    double m_dt      = mxGetScalar(prhs[idx++]);
    double M_dt      = mxGetScalar(prhs[idx++]);
    double r_dt      = mxGetScalar(prhs[idx++]);
    double z0_dt     = mxGetScalar(prhs[idx++]);

    int    n_sat     = (int)mxGetScalar(prhs[idx++]); // 8
    double sigma_x2  = mxGetScalar(prhs[idx++]);
    const mxArray* q0_in = prhs[idx++];
    const mxArray* v0_in = prhs[idx++];
    int D = (int)mxGetNumberOfElements(q0_in);
    if (D != (int)mxGetNumberOfElements(v0_in)) mexErrMsgIdAndTxt("funnel9d_mex:dim","q0 and v0 size mismatch.");
    if (D != n_sat+1) mexErrMsgIdAndTxt("funnel9d_mex:dim","D must equal n_sat+1.");

    std::vector<double> q0(D), v0(D);
    double* q0p = mxGetPr(q0_in);
    double* v0p = mxGetPr(v0_in);
    for(int i=0;i<D;++i){ q0[i]=q0p[i]; v0[i]=v0p[i]; }

    int    burn_in_noise   = (int)mxGetScalar(prhs[idx++]);
    double initial_sigma   = mxGetScalar(prhs[idx++]);
    double sigma_min       = mxGetScalar(prhs[idx++]);
    double sigma_max       = mxGetScalar(prhs[idx++]);
    double alpha_sigma     = mxGetScalar(prhs[idx++]);
    double threshold_sigma = mxGetScalar(prhs[idx++]);
    double steepness_sigma = mxGetScalar(prhs[idx++]);

    auto R = runZBAOABZ_funnel9d(
        nmax, nmeas, dtau, gamma, T, gt,
        alpha_dt, scale_g, m_dt, M_dt, r_dt, z0_dt,
        n_sat, sigma_x2, q0, v0,
        burn_in_noise, initial_sigma, sigma_min, sigma_max, alpha_sigma, threshold_sigma, steepness_sigma
    );

    // outputs: θ, x1, dt, psi, Umean, Tkin, Tconf, sigmas, sigma_zetas
    mwSize Ntraj = R.theta.size();
    mwSize Nmeas = R.Umean.size();
    plhs[0] = mxCreateDoubleMatrix(Ntraj,1,mxREAL);
    plhs[1] = mxCreateDoubleMatrix(Ntraj,1,mxREAL);
    plhs[2] = mxCreateDoubleMatrix(Ntraj,1,mxREAL);
    plhs[3] = mxCreateDoubleMatrix(Ntraj,1,mxREAL);
    plhs[4] = mxCreateDoubleMatrix(Nmeas,1,mxREAL);
    plhs[5] = mxCreateDoubleMatrix(Nmeas,1,mxREAL);
    plhs[6] = mxCreateDoubleMatrix(Nmeas,1,mxREAL);
    plhs[7] = mxCreateDoubleMatrix(Nmeas,1,mxREAL);
    plhs[8] = mxCreateDoubleMatrix(Nmeas,1,mxREAL);

    auto copy_vec = [](const std::vector<double>& v, mxArray* out){
        double* p = mxGetPr(out);
        for(mwSize i=0;i<v.size();++i) p[i]=v[i];
    };
    copy_vec(R.theta,       plhs[0]);
    copy_vec(R.x1,          plhs[1]);
    copy_vec(R.dt,          plhs[2]);
    copy_vec(R.psi,         plhs[3]);
    copy_vec(R.Umean,       plhs[4]);
    copy_vec(R.Tkin,        plhs[5]);
    copy_vec(R.Tconf,       plhs[6]);
    copy_vec(R.sigmas,      plhs[7]);
    copy_vec(R.sigma_zetas, plhs[8]);
}
