% figure; hold on;
% 
% % 统一 bin（非常重要：所有直方图对齐）
% edges = 0:0.005:0.28;                 % 比原先 0.01 更细
% 
% % 高对比配色（Tableau/ColorBrewer 风格）
% cols = [ 0.121 0.466 0.705;   % 蓝  (dt)
%          1.000 0.498 0.054;   % 橙  (dt_per)
%          0.172 0.627 0.172;   % 绿  (dt_max)
%          0.839 0.152 0.156];  % 红  (dt_batch)
% 
% % 画直方图（建议用 pdf，更易比较）
% h1 = histogram(dt,       'BinEdges',edges,'Normalization','pdf',...
%      'FaceColor',cols(1,:),'FaceAlpha',0.35,'EdgeColor','none', ...
%      'DisplayName','ZBAOABZ without noise');
% h2 = histogram(dt_per,   'BinEdges',edges,'Normalization','pdf',...
%      'FaceColor',cols(2,:),'FaceAlpha',0.35,'EdgeColor','none', ...
%      'DisplayName','ZBAOABZ with mean $\sigma$');
% h3 = histogram(dt_max,   'BinEdges',edges,'Normalization','pdf',...
%      'FaceColor',cols(3,:),'FaceAlpha',0.35,'EdgeColor','none', ...
%      'DisplayName','ZBAOABZ with max $\sigma$');
% h4 = histogram(dt_batch, 'BinEdges',edges,'Normalization','pdf',...
%      'FaceColor',cols(4,:),'FaceAlpha',0.35,'EdgeColor','none', ...
%      'DisplayName','ZBAOA$\mathrm{Z}^{\sigma}$BZ');
% 
% % 与直方图同色的均值虚线（不进 legend，避免拥挤）
% m = [mean(dt), mean(dt_per), mean(dt_max), mean(dt_batch)];
% for i = 1:4
%     xline(m(i),'--','Color',cols(i,:),'LineWidth',1.5,'HandleVisibility','off');
% end
% 
% % 文本标注（可选：更清爽就去掉）
% yl = ylim;
% for i = 1:4
%     text(m(i), yl(2)*(0.92-0.04*(i-1)), ...
%      sprintf('$\\mu=%.4f$', m(i)), ...
%      'Interpreter','latex','HorizontalAlignment','center', ...
%      'Color',cols(i,:), 'FontSize',8, ...          % 更小
%      'BackgroundColor',[1 1 1 0.6],'EdgeColor','none'); % 去掉白框
% end
% 
% % 轴、标题、图例
% legend('Interpreter','latex','FontSize',8,'Location','northwest');
% xlabel('$\Delta t$','Interpreter','latex'); 
% ylabel('pdf','Interpreter','latex');
% title('$\Delta t$ Distributions With and Without Adaptive Batch Size', ...
%       'Interpreter','latex');
% xlim([edges(1) edges(end)]); grid on; box on;
% 
% % 更干净的外观
% set(gcf,'Color','w');
% set(gca,'Layer','top','LineWidth',1);   % 坐标轴线细一些
% saveas(gcf,'dt_histograms_colored.png');


% figure; hold on;
% edges = 0:0.005:0.30;
% cols = [0.121 0.466 0.705;
%         1.000 0.498 0.054;
%         0.172 0.627 0.172;
%         0.839 0.152 0.156];
% 
% % 使用 'DisplayStyle','stairs'（只画轮廓）
% histogram(dt,      'BinEdges',edges,'Normalization','pdf','DisplayStyle','stairs',...
%     'EdgeColor',cols(1,:),'LineWidth',1.5,'DisplayName','ZBAOABZ without noise');
% histogram(dt_per,  'BinEdges',edges,'Normalization','pdf','DisplayStyle','stairs',...
%     'EdgeColor',cols(2,:),'LineWidth',1.5,'DisplayName','ZBAOABZ with mean $\sigma$');
% histogram(dt_max,  'BinEdges',edges,'Normalization','pdf','DisplayStyle','stairs',...
%     'EdgeColor',cols(3,:),'LineWidth',1.5,'DisplayName','ZBAOABZ with max $\sigma$');
% histogram(dt_batch,'BinEdges',edges,'Normalization','pdf','DisplayStyle','stairs',...
%     'EdgeColor',cols(4,:),'LineWidth',1.5,'DisplayName','ZBAOA$\mathrm{Z}^{\sigma}$BZ');
% 
% % 均值线（同色）
% m = [mean(dt), mean(dt_per), mean(dt_max), mean(dt_batch)];
% for i = 1:4
%     xline(m(i),'--','Color',cols(i,:),'LineWidth',1.2,'HandleVisibility','off');
% end
% 
% legend('Interpreter','latex','FontSize',8,'Location','northwest');
% xlabel('$\Delta t$','Interpreter','latex'); 
% ylabel('pdf','Interpreter','latex');
% title('$\Delta t$ Distributions With and Without Adaptive Batch Size', ...
%       'Interpreter','latex');
% xlim([edges(1) edges(end)]); grid on; box on; set(gcf,'Color','w');
% saveas(gcf,'dt_histograms.png');

figure; clf; hold on;

% 统一 bin（所有分布对齐很关键）
edges = 0:0.0025:0.30;    % 可调：越小柱越细

% 数据与标签
data  = {dt, dt_per, dt_max, dt_batch};
names = {'ZBAOABZ without noise', ...
         'ZBAOABZ with mean $\sigma$', ...
         'ZBAOABZ with max $\sigma$', ...
         'ZBAOAZ$^{\sigma}$BZ'};

% 高可读配色（Tableau 风格）
cols = [ 0.121 0.466 0.705;   % 蓝
         1.000 0.498 0.054;   % 橙
         0.172 0.627 0.172;   % 绿
         0.839 0.152 0.156];  % 红

% 线型备用（黑白打印也能区分）
styles = {'-','--',':','-.'};

% 先画“淡填充”，不进 legend
for i = 1:numel(data)
    histogram(data{i}, 'BinEdges', edges, 'Normalization','pdf', ...
        'FaceColor', cols(i,:), 'FaceAlpha', 0.18, ...
        'EdgeColor', 'none', 'HandleVisibility','off');
end

% 再画“描边轮廓”，进入 legend
for i = 1:numel(data)
    histogram(data{i}, 'BinEdges', edges, 'Normalization','pdf', ...
        'DisplayStyle','stairs', 'EdgeColor', cols(i,:), ...
        'LineWidth', 1.6, 'LineStyle', styles{i}, ...
        'DisplayName', names{i});
end

% 同色均值虚线（不进 legend）
for i = 1:numel(data)
    xline(mean(data{i}), ':', 'Color', cols(i,:), ...
        'LineWidth', 1.3, 'HandleVisibility','off');
end

% 轴/标题/版式（论文友好）
xlabel('$\Delta t$', 'Interpreter','latex');
ylabel('pdf', 'Interpreter','latex');
title('$\Delta t$ Distributions With and Without Adaptive Batch Size', ...
      'Interpreter','latex');
xlim([edges(1) edges(end)]);
grid on; box on; set(gcf,'Color','w');
set(gca,'Layer','top','LineWidth',1, 'FontSize',9);   % 刻度字号
legend('Interpreter','latex','FontSize',9,'Location','northwest');
saveas(gcf,'dt_histograms_.png');