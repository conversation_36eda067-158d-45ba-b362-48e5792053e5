% dt vs. (x, y)
% === 参数 ===
xlim_ = [-10 10];  ylim_ = [-10 10];
nx = 800; ny = 800;

% 保证列向量；修复/重建 idx
x = x(:); y = y(:); dt_max = dt_max(:);
if ~exist('idx','var') || numel(idx) ~= numel(x)
    warning('idx 尺寸不匹配，将使用全部点。');
    idx = true(size(x));
else
    idx = logical(idx(:));
end
idx = idx & isfinite(x) & isfinite(y) & isfinite(dt_max);

% 只取画布范围内的数据
in = idx & (x>=xlim_(1) & x<=xlim_(2) & y>=ylim_(1) & y<=ylim_(2));
x1 = x(in); y1 = y(in); v = dt_max(in);

% 分箱
xe = linspace(xlim_(1), xlim_(2), nx+1);
ye = linspace(ylim_(1), ylim_(2), ny+1);
[ix,~] = discretize(x1, xe);
[iy,~] = discretize(y1, ye);
ok = ~isnan(ix) & ~isnan(iy);
ix = ix(ok); iy = iy(ok); v = v(ok);
lin = sub2ind([ny, nx], iy, ix);
Z = accumarray(lin, v, [ny*nx 1], @max, NaN);   % 或 @mean/@median
Z = reshape(Z, ny, nx);

% ===== 在 Z 之后、imagesc 之前：分段+gamma 拉伸到 [0,1] =====
vmax = max(Z(:),[],'omitnan');    % 真实最大值，不裁剪
edges  = [0, 0.02, 0.04, 0.08, 0.16, vmax];   % 分段边界

% 把更多 colormap 比例给 [0.04,0.08] 和 [0.08,0.16]
portion = [0.12, 0.18, 0.30, 0.25, 0.15];     % 和为 1
%                 ^^^^^  ^^^^^
%                 0.04–0.08    0.08–0.16  两段占比变大

% 对这两段做轻度 gamma 展开（<1 拉开小值）
gamma   = [1.00, 1.00, 0.85, 0.85, 1.00];
% 计算每段的起点（累计占比）
startpos = [0, cumsum(portion(1:end-1))];

% 映射
Zmap = nan(size(Z));
for k = 1:numel(portion)
    a = edges(k); b = edges(k+1);
    mask = ~isnan(Z) & Z>=a & Z<=b;
    if k < numel(portion)         % 前几段闭开区间 [a,b)
        mask = ~isnan(Z) & Z>=a & Z<b;
    end
    if any(mask(:)) && b>a
        t = (Z(mask)-a)/(b-a);         % 归一到 [0,1]
        t = t.^gamma(k);               % gamma 拉伸
        Zmap(mask) = startpos(k) + portion(k)*t;
    end
end

% ===== 用 Zmap 画图 =====
figure;
h = imagesc([xlim_(1) xlim_(2)], [ylim_(1) ylim_(2)], Zmap);
set(gca,'YDir','normal');
axis equal; xlim(xlim_); ylim(ylim_);
xlabel('x'); ylabel('y');
title('\Delta t as a function of (x,y)');
colormap(flipud(turbo));

set(gca,'Color',[1 1 1]);
set(h,'AlphaData', ~isnan(Zmap));
clim([0 1]);                           % 映射空间

% ===== colorbar：等距显示你要的刻度 + 顶端留一格 =====
tick_vals = [0 0.02 0.04 0.08 0.16];   % 想显示的数值
tick_pos  = zeros(size(tick_vals));
for i = 1:numel(tick_vals)
    % tick_vals(i) 位于第 k 段
    k = find(edges(1:end-1) <= tick_vals(i) & tick_vals(i) <= edges(2:end), 1, 'last');
    a = edges(k); b = edges(k+1);
    t = (tick_vals(i)-a)/(b-a);
    t = t.^gamma(k);
    tick_pos(i) = startpos(k) + portion(k)*t;
end
cbar = colorbar;
cbar.Ticks = [tick_pos, 1];            % 顶部多放一个刻度
labels = arrayfun(@(x)sprintf('%.3f',x), tick_vals, 'UniformOutput', false);
labels{end+1} = '$\Delta t$'; set(cbar,'TickLabelInterpreter','latex');                     % 顶端不标数值（仍有颜色）
cbar.TickLabels = labels;

saveas(gcf,'dt_vs_xy.png');


% figure;
% % 原始数据，不裁剪
% scatter(x(idx), y(idx), 2, dt(idx), 'filled');
% axis equal
% xlabel('x'); ylabel('y');
% xlim([-10,10]); ylim([-10,10])
% title('\Delta t as a function of (x,y)');
% colormap(flipud(turbo))
% 
% % 用全范围，这样 0.16 以上仍然有不同颜色
% clim([0, max(dt(idx))]);
% 
% % 只想显示到 0.16，但让色条顶部还留一段没有标签
% tick_values_disp = [0, 0.02, 0.04, 0.08, 0.16];   % 想展示的刻度文本
% N = numel(tick_values_disp);
% 
% cbar = colorbar;
% 
% % 等距放置 N+1 个刻度：前 N 个对应你要显示的标签，最顶上的第 N+1 个留空
% cbar.Ticks = linspace(0, max(dt(idx)), N+1);
% 
% labels = arrayfun(@(x) sprintf('%.3f', x), tick_values_disp, 'UniformOutput', false);
% labels{end+1} = '$\Delta t$';     % 顶部不显示数值（但颜色仍继续）
% cbar.TickLabels = labels;
% set(cbar, 'TickLabelInterpreter', 'latex');  % 开启 LaTeX 解释
% 
% saveas(gcf, 'dt_vs_xy.png');

% % Unorm vs. (x, y)
% figure;
% scatter(x(idx), y(idx), 5, Unorm(idx), 'filled');
% axis equal
% xlabel('x'); ylabel('y');
% xlim([-10,10]); ylim([-10,10])
% title('Gradient norm as a function of (x,y)');
% colormap(flipud(turbo))
% colorbar
% 
% saveas(gcf, 'Unorm_vs_xy.png')