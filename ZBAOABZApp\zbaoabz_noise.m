% run_and_plot_zbaoabz.m
% 1) Simulation parameters
pot    = 0;    % 0=Funnel, 1=Channel, 2=Beale
kern   = 1;    % 0=K1,     1=K2
grad_type = 1;     % New: 0=Standard, 1=Perturbed
nmax   = 1e7;
nmeas  = 100;
dtau   = 1;
gamma  = 5;
alpha  = 0.1;
eps    = 0.1;
sigma  = 1.8;
scaleG = 1;
T      = 1;
m      = 0.0001;
M      = 0.6;
r      = 0.5;
x0     = [0,5];
v0     = [0,0];
z0     = 0;

% 2) Run MEX (all data now in x,y,dt,psi,Umean)
% [x,y,dt,psi,Umean] = zbaoabz_mex( ...
%     pot, kern, nmax, nmeas, dtau, gamma, alpha, eps, ...
%     scaleG, T, m, M, r, x0, v0, z0 );
% [x,y,dt,psi,Umean,Tkin,Tconf] = zbaoabz_core(pot, kern, grad_type, nmax, nmeas, dtau, gamma, alpha, eps, sigma, scaleG, T, m, M, r, x0, v0, z0 );
[x,y,dt_max,psi,Umean,Tkin,Tconf,Unorm] = zbaoabz_core(pot, kern, grad_type, nmax, nmeas, dtau, gamma, alpha, eps, sigma, scaleG, T, m, M, r, x0, v0, z0 );

% 3) Discard burn‐in
burn = 1e5;
idx  = (burn+1):numel(x);

% 4) Make the 3‐panel figure
f1 = figure('Position',[100 100 1800 300]);
% Trajectory
% subplot(1,3,1)
subplot(1,5,1)
plot(x(idx), y(idx), '.', 'MarkerSize',1)
title('Trajectory (x vs y)')
xlabel('x'); ylabel('y')
xlim([-10,10]); ylim([-10,10])
grid on

% Δt histogram
% subplot(1,3,2)
% histogram(dt, 100, 'Normalization','pdf')
% hold on
% xline(mean(dt),'r--','Label',sprintf('mean \\Delta t=%.4f',mean(dt)));
% hold off
% title('Adaptive step‐size')
% xlabel('\Delta t'); ylabel('pdf')
% grid on
% subplot(1,3,2)
subplot(1,5,2)
h = histogram(dt_max, 100, 'Normalization','pdf');
hold on

m = mean(dt_max);
hx = xline(m, 'r--', 'LineWidth',1.5, ...
    'Label', sprintf('mean \\Delta t = %.4f', m), ...
    'LabelOrientation', 'horizontal', ...
    'LabelHorizontalAlignment','center', ...
    'LabelVerticalAlignment','top' ...
);
hold off

xlabel('\Delta t'); ylabel('pdf')
title('Adaptive step‐size')
grid on

% Running mean 
% subplot(1,3,3)
subplot(1,5,3)
plot((1:numel(Umean))*nmeas, Umean)
hold on, yline(1.1,'r--'), hold off
title('Time-averaged potential energy')
xlabel('Iteration'); ylabel('\langle U\rangle')
ylim([0,2])
grid on

% Tkin mean
subplot(1,5,4)
plot((1:numel(Tkin))*nmeas, Tkin)
hold on, yline(1,'r--'), hold off
title('Time-averaged kinetic temperature')
xlabel('Iteration'); ylabel('\langle T_{kin}\rangle')
ylim([0,2])
grid on

% Tconf mean
subplot(1,5,5)
plot((1:numel(Tconf))*nmeas, Tconf)
hold on, yline(1,'r--'), hold off
title('Time-averaged configurational temperature')
xlabel('Iteration'); ylabel('\langle T_{conf}\rangle')
ylim([0,2])
grid on

% 5) Save the figure only
saveas(f1,'zbaoabz_panels.png')

% % 6) Optional 2D comparison, weighted histogram via histcounts2
% nbins = 100;
% % build edges from –10 to 10, with nbins bins → nbins+1 edges
% [counts, xedges, yedges] = histcounts2( ...
%     x(idx), y(idx), ...
%     'NumBins',      [nbins nbins], ...
%     'XBinLimits',   [-10 10], ...
%     'YBinLimits',   [-10 10], ...
%     'Weights',      psi(idx), ...
%     'Normalization','pdf'  ...
% );
% 
% % plot
% figure;
% pcolor(XC,YC,counts);
% shading interp; axis square;
% xlim([-10,10]); ylim([-10,10]);
% xlabel('x'); ylabel('y');
% title('Weighted 2D histogram');
% colorbar;
% 
% % (b) Canonical PDF as before
% N = 200;
% [xg,yg] = meshgrid(linspace(-10,10,N));
% V = 0.5*xg.^2.*exp(-yg) + 0.5*eps*(xg.^2+yg.^2);
% pdf = exp(-V);
% pdf = pdf / sum(pdf(:));
% 
% subplot(1,2,2);
% pcolor(xg,yg,pdf);
% shading interp; axis square;
% xlim([-10,10]); ylim([-10,10]);
% xlabel('x'); ylabel('y');
% title('2D canonical PDF');
% colorbar;
% 
% % Save
% saveas(gcf,'zbaoabz_2d_compare.png');