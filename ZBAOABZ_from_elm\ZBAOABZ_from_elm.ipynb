{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import torch\n", "import copy\n", "import matplotlib.pyplot as plt\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "###############################################################################\n", "# 1) Define the 2D Neal’s Funnel potential and gradient\n", "###############################################################################\n", "def U_funnel_2d(params: torch.Tensor, eps=0.1):\n", "    \"\"\"\n", "    <PERSON> <PERSON>'s Funnel potential: U(x, theta) =  x^2/(2 e^theta) + eps/2 (x^2 + theta^2).\n", "    params: shape (..., 2), last dim are (x, theta).\n", "    eps   : small positive constant controlling regularization or funnel strength.\n", "    returns: shape (...).\n", "    \"\"\"\n", "    x, theta = params[...,0], params[...,1]\n", "    # x^2/(2 e^theta) + eps/2 (x^2 + theta^2)\n", "    return 0.5*x.pow(2)*torch.exp(-theta) + 0.5*eps*(x.pow(2)+theta.pow(2))\n", "\n", "# def U_funnel_2d(x, theta, eps=0.1):\n", "#     \"\"\"\n", "#     2D <PERSON>'s Funnel potential: U(x, theta) =  x^2/(2 e^theta) + eps/2 (x^2 + theta^2).\n", "#     params: shape (..., 2), last dim are (x, theta).\n", "#     eps   : small positive constant controlling regularization or funnel strength.\n", "#     returns: shape (...).\n", "#     \"\"\"\n", "#     # x, theta = params[...,0], params[...,1]\n", "#     # x^2/(2 e^theta) + eps/2 (x^2 + theta^2)\n", "#     return 0.5*x.pow(2)*torch.exp(-theta) + 0.5*eps*(x.pow(2)+theta.pow(2))\n", "\n", "def grad_funnel_2d(params: torch.Tensor, eps=0.1):\n", "    \"\"\"\n", "    Gradient of the 2D Neal's Funnel potential wrt (x, theta).\n", "    grad wrt x:      (d/dx) [ x^2/(2 e^theta) + eps/2 (x^2 + theta^2 ) ]\n", "                   = x e^(-theta) + eps x\n", "    grad wrt theta: (d/dtheta) [ etc. ]\n", "                   = - (x^2)/2 * e^(-theta)  + eps theta\n", "    returns: shape (..., 2).\n", "    \"\"\"\n", "    x, theta = params[...,0], params[...,1]\n", "    gx = x*torch.exp(-theta) + eps*x\n", "    gtheta = -0.5*x.pow(2)*torch.exp(-theta) + eps*theta\n", "    return torch.stack([gx, gtheta], dim=-1)\n", "\n", "# def grad_funnel_2d(x, theta, eps=0.1):\n", "#     \"\"\"\n", "#     Gradient of the 2D Neal's Funnel potential wrt (x, theta).\n", "#     grad wrt x:      (d/dx) [ x^2/(2 e^theta) + eps/2 (x^2 + theta^2 ) ]\n", "#                    = x e^(-theta) + eps x\n", "#     grad wrt theta: (d/dtheta) [ etc. ]\n", "#                    = - (x^2)/2 * e^(-theta)  + eps theta\n", "#     returns: shape (..., 2).\n", "#     \"\"\"\n", "#     # x, theta = params[...,0], params[...,1]\n", "#     gx = x*torch.exp(-theta) + eps*x\n", "#     gtheta = -0.5*x.pow(2)*torch.exp(-theta) + eps*theta\n", "#     return torch.stack([gx, gtheta], dim=-1)\n", "\n", "###############################################################################\n", "# 2) Fixed-step BAOAB for underdamped <PERSON><PERSON>\n", "###############################################################################\n", "class BAOAB_Constants:\n", "    \"\"\"\n", "    This mimics your BAOABhclass from the question:\n", "      h:    step size\n", "      ...\n", "    \"\"\"\n", "    def __init__(self, h, gamma_val):\n", "        \"\"\"\n", "        h:         float or 0-D tensor\n", "        gamma_val: friction\n", "        \"\"\"\n", "        # Convert to double for math\n", "        h_  = float(h)\n", "        gam_= float(gamma_val)\n", "        gh  = gam_ * h_\n", "        # exponential factor\n", "        self.eta = math.exp(-gh)\n", "        # Terms\n", "        # The scheme below is just a consistent version as in the paper (OBAB or BAOAB).\n", "        self.xc1 = 0.5*h_*(1+self.eta)\n", "        self.xc2 = 0.5*h_*h_*(1+self.eta)\n", "        # sqrt(1 - e^{-2gh}), or in your version: sqrt(-expm1(-2gh))\n", "        # but watch sign/h domain:\n", "        # We'll do:\n", "        tmp = -math.expm1(-2.*gh)\n", "        tmp = max(tmp, 1e-16)  # guard\n", "        self.xc3 = 0.5*h_* math.sqrt(tmp)\n", "        self.vc1 = self.eta*0.5*h_\n", "        self.vc2 = 0.5*h_\n", "        self.vc3 = math.sqrt(tmp)*0.5\n", "\n", "def BAOAB_single_step(x, v, gradU, bconst: BAOAB_Constants):\n", "    \"\"\"\n", "    Perform 1 step of BAOAB integrator for underdamped Langevin:\n", "\n", "       p <- p - (h/2)*gradU\n", "       ...\n", "    or the method you had. We do the version consistent with the \n", "    paper's eqn. Possibly a 'splitting' version. We'll just do a quick \n", "    'BAOAB-like' step in one function for demonstration.\n", "    \n", "    x, v, gradU: each shape (2,) for 2D problem\n", "    returns: (xnew, vnew, gradUnew)\n", "    \"\"\"\n", "    with torch.no_grad():\n", "        xi = torch.randn_like(x)            # random for the O part\n", "        # A step: x update\n", "        x_new = x + bconst.xc1*v - bconst.xc2*gradU + bconst.xc3*xi\n", "\n", "        # fetch new grad\n", "        gradU_new = grad_funnel_2d(x_new)\n", "\n", "        # B+O step: v update\n", "        v_new = bconst.eta*v - bconst.vc1*gradU - bconst.vc2*gradU_new + bconst.vc3*xi\n", "\n", "    return x_new, v_new, gradU_new\n", "\n", "###############################################################################\n", "# 3) Z-step logic: auxiliary variable ζ, \n", "#    plus the time transform kernel ψ(ζ)\n", "###############################################################################\n", "# def psi_of_zeta(z, m=0.1, M=10.0, r=0.5):\n", "#     \"\"\"\n", "#     The Sundman transform kernel, eq. (25) from paper, version (1):\n", "#        ψ(1)(ζ) = m * ( ζ^r + M ) / ( ζ^r + m ).\n", "#     \"\"\"\n", "#     # clamp z so it's never negative or zero\n", "#     if z<1e-14:\n", "#         z=1e-14\n", "#     zr = z**r\n", "#     return m*(zr + M)/(zr + m)\n", "\n", "def psi_of_zeta(z, m=0.1, M=10.0, r=0.5):\n", "    \"\"\"\n", "    The Sundman transform kernel, eq. (25) from paper, version (1):\n", "       ψ(1)(ζ) = m * ( ζ^r + M ) / ( ζ^r + m ).\n", "    \"\"\"\n", "    # clamp z so it's never negative or zero\n", "    # if z<1e-14:\n", "    #     z=1e-14\n", "    zr = z**r\n", "    return m*(zr + M/m)/(zr + 1)\n", "\n", "def z_half_step(z_old, alpha, half_dtau, g_val):\n", "    \"\"\"\n", "    The half-step update for z using eq. (17) from paper:\n", "      z_new = e^{-alpha * half_dtau} z_old + [1 - e^{-alpha * half_dtau}]/alpha * g_val\n", "    \"\"\"\n", "    rho = math.exp(-alpha*half_dtau)\n", "    return rho*z_old + (1.-rho)/alpha*g_val\n", "\n", "###############################################################################\n", "# 4) The main <PERSON><PERSON><PERSON><PERSON> (ZBAOABZ) routine for <PERSON>'s funnel\n", "###############################################################################\n", "def SamAdams_ZBAOABZ_funnel2d(x_init, v_init, z0,\n", "                              nmax,   # total iteration steps\n", "                              nmeas,  # how often to store samples\n", "                              dtau,   # ∆τ\n", "                              gamma_val=1.0, \n", "                              alpha=0.1,\n", "                              eps=0.1,\n", "                              scale_g=1.0,\n", "                              m=0.1, M=10.0, r=0.5):\n", "    \"\"\"\n", "    Reproduces the method from the paper, Section 5.2.1 for 2D Neal funnel.\n", "\n", "    x_init, v_init: shape (2,) initial guess\n", "    z0: initial ζ (scalar)\n", "    nmax: total steps in 'fictitious' time\n", "    nmeas: store or \"collect\" sample every nmeas steps\n", "    dtau: base stepsize in τ\n", "    gamma_val: friction\n", "    alpha:  relaxation rate for z\n", "    eps: funnel's 'epsilon' parameter\n", "    scale_g: scale factor for monitor function g\n", "    (m, M, r): parameters for Sundman transform kernel ψ\n", "    \"\"\"\n", "    x = x_init.clone().detach()\n", "    v = v_init.clone().detach()\n", "\n", "    # for convenience, do gradient now:\n", "    gradU = grad_funnel_2d(x, eps=eps)\n", "    # gradU = grad_funnel_2d(x, v, eps=eps)\n", "\n", "    zeta = float(z0)\n", "    half_dtau = 0.5*dtau\n", "\n", "    # arrays to store\n", "    store_size = (nmax//nmeas)+1\n", "    X_store = torch.zeros((store_size,2), dtype=torch.float, device=device)\n", "    Z_store = torch.zeros((store_size,),   dtype=torch.float, device=device)\n", "    steps_store = []\n", "\n", "    count = 0\n", "    X_store[count] = x\n", "    Z_store[count] = zeta\n", "    steps_store.append(0)\n", "\n", "    def monitor_g(x_, v_, gradU_):\n", "        \"\"\"\n", "        The 'g(x,p)' from the paper: sum-of-square or norm-of gradient \n", "        => we'll do: ∥gradU∥ / scale_g or ∥gradU∥^1 or ^2. \n", "        Let's do ∥gradU∥^1 for demonstration: \n", "        \"\"\"\n", "        gn = gradU_.norm(p=1)  # or p=2 \n", "        return float(gn/scale_g)\n", "\n", "    for step_i in range(nmax):\n", "        # 1) first half-step in ζ\n", "        g_val = monitor_g(x, v, gradU)\n", "        z_half = z_half_step(zeta, alpha, half_dtau, g_val)\n", "\n", "        # 2) compute real stepsize = ψ(z_half)*∆τ\n", "        psival = psi_of_zeta(z_half, m=m, M=M, r=r)\n", "        real_dt = psival*dtau\n", "\n", "        # 3) BAOAB step\n", "        bconst = BAOAB_Constants(real_dt, gamma_val)\n", "        xnew, vnew, gradUnew = BAOAB_single_step(x, v, gradU, bconst)\n", "\n", "        # 4) second half-step in ζ\n", "        g_val2 = monitor_g(xnew, vnew, gradUnew)\n", "        z_new = z_half_step(z_half, alpha, half_dtau, g_val2)\n", "\n", "        # update\n", "        x, v, gradU = xnew, vnew, gradUnew\n", "        zeta = z_new\n", "\n", "        # store if needed\n", "        if (step_i+1)%nmeas==0:\n", "            count += 1\n", "            X_store[count] = x\n", "            Z_store[count] = zeta\n", "            steps_store.append(step_i+1)\n", "    \n", "    return X_store[:count+1], Z_store[:count+1], steps_store\n", "\n", "###############################################################################\n", "# 5) Demo / Plot \n", "###############################################################################\n", "if __name__==\"__main__\":\n", "    # For Section 5.2.1 in the paper, pick T=1 => friction gamma ~ 1 or 5\n", "    # We'll just do gamma=1. \n", "    # funnel with eps=0.1. \n", "    # We'll do nmax=2e5 or 1e6 for a bigger run.\n", "\n", "    # initial conditions in the wide part:\n", "    x0 = torch.tensor([0.0, 5.0], device=device)\n", "    v0 = torch.zeros_like(x0)\n", "    z0 = 0.0\n", "\n", "    # typical parameters\n", "    alpha_in  = 0.1      # slow relaxation for z\n", "    dtau_in   = 0.01\n", "    gamma_in  = 5.0\n", "    eps_in    = 0.1\n", "    scale_gin = 1.0\n", "    m_in, M_in, r_in = (0.01, 60.0, 0.5)\n", "\n", "    nmax = 10**8\n", "    nmeas= 200  # sample every 1000 steps\n", "\n", "    <PERSON>_traj, <PERSON>_traj, step_arr = SamAdams_ZBAOABZ_funnel2d(\n", "        x_init=x0, v_init=v0, z0=z0,\n", "        nmax=nmax, nmeas=nmeas, dtau=dtau_in,\n", "        gamma_val=gamma_in, alpha=alpha_in, eps=eps_in,\n", "        scale_g=scale_gin, m=m_in, M=M_in, r=r_in\n", "    )\n", "\n", "    # X_traj shape = (num_stored, 2)\n", "\n", "    # Plot the funnel shape as a reference, plus the path\n", "    xvals = X_traj[:,0].cpu().numpy()\n", "    tvals = X_traj[:,1].cpu().numpy()\n", "\n", "    fig, axs = plt.subplots(1,2, figsize=(10,4))\n", "\n", "    # left plot: the trajectory in x-theta space\n", "    axs[0].plot(xvals, tvals, 'b.', alpha=0.3, markersize=1)\n", "    axs[0].set_title(\"SamAdams trajectory in (theta vs. x)\")\n", "    axs[0].set_xlabel(\"x\")\n", "    axs[0].set_ylabel(\"theta\")\n", "\n", "    # right plot: the adaptive stepsize distribution\n", "    # We can reconstruct the used stepsize each time from zeta at half step:\n", "    # but we only stored final zeta each iteration. Let's approximate it:\n", "    #   ∆t_n ~ ψ(zeta_n)* dtau\n", "    zvals = Z_traj.cpu().numpy()\n", "    psivals = [psi_of_zeta(zzi, m=m_in, M=M_in, r=r_in) for zzi in zvals]\n", "    dtvals = [pvv*dtau_in for pvv in psivals]\n", "\n", "    axs[1].hist(dtvals, bins=50, density=True, alpha=0.7)\n", "    axs[1].axvline(x=sum(dtvals)/len(dtvals), color='r', label=\"mean ∆t\")\n", "    axs[1].set_title(\"Histogram of adaptive ∆t\")\n", "    axs[1].set_xlabel(\"stepsize ∆t\")\n", "    axs[1].legend()\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # done\n"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}