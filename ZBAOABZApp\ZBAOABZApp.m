classdef ZBAOABZApp < matlab.apps.AppBase
    % ZBAOAB<PERSON>App  Interactive App for ZBAOABZ Simulation
    %
    % This App Designer class provides UI controls to configure and run the
    % C++ ZBAOABZ simulation for different potentials, parameters, and
    % kernels, then loads and plots the results.

    % Properties that correspond to app components
    properties (Access = public)
        UIFigure             matlab.ui.Figure
        GridLayout           matlab.ui.container.GridLayout
        ControlPanel         matlab.ui.container.Panel
        RunButton            matlab.ui.control.Button
        KernelDropDownLabel  matlab.ui.control.Label
        KernelDropDown       matlab.ui.control.DropDown
        PotentialDropDownLabel matlab.ui.control.Label
        PotentialDropDown    matlab.ui.control.DropDown
        NmaxEditFieldLabel   matlab.ui.control.Label
        NmaxEditField        matlab.ui.control.NumericEditField
        NmeasEditFieldLabel  matlab.ui.control.Label
        NmeasEditField       matlab.ui.control.NumericEditField
        dtauEditFieldLabel   matlab.ui.control.Label
        dtauEditField        matlab.ui.control.NumericEditField
        gammaEditFieldLabel  matlab.ui.control.Label
        gammaEditField       matlab.ui.control.NumericEditField
        alphaEditFieldLabel  matlab.ui.control.Label
        alphaEditField       matlab.ui.control.NumericEditField
        epsEditFieldLabel    matlab.ui.control.Label
        epsEditField         matlab.ui.control.NumericEditField
        scaleGEditFieldLabel matlab.ui.control.Label
        scaleGEditField      matlab.ui.control.NumericEditField
        TEditFieldLabel      matlab.ui.control.Label
        TEditField           matlab.ui.control.NumericEditField
        mEditFieldLabel      matlab.ui.control.Label
        mEditField           matlab.ui.control.NumericEditField
        MEditFieldLabel      matlab.ui.control.Label
        MEditField           matlab.ui.control.NumericEditField
        rEditFieldLabel      matlab.ui.control.Label
        rEditField           matlab.ui.control.NumericEditField
        x0EditFieldLabel     matlab.ui.control.Label
        x0EditField          matlab.ui.control.EditField
        v0EditFieldLabel     matlab.ui.control.Label
        v0EditField          matlab.ui.control.EditField
        z0EditFieldLabel     matlab.ui.control.Label
        z0EditField          matlab.ui.control.NumericEditField
        PlotPanel            matlab.ui.container.Panel
        TrajAxes             matlab.ui.control.UIAxes
        HistAxes             matlab.ui.control.UIAxes
        UmeanAxes            matlab.ui.control.UIAxes
        WeightedHistAxes     matlab.ui.control.UIAxes
    end

    methods (Access = private)
        function RunButtonPushed(app, event)
            % ... (保持你原有的代码不变)
        end
    end

    methods (Access = public)
        % Construct app
        function app = ZBAOABZApp
            createComponents(app);
        end
        
        % Create UI components
        function createComponents(app)
            % 创建主窗口
            app.UIFigure = uifigure('Name', 'ZBAOABZ Simulation');
            app.UIFigure.Position = [100 100 1200 700];
            app.UIFigure.Scrollable = 'on';

            % 创建主网格布局 (2行1列)
            app.GridLayout = uigridlayout(app.UIFigure);
            app.GridLayout.RowHeight = {'1x', '2x'};
            app.GridLayout.ColumnWidth = {'1x'};

            % 创建控制面板
            app.ControlPanel = uipanel(app.GridLayout);
            app.ControlPanel.Layout.Row = 1;
            app.ControlPanel.Layout.Column = 1;
            app.ControlPanel.Title = 'Simulation Parameters';
            app.ControlPanel.Scrollable = 'on'; % 允许滚动

            % 在控制面板内创建参数网格 (20行2列)
            paramGrid = uigridlayout(app.ControlPanel);
            paramGrid.RowHeight = repmat({'fit'}, 1, 20);
            paramGrid.ColumnWidth = [150, 200];
            paramGrid.Padding = [10 10 10 10];
            paramGrid.RowSpacing = 5;
            paramGrid.ColumnSpacing = 10;

            % === 参数控件创建 ===
            % 下拉菜单和标签
            createDropdown(app, paramGrid, 1, 'Potential', {'Funnel','Channel','Beale'});
            createDropdown(app, paramGrid, 2, 'Kernel', {'Kernel1','Kernel2'});
            
            % 数值输入框和标签
            createNumericField(app, paramGrid, 3, 'Nmax', 10000);
            createNumericField(app, paramGrid, 4, 'Nmeas', 100);
            createNumericField(app, paramGrid, 5, 'dtau', 0.01);
            createNumericField(app, paramGrid, 6, 'gamma', 1.0);
            createNumericField(app, paramGrid, 7, 'alpha', 0.1);
            createNumericField(app, paramGrid, 8, 'eps', 0.1);
            createNumericField(app, paramGrid, 9, 'scaleG', 1.0);
            createNumericField(app, paramGrid, 10, 'T', 1.0);
            createNumericField(app, paramGrid, 11, 'm', 1.0);
            createNumericField(app, paramGrid, 12, 'M', 1.0);
            createNumericField(app, paramGrid, 13, 'r', 1.0);
            createNumericField(app, paramGrid, 14, 'z0', 0.0);
            
            % 文本输入框 (x0, v0)
            createTextInput(app, paramGrid, 15, 'x0', '0,0');
            createTextInput(app, paramGrid, 16, 'v0', '0,0');
            
            % 运行按钮 (跨两列)
            app.RunButton = uibutton(paramGrid, 'push');
            app.RunButton.Text = 'Run Simulation';
            app.RunButton.ButtonPushedFcn = createCallbackFcn(app, @RunButtonPushed, true);
            app.RunButton.Layout.Row = 20;
            app.RunButton.Layout.Column = [1 2];
            app.RunButton.BackgroundColor = [0 0.5 0];
            app.RunButton.FontColor = [1 1 1];
            app.RunButton.FontWeight = 'bold';

            % === 绘图面板 ===
            app.PlotPanel = uipanel(app.GridLayout);
            app.PlotPanel.Layout.Row = 2;
            app.PlotPanel.Layout.Column = 1;
            app.PlotPanel.Title = 'Results';

            % 创建2x2网格用于坐标轴
            plotGrid = uigridlayout(app.PlotPanel);
            plotGrid.RowHeight = {'1x', '1x'};
            plotGrid.ColumnWidth = {'1x', '1x'};
            plotGrid.Padding = [10 10 10 10];
            
            % 创建坐标轴
            app.TrajAxes = uiaxes(plotGrid);
            title(app.TrajAxes, 'Trajectory (x vs y)');
            app.TrajAxes.Layout.Row = 1;
            app.TrajAxes.Layout.Column = 1;
            
            app.HistAxes = uiaxes(plotGrid);
            title(app.HistAxes, '\Delta t Distribution');
            app.HistAxes.Layout.Row = 1;
            app.HistAxes.Layout.Column = 2;
            
            app.UmeanAxes = uiaxes(plotGrid);
            title(app.UmeanAxes, '<U> vs Samples');
            app.UmeanAxes.Layout.Row = 2;
            app.UmeanAxes.Layout.Column = 1;
            
            app.WeightedHistAxes = uiaxes(plotGrid);
            title(app.WeightedHistAxes, 'Weighted \psi Histogram');
            app.WeightedHistAxes.Layout.Row = 2;
            app.WeightedHistAxes.Layout.Column = 2;
        end
        
        % Code that executes before app deletion
        function delete(app)
            delete(app.UIFigure)
        end
    end
    
    % === 辅助函数 (简化UI创建) ===
    methods (Access = private)
        function createDropdown(app, parent, row, labelText, items)
            % 创建标签
            label = uilabel(parent);
            label.Text = labelText;
            label.Layout.Row = row;
            label.Layout.Column = 1;
            label.HorizontalAlignment = 'right';
            
            % 创建下拉菜单
            dropdown = uidropdown(parent);
            dropdown.Items = items;
            dropdown.Value = items{1};
            dropdown.Layout.Row = row;
            dropdown.Layout.Column = 2;
            
            % 存储到对应属性
            switch labelText
                case 'Potential'
                    app.PotentialDropDownLabel = label;
                    app.PotentialDropDown = dropdown;
                case 'Kernel'
                    app.KernelDropDownLabel = label;
                    app.KernelDropDown = dropdown;
            end
        end
        
        function createNumericField(app, parent, row, labelText, defaultVal)
            % 创建标签
            label = uilabel(parent);
            label.Text = labelText;
            label.Layout.Row = row;
            label.Layout.Column = 1;
            label.HorizontalAlignment = 'right';
            
            % 创建数值输入框
            numField = uieditfield(parent, 'numeric');
            numField.Value = defaultVal;
            numField.Layout.Row = row;
            numField.Layout.Column = 2;
            
            % 存储到对应属性
            switch labelText
                case 'Nmax'
                    app.NmaxEditFieldLabel = label;
                    app.NmaxEditField = numField;
                case 'Nmeas'
                    app.NmeasEditFieldLabel = label;
                    app.NmeasEditField = numField;
                case 'dtau'
                    app.dtauEditFieldLabel = label;
                    app.dtauEditField = numField;
                case 'gamma'
                    app.gammaEditFieldLabel = label;
                    app.gammaEditField = numField;
                case 'alpha'
                    app.alphaEditFieldLabel = label;
                    app.alphaEditField = numField;
                case 'eps'
                    app.epsEditFieldLabel = label;
                    app.epsEditField = numField;
                case 'scaleG'
                    app.scaleGEditFieldLabel = label;
                    app.scaleGEditField = numField;
                case 'T'
                    app.TEditFieldLabel = label;
                    app.TEditField = numField;
                case 'm'
                    app.mEditFieldLabel = label;
                    app.mEditField = numField;
                case 'M'
                    app.MEditFieldLabel = label;
                    app.MEditField = numField;
                case 'r'
                    app.rEditFieldLabel = label;
                    app.rEditField = numField;
                case 'z0'
                    app.z0EditFieldLabel = label;
                    app.z0EditField = numField;
            end
        end
        
        function createTextInput(app, parent, row, labelText, defaultVal)
            % 创建标签
            label = uilabel(parent);
            label.Text = labelText;
            label.Layout.Row = row;
            label.Layout.Column = 1;
            label.HorizontalAlignment = 'right';
            
            % 创建文本输入框
            textField = uieditfield(parent, 'text');
            textField.Value = defaultVal;
            textField.Layout.Row = row;
            textField.Layout.Column = 2;
            
            % 存储到对应属性
            switch labelText
                case 'x0'
                    app.x0EditFieldLabel = label;
                    app.x0EditField = textField;
                case 'v0'
                    app.v0EditFieldLabel = label;
                    app.v0EditField = textField;
            end
        end
    end
end