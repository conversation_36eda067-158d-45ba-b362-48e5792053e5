#include "zbaoabz_with_batch_core.hpp"
#include <cmath>
#include <random>
#include <numeric>

// --- Constants and Utility Structs ---
static std::random_device rd;
static std::mt19937 gen(rd()); 
static std::normal_distribution<double> normal_dist(0.0, 1.0);

const double PI = 3.14159265358979323846;

struct Vector2D {
    double x, y;
    Vector2D() : x(0.0), y(0.0) {}
    Vector2D(double x_, double y_) : x(x_), y(y_) {}
    Vector2D operator+(const Vector2D& other) const { return Vector2D(x + other.x, y + other.y); }
    Vector2D operator-(const Vector2D& other) const { return Vector2D(x - other.x, y - other.y); }
    Vector2D operator*(double scalar) const { return Vector2D(x * scalar, y * scalar); }
    double norm2() const { return std::sqrt(x*x + y*y); }
    double dot(const Vector2D& other) const { return x * other.x + y * other.y; }
};


// --- Potential and Gradient Functions (No Changes) ---

// Potentials
inline double U_funnel_2d(const Vector2D& p, double eps) {
    double x = p.x, t = p.y;
    return 0.5 * x*x * std::exp(-t) + 0.5 * eps * (x*x + t*t);
}
// ... (U_channel_2d, U_beale_2d remain the same)
inline double U_channel_2d(const Vector2D& p) {
    double x = p.x, y = p.y;
    double x2 = x*x, x4 = x2*x2;
    return 100.0 * y*y/(1+10*x4) + 0.001 * (x2-9)*(x2-9);
}
inline double U_beale_2d(const Vector2D& p) {
    double x = p.x, y = p.y;
    constexpr double c1=1.5, c2=2.25, c3=2.625;
    double xy=y*x;
    double t1=c1 - x + x*y;
    double t2=c2 - x + x*y*y;
    double t3=c3 - x + x*y*y*y;
    return t1*t1 + t2*t2 + t3*t3;
}


// Standard Gradients
inline Vector2D grad_funnel_2d(const Vector2D& p, double eps) {
    double x=p.x, t=p.y;
    double gx = x*std::exp(-t) + eps*x;
    double gy = -0.5*x*x*std::exp(-t) + eps*t;
    return Vector2D(gx, gy);
}
// ... (grad_channel_2d, grad_beale_2d remain the same)
inline Vector2D grad_channel_2d(const Vector2D& p) {
    double x=p.x, y=p.y;
    double x2=x*x, x4=x2*x2;
    double den = (1+10*x4);
    double gx = -4000.0*x*x2*y*y/(den*den) + 0.004*x*(x2-9.0);
    double gy = 200.0*y/den;
    return Vector2D(gx, gy);
}
inline Vector2D grad_beale_2d(const Vector2D& p) {
    double x=p.x, y=p.y;
    constexpr double c1=1.5, c2=2.25, c3=2.625;
    double t1=c1 - x + x*y;
    double t2=c2 - x + x*y*y;
    double t3=c3 - x + x*y*y*y;
    double gx = 2*t1*(-1+y) + 2*t2*(-1+y*y) + 2*t3*(-1+y*y*y);
    double gy = 2*t1*x   + 2*t2*2*x*y   + 2*t3*3*x*y*y;
    return Vector2D(gx, gy);
}


// Perturbed Gradients (Uses dynamic sigma passed as argument)
Vector2D perturbed_grad_funnel(const Vector2D& p, double sigma, std::mt19937_64& gen) {
    std::normal_distribution<> N(0.0, 1.0);
    double random_scale = N(gen) * sigma;
    double x = p.x, y = p.y;
    double gx = (1.0 + random_scale) * x*std::exp(-y) + (1.0 - random_scale) * 0.1*x;
    double gy = (1.0 + random_scale) * (-0.5*x*x*std::exp(-y)) + (1.0 - random_scale) * 0.1*y;
    return {gx, gy};
}
// ... (perturbed_grad_channel remains the same)
Vector2D perturbed_grad_channel(const Vector2D& p, double sigma, std::mt19937_64& gen) {
    std::normal_distribution<> N(0.0,1.0);
    double random_scale = N(gen) * sigma;
    double x = p.x, y = p.y;
    double denom = 1.0 + 10.0*std::pow(x,4);
    double gx = - (1.0 + random_scale) * 4000.0*std::pow(x,3)*y*y / std::pow(denom,2)
              + (1.0 - random_scale) * 0.004*x*(x*x - 9.0);
    double gy = (1.0 + random_scale) * 200.0*y / denom;
    return {gx, gy};
}


// --- SamAdams Step-Size Control Functions (dt) ---
double monitor_g_dt(const Vector2D& gradU, double scale_g) {
    return gradU.norm2() / scale_g;
}

double psi_of_zeta_k(double z, double m, double M, double r) {
    double zr = std::pow(z, r);
    return m * (zr + M/m) / (zr + 1.0); // Using K2 from paper
}

double zeta_step(double z_old, double alpha, double dtau, double gval) {
    double rho = std::exp(-alpha * dtau);
    return rho * z_old + (1.0 - rho)/alpha * gval;
}


// --- NEW: Adaptive Noise Control Functions (sigma) ---

// New monitor function for sigma with a threshold.
// Returns a positive value (penalty) if the gap is large, negative (reward) if small.
// double monitor_g_sigma(double Tconf_, double T_, double threshold) {
//     double gap = std::abs(Tconf_ - T_) / T_;
//     return std::log(gap / threshold + 1e-8);
// }
double monitor_g_sigma(double Tconf_, double T_, double threshold) {
    double gap = std::abs(Tconf_ - T_) / T_;
    return gap - threshold;
}

// Robust mapping function for sigma using a generalized sigmoid.
// This maps the full range of sigma_zeta to [sigma_min, sigma_max].
double psi_sigma(double sigma_zeta, double sigma_min, double sigma_max, double steepness) {
    double sigma_range = sigma_max - sigma_min;
    double atan_val = std::atan(steepness * sigma_zeta);
    // CORRECTED: Use a minus sign to make it a decreasing function
    return (sigma_min + sigma_max) / 2.0 - (sigma_range / PI) * atan_val;
}
// double psi_sigma(double sigma_zeta, double sigma_min, double sigma_max, double steepness) {
//     double sigma_range = sigma_max - sigma_min;
//     // The negative sign in the exponent makes it a decreasing function.
//     double sigmoid = 1.0 / (1.0 + std::exp(steepness * sigma_zeta));
//     return sigma_min + sigma_range * sigmoid;
// }
// double psi_sigma(double sigma_zeta, double sigma_min, double sigma_max, double steepness) {
//     double zr_ = std::pow(sigma_zeta, steepness);
//     double sigma_range = sigma_max - sigma_min;
//     return 1 - std::tanh(4/sigma_range * (zr_ - sigma_min) + 2);
// }


// --- BAOAB Integrator Components (No Changes) ---
struct BAOAB_Constants {
    double eta, xc1, xc2, xc3, vc1, vc2, vc3;
    BAOAB_Constants(double dt, double gamma, double T=1.0) {
        double gh = gamma * dt;
        eta = std::exp(-gh);
        xc1 = 0.5*dt*(1+eta);
        xc2 = 0.25*dt*dt*(1+eta);
        double tmp = -std::expm1(-2.0*gh);
        tmp = std::max(tmp, 1e-16);
        xc3 = 0.5*dt*std::sqrt(tmp * T);
        vc1 = 0.5*dt*eta;
        vc2 = 0.5*dt;
        vc3 = std::sqrt(tmp * T);
    }
};

template<typename GradFunc>
void BAOAB_single_step(Vector2D& x, Vector2D& v, Vector2D& gradU_prev,
                       const BAOAB_Constants& C,
                       GradFunc grad_func)
{
    // A-step
    Vector2D xi(normal_dist(gen), normal_dist(gen));
    Vector2D x_new = x + v*C.xc1 - gradU_prev*C.xc2 + xi*C.xc3;
    Vector2D gradU_new = grad_func(x_new);
    // B+O step
    Vector2D v_new = v*C.eta - gradU_prev*C.vc1 - gradU_new*C.vc2 + xi*C.vc3;
    // update
    x = x_new;
    v = v_new;
    gradU_prev = gradU_new;
}


// --- UPDATED CORE SIMULATION FUNCTION ---
SimulationResult runZBAOABZ(
    Potential pot, Kernel kern, GradientType grad_type,
    int nmax, int nmeas,
    double dtau, double gamma, double eps, double T,
    std::pair<double,double> x0p, std::pair<double,double> v0p,
    double alpha_dt, double scale_g, double m_dt, double M_dt, double r_dt, double z0_dt,
    int burn_in_noise, double initial_sigma, double sigma_min, double sigma_max,
    double alpha_sigma, double threshold_sigma, double steepness_sigma)
{
    // --- Initial State ---
    Vector2D x(x0p.first, x0p.second);
    Vector2D v(v0p.first, v0p.second);
    std::mt19937_64 gen64(rd());

    // --- State variables for adaptive control ---
    double zeta_dt = z0_dt; // For step-size control
    double sigma = initial_sigma; // For noise control, starts at initial value
    double sigma_zeta = 0.0; // Auxiliary variable for sigma, starts at 0 (neutral)

    // --- Lambdas for Potentials and Gradients ---
    auto grad_func = [&](const Vector2D& p) {
        if (grad_type == GradientType::Perturbed) {
            // This lambda now captures the DYNAMIC sigma from the outer scope
            switch(pot) {
                case Potential::Funnel:  return perturbed_grad_funnel(p, sigma, gen64);
                case Potential::Channel: return perturbed_grad_channel(p, sigma, gen64);
                case Potential::Beale:   return grad_beale_2d(p); // Fallback
            }
        }
        // Standard gradient case
        switch(pot) {
            case Potential::Funnel:  return grad_funnel_2d(p, eps);
            case Potential::Channel: return grad_channel_2d(p);
            case Potential::Beale:   return grad_beale_2d(p);
        }
        return grad_channel_2d(p); // Should not be reached
    };

    auto Uval = [&](const Vector2D& p) {
        switch(pot) {
            case Potential::Funnel:  return U_funnel_2d(p, eps);
            case Potential::Channel: return U_channel_2d(p);
            case Potential::Beale:   return U_beale_2d(p);
        }
        return 0.0;
    };
    
    auto psi_dt = [&](double z) {
        return psi_of_zeta_k(z, m_dt, M_dt, r_dt);
    };

    auto psi_sigma_ = [&](double z) {
        return psi_of_zeta_k(z, sigma_min, sigma_max, r_dt);
    };

    // --- Result Storage ---
    SimulationResult res;
    res.x.reserve(nmax + 1);
    res.y.reserve(nmax + 1);
    res.dt.reserve(nmax + 1);
    res.psi.reserve(nmax + 1);
    res.Umean.reserve((nmax + 1) / nmeas + 1);
    res.Tkin.reserve((nmax + 1) / nmeas + 1);
    res.Tconf.reserve((nmax + 1) / nmeas + 1);
    res.sigmas.reserve((nmax + 1) / nmeas + 1);
    res.sigma_zetas.reserve((nmax+1)/nmeas + 1);

    // --- Initial Values ---
    Vector2D gradU = grad_func(x);
    double gval_dt = monitor_g_dt(gradU, scale_g);
    double sum_psi = 0.0;
    
    // Record initial state
    res.x.push_back(x.x);
    res.y.push_back(x.y);
    double current_dt = psi_dt(zeta_dt) * dtau;
    res.dt.push_back(current_dt);
    double p0 = psi_dt(zeta_dt);
    res.psi.push_back(p0);
    // Initial values for running averages are handled in the loop

    // --- MAIN SIMULATION LOOP ---
    for(int i=0; i<nmax; ++i) {
        // 1. Z-half step for dt
        double zhalf_dt = zeta_step(zeta_dt, alpha_dt, 0.5*dtau, gval_dt);
        
        // 2. Determine physical step-size
        current_dt = psi_dt(zhalf_dt) * dtau;
        
        // 3. BAOAB step
        BAOAB_single_step(x, v, gradU, BAOAB_Constants(current_dt, gamma, T), grad_func);
        
        // 4. Z-update for dt
        gval_dt = monitor_g_dt(gradU, scale_g);
        zeta_dt = zeta_step(zhalf_dt, alpha_dt, 0.5*dtau, gval_dt);
        
        // 5. Record trajectory
        res.x.push_back(x.x);
        res.y.push_back(x.y);
        res.dt.push_back(current_dt);
        double current_psi = psi_dt(zeta_dt);
        res.psi.push_back(current_psi);

        // --- ADAPTIVE NOISE & MEASUREMENTS ---
        if (((i + 1) % nmeas) == 0) {
            // --- Adaptive Noise Control Logic ---
            // This block is only active for perturbed gradients AND after the burn-in period.
            // if (grad_type == GradientType::Perturbed && (i + 1) > burn_in_noise) {
            //     double current_Tkin = 0.5 * v.norm2() * v.norm2();
            //     double current_Tconf = 0.5 * x.dot(gradU);
                
            //     // a. Get monitor value (can be positive or negative)
            //     double gval_sigma = monitor_g_sigma(current_Tconf, T, threshold_sigma);
            //     // double gval_sigma = gradU.norm2()/scale_g;

            //     // b. Update auxiliary variable. CRITICAL: Use sigma_zeta as input, not sigma.
            //     // The time step here is dtau*nmeas because this update only happens every nmeas steps.
            //     sigma_zeta = zeta_step(sigma_zeta, alpha_sigma, dtau * nmeas, gval_sigma);

            //     // c. Update the actual noise level using the robust mapping function.
            //     sigma = psi_sigma(sigma_zeta, sigma_min, sigma_max, steepness_sigma);
            //     // sigma = psi_sigma_(sigma_zeta);
            // }

            // --- Running Average Measurements ---
            double current_U = Uval(x);
            double current_Tkin = 0.5 * v.norm2() * v.norm2(); // Note: v is velocity, not momentum
            double current_Tconf = 0.5 * x.dot(gradU);

            if (res.Umean.empty()) { // First measurement
                sum_psi = current_psi;
                res.Umean.push_back(current_U);
                res.Tkin.push_back(current_Tkin);
                res.Tconf.push_back(current_Tconf);
            } else { // Update running averages
                double S_old = sum_psi;
                sum_psi += current_psi;
                res.Umean.push_back(res.Umean.back() * (S_old / sum_psi) + current_U * (current_psi / sum_psi));
                res.Tkin.push_back(res.Tkin.back() * (S_old / sum_psi) + current_Tkin * (current_psi / sum_psi));
                res.Tconf.push_back(res.Tconf.back() * (S_old / sum_psi) + current_Tconf * (current_psi / sum_psi));
            }

            // --- Adaptive Noise Control Logic ---
            // This block is only active for perturbed gradients AND after the burn-in period.
            if (grad_type == GradientType::Perturbed && (i + 1) > burn_in_noise) {
                // double current_Tkin_mean = res.Tkin.back();
                // double current_Tconf_mean = res.Tconf.back();
                
                // a. Get monitor value (can be positive or negative)
                double gval_sigma = monitor_g_sigma(current_Tconf, T, threshold_sigma);
                // double gval_sigma = gradU.norm2()/scale_g;

                // b. Update auxiliary variable. CRITICAL: Use sigma_zeta as input, not sigma.
                // The time step here is dtau*nmeas because this update only happens every nmeas steps.
                sigma_zeta = zeta_step(sigma_zeta, alpha_sigma, dtau * nmeas, gval_sigma);

                // c. Update the actual noise level using the robust mapping function.
                sigma = psi_sigma(sigma_zeta, sigma_min, sigma_max, steepness_sigma);
                // sigma = psi_sigma_(sigma_zeta);
            }

            // Record the state of the noise control system at each measurement point
            res.sigmas.push_back(sigma);
            res.sigma_zetas.push_back(sigma_zeta);
        }
    }

    return res;
}




// Version before 8.2
// #include "zbaoabz_with_batch_core.hpp"
// #include <cmath>
// #include <random>
// #include <numeric>

// // Constants
// static std::random_device rd;
// // Use mt19937 for the original BAOAB noise
// static std::mt19937 gen(rd()); 
// static std::normal_distribution<double> normal_dist(0.0, 1.0);

// const double PI = 3.14159265358979323846;

// // Vector2D class (as in header) - with L1 and L2 norms, dot product, etc.
// struct Vector2D {
//     double x, y;
//     Vector2D() : x(0.0), y(0.0) {}
//     Vector2D(double x_, double y_) : x(x_), y(y_) {}
//     Vector2D operator+(const Vector2D& other) const { return Vector2D(x + other.x, y + other.y); }
//     Vector2D operator-(const Vector2D& other) const { return Vector2D(x - other.x, y - other.y); }
//     Vector2D operator*(double scalar)     const { return Vector2D(x * scalar, y * scalar); }
//     Vector2D operator*(const Vector2D& other) const { return Vector2D(x * other.x, y * other.y); }
//     double norm1() const { return std::abs(x) + std::abs(y); }
//     double norm2() const { return std::sqrt(x*x + y*y); }
//     double dot(const Vector2D& other) const { return x * other.x + y * other.y; }
// };

// double monitor_g(const Vector2D& gradU, double scale_g) {
//     double norm = gradU.norm2();
//     return norm / scale_g;
// }

// // Potentials
// inline double U_funnel_2d(const Vector2D& p, double eps) {
//     double x = p.x, t = p.y;
//     return 0.5 * x*x * std::exp(-t) + 0.5 * eps * (x*x + t*t);
// }

// inline double U_channel_2d(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     double x2 = x*x, x4 = x2*x2;
//     return 100.0 * y*y/(1+10*x4) + 0.001 * (x2-9)*(x2-9);
// }

// inline double U_beale_2d(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     constexpr double c1=1.5, c2=2.25, c3=2.625;
//     double xy=y*x;
//     double t1=c1 - x + x*y;
//     double t2=c2 - x + x*y*y;
//     double t3=c3 - x + x*y*y*y;
//     return t1*t1 + t2*t2 + t3*t3;
// }

// // Standard Gradients
// inline Vector2D grad_funnel_2d(const Vector2D& p, double eps) {
//     double x=p.x, t=p.y;
//     double gx = x*std::exp(-t) + eps*x;
//     double gy = -0.5*x*x*std::exp(-t) + eps*t;
//     return Vector2D(gx, gy);
// }

// inline Vector2D grad_channel_2d(const Vector2D& p) {
//     double x=p.x, y=p.y;
//     double x2=x*x, x4=x2*x2;
//     double den = (1+10*x4);
//     double gx = -4000.0*x*x2*y*y/(den*den) + 0.004*x*(x2-9.0);
//     double gy = 200.0*y/den;
//     return Vector2D(gx, gy);
// }

// inline Vector2D grad_beale_2d(const Vector2D& p) {
//     double x=p.x, y=p.y;
//     constexpr double c1=1.5, c2=2.25, c3=2.625;
//     double t1=c1 - x + x*y;
//     double t2=c2 - x + x*y*y;
//     double t3=c3 - x + x*y*y*y;
//     double gx = 2*t1*(-1+y) + 2*t2*(-1+y*y) + 2*t3*(-1+y*y*y);
//     double gy = 2*t1*x   + 2*t2*2*x*y   + 2*t3*3*x*y*y;
//     return Vector2D(gx, gy);
// }

// // New: Perturbed Gradients
// Vector2D perturbed_grad_funnel(const Vector2D& p, double sigma, std::mt19937_64& gen) {
//     std::normal_distribution<> N(0.0,1.0);
//     double random_scale = N(gen) * sigma;
//     double x = p.x, y = p.y;
//     double gx = (1.0 + random_scale) * x*std::exp(-y) + (1.0 - random_scale) * 0.1*x;
//     double gy = (1.0 + random_scale) * (-0.5*x*x*std::exp(-y)) + (1.0 - random_scale) * 0.1*y;
//     return {gx, gy};
// }

// Vector2D perturbed_grad_channel(const Vector2D& p, double sigma, std::mt19937_64& gen) {
//     std::normal_distribution<> N(0.0,1.0);
//     double random_scale = N(gen) * sigma;
//     double x = p.x, y = p.y;
//     double denom = 1.0 + 10.0*std::pow(x,4);
//     double gx = - (1.0 + random_scale) * 4000.0*std::pow(x,3)*y*y / std::pow(denom,2)
//                 + (1.0 - random_scale) * 0.004*x*(x*x - 9.0);
//     double gy = (1.0 + random_scale) * 200.0*y / denom;
//     return {gx, gy};
// }


// // Sundman kernels
// double psi_of_zeta_k1(double z, double m, double M, double r) {
//     double zr = std::pow(z, r);
//     return m * (zr + M) / (zr + m);
// }

// double psi_of_zeta_k2(double z, double m, double M, double r) {
//     double zr = std::pow(z, r);
//     return m * (zr + M/m) / (zr + 1.0);
// }

// // Half-step for zeta
// double z_half_step(double z_old, double alpha, double half_dtau, double gval) {
//     double rho = std::exp(-alpha * half_dtau);
//     return rho * z_old + (1.0 - rho)/alpha * gval;
// }

// // Monitor function for sigma (batch)
// double monitor_sigma(double Tconf_, double T_) {
//     double percent = std::abs(Tconf_ - T_) / T_;
//     double threshold = 0.05;
//     return percent - threshold;                                                                                    
// }

// // double monitor_sigma(double Tconf_, double T_) {
// //     double percent = std::abs(Tconf_ - T_) / T_;
// //     constexpr double C   = 1.0;
// //     constexpr int k   = 2;
// //     constexpr double max   = 0.1;     // “max” percent before tan argument = π
// //     if (percent < max) {
// //         return C * std::pow(percent, k) / (max - percent);
// //     } else {
// //         return 1000;
// //     }                                                                                      
// // }

// // double monitor_sigma(double Tconf_, double T_) {
// //     double percent = std::abs(Tconf_ - T_) / T_;
// //     constexpr double C   = 1.0;
// //     constexpr double max   = 0.1;     // “max” percent before tan argument = π
// //     double arg = PI * percent / max;
// //     // clamp arg to avoid tan(arg) → ∞ at π/2 + kπ
// //     if (std::abs(arg) > PI*0.49) 
// //         arg = (arg > 0 ?  PI*0.49 : -PI*0.49);
// //     return C * std::tan(arg);
// // }

// double sigma_zstep(double sigma_old, double alpha, double dtau, double gval) {
//     double rho = std::exp(-alpha * dtau);
//     return rho * sigma_old + (1.0 - rho)/alpha * gval;
// }

// double psi_sigma(double sigma_zeta) {
//     // The range of the noise level
//     double sigma_min = 0.1; double sigma_max = 4.0; double steepness = 1.0;
//     double sigma_range = sigma_max - sigma_min;
    
//     // A standard sigmoid function is 1.0 / (1.0 + exp(-x)).
//     // The negative sign in `-steepness * sigma_zeta` makes it a decreasing function.
//     double sigmoid = 1.0 / (1.0 + std::exp(steepness * sigma_zeta));
    
//     // Scale and shift the sigmoid output to the desired range
//     return sigma_min + sigma_range * sigmoid;
// }

// // double psi_sigma(double sigma_zeta) {
// //     double zr_ = std::pow(sigma_zeta, 0.5);
// //     double m_ = 0.1; double M_ = 4.0;
// //     double gap = M_ - m_;
// //     return 1 - std::tanh(4/gap * (zr_ - m_) + 2);
// // }

// // double psi_sigma(double sigma_zeta) {
// //     double zr_ = std::pow(sigma_zeta, 0.5);
// //     double m_ = 0.01; double M_ = 3.0;
// //     return m_ * (zr_ + M_) / (zr_ + m_);
// // }

// // double psi_sigma(double sigma_zeta){
// //     double zr_ = std::pow(sigma_zeta, 0.5);
// //     double m_ = 0.01; double M_ = 3.0;
// //     return m_ * (zr_ + M_/m_) / (zr_ + 1.0);
// // }

// // BAOAB Constants
// struct BAOAB_Constants {
//     double eta, xc1, xc2, xc3, vc1, vc2, vc3;
//     BAOAB_Constants(double dt, double gamma, double T=1.0) {
//         double gh = gamma * dt;
//         eta = std::exp(-gh);
//         xc1 = 0.5*dt*(1+eta);
//         xc2 = 0.25*dt*dt*(1+eta);
//         double tmp = -std::expm1(-2.0*gh);
//         tmp = std::max(tmp, 1e-16);
//         xc3 = 0.5*dt*std::sqrt(tmp * T);
//         vc1 = 0.5*dt*eta;
//         vc2 = 0.5*dt;
//         vc3 = std::sqrt(tmp * T);
//     }
// };

// // Single BAOAB step
// template<typename GradFunc>
// void BAOAB_single_step(Vector2D& x, Vector2D& v, Vector2D& gradU_prev,
//                        const BAOAB_Constants& C,
//                        GradFunc grad_func)
// {
//     // A-step
//     Vector2D xi(normal_dist(gen), normal_dist(gen));
//     Vector2D x_new = x + v*C.xc1 - gradU_prev*C.xc2 + xi*C.xc3;
//     Vector2D gradU_new = grad_func(x_new);
//     // B+O step
//     Vector2D v_new = v*C.eta - gradU_prev*C.vc1 - gradU_new*C.vc2 + xi*C.vc3;
//     // update
//     x = x_new;
//     v = v_new;
//     gradU_prev = gradU_new;
// }

// // Core run function
// SimulationResult runZBAOABZ(
//     Potential pot, Kernel kern, GradientType grad_type,
//     int nmax, int nmeas,
//     double dtau, double gamma, double alpha, double eps, double sigma,
//     double scale_g, double T, double m, double M, double r,
//     std::pair<double,double> x0p,
//     std::pair<double,double> v0p,
//     double z0)
// {
//     // Unpack
//     Vector2D x(x0p.first, x0p.second);
//     Vector2D v(v0p.first, v0p.second);
    
//     // New: Create a 64-bit generator for the perturbed gradients
//     std::mt19937_64 gen64(rd());

//     // Lambdas
//     // Updated: grad_func now handles the choice between standard and perturbed
//     auto grad_func = [&](const Vector2D& p) {
//         if (grad_type == GradientType::Perturbed) {
//             switch(pot) {
//                 case Potential::Funnel:   return perturbed_grad_funnel(p, sigma, gen64);
//                 case Potential::Channel:  return perturbed_grad_channel(p, sigma, gen64);
//                 case Potential::Beale:    // Fallback for Beale as no perturbed version was provided
//                                           return grad_beale_2d(p);
//             }
//         }
//         // Standard gradient case
//         switch(pot) {
//             case Potential::Funnel:   return grad_funnel_2d(p, eps);
//             case Potential::Channel:  return grad_channel_2d(p);
//             case Potential::Beale:    return grad_beale_2d(p);
//         }
//         // Should not be reached, but as a fallback
//         return grad_channel_2d(p);
//     };

//     auto Uval = [&](const Vector2D& p) {
//         switch(pot) {
//             case Potential::Funnel:   return U_funnel_2d(p, eps);
//             case Potential::Channel:  return U_channel_2d(p);
//             case Potential::Beale:    return U_beale_2d(p);
//         }
//         return 0.0;
//     };
//     auto psi = [&](double z) {
//         return (kern==Kernel::K1)
//             ? psi_of_zeta_k1(z, m, M, r)
//             : psi_of_zeta_k2(z, m, M, r);
//     };

//     SimulationResult res;
//     res.x.reserve(nmax+1);
//     res.y.reserve(nmax+1);
//     res.dt.reserve(nmax+1);
//     res.psi.reserve(nmax+1);
//     res.Umean.reserve((nmax+1)/nmeas + 1);
//     // res.Unorm.reserve(nmax+1);

//     // Initial
//     Vector2D gradU = grad_func(x);
//     double gval = monitor_g(gradU, scale_g);
//     double zeta = z0;
//     double half_dt = 0.5*dtau;
//     double sum_psi = 0.0;
//     double sigma_zeta = 0.0;     // New: noise level
//     // double sum_psi = 0.0, sum_Upsi = 0.0;

//     // record initial
//     res.x.push_back(x.x);
//     res.y.push_back(x.y);
//     double dt0 = psi(zeta)*dtau;
//     res.dt.push_back(dt0);
//     double p0 = psi(zeta);
//     res.psi.push_back(p0);
//     // res.Unorm.push_back(gradU.norm2());

//     for(int i=0; i<nmax; ++i) {
//         // z half
//         double zhalf = z_half_step(zeta, alpha, half_dt, gval);
//         // dt
//         double real_dt = psi(zhalf)*dtau;
//         // step
//         BAOAB_single_step(x, v, gradU, BAOAB_Constants(real_dt, gamma, T), grad_func);
//         // z update
//         gval = monitor_g(gradU, scale_g);
//         zeta = z_half_step(zhalf, alpha, half_dt, gval);
//         // record
//         res.x.push_back(x.x);
//         res.y.push_back(x.y);
//         res.dt.push_back(real_dt);
//         double pi = psi(zeta);
//         res.psi.push_back(pi);
//         // res.Unorm.push_back(gradU.norm2());
//         // sum_psi    += pi;
//         // sum_Upsi   += pi * Uval(x);
//         if(((i+1)%nmeas)==0) {
//             double current_U = Uval(x);
//             double current_Tkin = 0.5 * v.norm2() * v.norm2();
//             double current_Tconf = 0.5 * x.dot(gradU);

//             if(i+1==nmeas) {
//                 sum_psi = pi;
//                 res.Umean.push_back(current_U);
//                 res.Tkin.push_back(current_Tkin);
//                 res.Tconf.push_back(current_Tconf);
//                 res.sigmas.push_back(sigma);
//             } else {
//                 double S_old = sum_psi;
//                 sum_psi += pi;
//                 double new_Umean = res.Umean.back() * (S_old / sum_psi) 
//                                  + current_U * (pi / sum_psi);
//                 res.Umean.push_back(new_Umean);
//                 double new_Tkin = res.Tkin.back() * (S_old / sum_psi) 
//                                  + current_Tkin * (pi / sum_psi);
//                 res.Tkin.push_back(new_Tkin);
//                 double new_Tconf = res.Tconf.back() * (S_old / sum_psi) 
//                                  + current_Tconf * (pi / sum_psi);
//                 res.Tconf.push_back(new_Tconf);
//                 res.sigmas.push_back(sigma);
//             }
//             if(grad_type == GradientType::Perturbed && (i+1)>10000){
//                 double Tconf_now = res.Tconf.back();
//                 double err = monitor_sigma(Tconf_now, T);
//                 // double sigma_zeta = sigma_zstep(sigma, alpha, dtau, err);
//                 sigma_zeta = sigma_zstep(sigma_zeta, alpha, dtau, err);

//                 // Optional: enforce a minimum σ > 0
//                 sigma = std::max(psi_sigma(sigma_zeta), 1e-8);
//                 res.sigmas.push_back(sigma);
//             }
//             // res.Umean.push_back(sum_Upsi / sum_psi);
//         }
//     }

//     return res;
// }



// First version
// #include "zbaoabz_core.hpp"
// #include <cmath>
// #include <random>
// #include <numeric>

// // Constants
// static std::random_device rd;
// static std::mt19937 gen(rd());
// static std::normal_distribution<double> normal_dist(0.0, 1.0);

// // Vector2D class (as in header) - with L1 and L2 norms, dot product, etc.
// struct Vector2D {
//     double x, y;
//     Vector2D() : x(0.0), y(0.0) {}
//     Vector2D(double x_, double y_) : x(x_), y(y_) {}
//     Vector2D operator+(const Vector2D& other) const { return Vector2D(x + other.x, y + other.y); }
//     Vector2D operator-(const Vector2D& other) const { return Vector2D(x - other.x, y - other.y); }
//     Vector2D operator*(double scalar)    const { return Vector2D(x * scalar, y * scalar); }
//     Vector2D operator*(const Vector2D& other) const { return Vector2D(x * other.x, y * other.y); }
//     double norm1() const { return std::abs(x) + std::abs(y); }
//     double norm2() const { return std::sqrt(x*x + y*y); }
//     double dot(const Vector2D& other) const { return x * other.x + y * other.y; }
// };

// double monitor_g(const Vector2D& gradU, double scale_g) {
//     double norm = gradU.norm2();
//     return norm / scale_g;
// }

// // Potentials
// inline double U_funnel_2d(const Vector2D& p, double eps) {
//     double x = p.x, t = p.y;
//     return 0.5 * x*x * std::exp(-t) + 0.5 * eps * (x*x + t*t);
// }

// inline double U_channel_2d(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     double x2 = x*x, x4 = x2*x2;
//     return 100.0 * y*y/(1+10*x4) + 0.001 * (x2-9)*(x2-9);
// }

// inline double U_beale_2d(const Vector2D& p) {
//     double x = p.x, y = p.y;
//     constexpr double c1=1.5, c2=2.25, c3=2.625;
//     double xy=y*x;
//     double t1=c1 - x + x*y;
//     double t2=c2 - x + x*y*y;
//     double t3=c3 - x + x*y*y*y;
//     return t1*t1 + t2*t2 + t3*t3;
// }

// // Gradients
// inline Vector2D grad_funnel_2d(const Vector2D& p, double eps) {
//     double x=p.x, t=p.y;
//     double gx = x*std::exp(-t) + eps*x;
//     double gy = -0.5*x*x*std::exp(-t) + eps*t;
//     return Vector2D(gx, gy);
// }

// inline Vector2D grad_channel_2d(const Vector2D& p) {
//     double x=p.x, y=p.y;
//     double x2=x*x, x4=x2*x2;
//     double den = (1+10*x4);
//     double gx = -4000.0*x*x2*y*y/(den*den) + 0.004*x*(x2-9.0);
//     double gy = 200.0*y/den;
//     return Vector2D(gx, gy);
// }

// inline Vector2D grad_beale_2d(const Vector2D& p) {
//     double x=p.x, y=p.y;
//     constexpr double c1=1.5, c2=2.25, c3=2.625;
//     double t1=c1 - x + x*y;
//     double t2=c2 - x + x*y*y;
//     double t3=c3 - x + x*y*y*y;
//     double gx = 2*t1*(-1+y) + 2*t2*(-1+y*y) + 2*t3*(-1+y*y*y);
//     double gy = 2*t1*x   + 2*t2*2*x*y   + 2*t3*3*x*y*y;
//     return Vector2D(gx, gy);
// }

// // Sundman kernels
// double psi_of_zeta_k1(double z, double m, double M, double r) {
//     double zr = std::pow(z, r);
//     return m * (zr + M) / (zr + m);
// }

// double psi_of_zeta_k2(double z, double m, double M, double r) {
//     double zr = std::pow(z, r);
//     return m * (zr + M/m) / (zr + 1.0);
// }

// // Half-step for zeta
// double z_half_step(double z_old, double alpha, double half_dtau, double gval) {
//     double rho = std::exp(-alpha * half_dtau);
//     return rho * z_old + (1.0 - rho)/alpha * gval;
// }

// // BAOAB Constants
// struct BAOAB_Constants {
//     double eta, xc1, xc2, xc3, vc1, vc2, vc3;
//     BAOAB_Constants(double dt, double gamma, double T=1.0) {
//         double gh = gamma * dt;
//         eta = std::exp(-gh);
//         xc1 = 0.5*dt*(1+eta);
//         xc2 = 0.25*dt*dt*(1+eta);
//         double tmp = -std::expm1(-2.0*gh);
//         tmp = std::max(tmp, 1e-16);
//         xc3 = 0.5*dt*std::sqrt(tmp * T);
//         vc1 = 0.5*dt*eta;
//         vc2 = 0.5*dt;
//         vc3 = std::sqrt(tmp * T);
//     }
// };

// // Single BAOAB step
// template<typename GradFunc>
// void BAOAB_single_step(Vector2D& x, Vector2D& v, Vector2D& gradU_prev,
//                        const BAOAB_Constants& C, double eps,
//                        GradFunc grad_func)
// {
//     // A-step
//     Vector2D xi(normal_dist(gen), normal_dist(gen));
//     Vector2D x_new = x + v*C.xc1 - gradU_prev*C.xc2 + xi*C.xc3;
//     Vector2D gradU_new = grad_func(x_new);
//     // B+O step
//     Vector2D v_new = v*C.eta - gradU_prev*C.vc1 - gradU_new*C.vc2 + xi*C.vc3;
//     // update
//     x = x_new;
//     v = v_new;
//     gradU_prev = gradU_new;
// }

// // Core run function
// SimulationResult runZBAOABZ(
//     Potential pot, Kernel kern,
//     int nmax, int nmeas,
//     double dtau, double gamma, double alpha, double eps,
//     double scale_g, double T, double m, double M, double r,
//     std::pair<double,double> x0p,
//     std::pair<double,double> v0p,
//     double z0)
// {
//     // Unpack
//     Vector2D x(x0p.first, x0p.second);
//     Vector2D v(v0p.first, v0p.second);
//     // Lambdas
//     auto grad_func = [&](const Vector2D& p) {
//         switch(pot) {
//             case Potential::Funnel:  return grad_funnel_2d(p, eps);
//             case Potential::Channel: return grad_channel_2d(p);
//             case Potential::Beale:   return grad_beale_2d(p);
//         }
//         return grad_channel_2d(p);
//     };
//     auto Uval = [&](const Vector2D& p) {
//         switch(pot) {
//             case Potential::Funnel:  return U_funnel_2d(p, eps);
//             case Potential::Channel: return U_channel_2d(p);
//             case Potential::Beale:   return U_beale_2d(p);
//         }
//         return 0.0;
//     };
//     auto psi = [&](double z) {
//         return (kern==Kernel::K1)
//             ? psi_of_zeta_k1(z, m, M, r)
//             : psi_of_zeta_k2(z, m, M, r);
//     };

//     SimulationResult res;
//     res.x.reserve(nmax+1);
//     res.y.reserve(nmax+1);
//     res.dt.reserve(nmax+1);
//     res.psi.reserve(nmax+1);
//     res.Umean.reserve((nmax+1)/nmeas + 1);

//     // Initial
//     Vector2D gradU = grad_func(x);
//     double gval = monitor_g(gradU, scale_g);
//     double zeta = z0;
//     double half_dt = 0.5*dtau;
//     double sum_psi = 0.0, sum_Upsi = 0.0;

//     // record initial
//     res.x.push_back(x.x);
//     res.y.push_back(x.y);
//     double dt0 = psi(zeta)*dtau;
//     res.dt.push_back(dt0);
//     double p0 = psi(zeta);
//     res.psi.push_back(p0);

//     for(int i=0; i<nmax; ++i) {
//         // z half
//         double zhalf = z_half_step(zeta, alpha, half_dt, gval);
//         // dt
//         double real_dt = psi(zhalf)*dtau;
//         // step
//         BAOAB_single_step(x, v, gradU, BAOAB_Constants(real_dt, gamma, T), eps, grad_func);
//         // z update
//         gval = monitor_g(gradU, scale_g);
//         zeta = z_half_step(zhalf, alpha, half_dt, gval);
//         // record
//         res.x.push_back(x.x);
//         res.y.push_back(x.y);
//         res.dt.push_back(real_dt);
//         double pi = psi(zeta);
//         res.psi.push_back(pi);
//         sum_psi    += pi;
//         sum_Upsi   += pi * Uval(x);
//         if(((i+1)%nmeas)==0) {
//             res.Umean.push_back(sum_Upsi / sum_psi);
//         }
//     }

//     return res;
// }
