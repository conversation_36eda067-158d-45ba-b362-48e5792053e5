{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "bGU6NwlsXFSt"}, "outputs": [], "source": ["#@title Import Dependencies\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.datasets as dsets\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import itertools\n", "import pickle\n", "import numpy as np\n", "from numpy import random\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "torch.set_float32_matmul_precision('high')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from PIL import Image, ImageOps\n", "from typing import TypeVar, Dict\n", "\n", "from torch.optim.optimizer import Optimizer\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "\n", "from argparse import Namespace\n", "import ast\n", "import os\n", "\n", "import torchvision.transforms as transforms\n", "from torch.autograd import Variable\n", "import torch.nn.functional as F\n", "from typing import TypeVar, <PERSON><PERSON>\n", "\n", "import copy\n", "import gc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["img_size = 180\n", "input_size = img_size*img_size*1 \n", "batch_size = 200 # the size of input data took for one iteration\n", "\n", "labels = ['PNEUMONIA', 'NORMAL']\n", "transform_aug = transforms.Compose([transforms.RandomResizedCrop((img_size,img_size),antialias=True),transforms.RandomRotation(20), transforms.RandomHorizontalFlip(p=0.5), transforms.ToTensor()])\n", "transform = transforms.Compose([transforms.Resize((img_size,img_size),antialias=True),transforms.ToTensor()])\n", "def get_training_data_aug(data_dir,augment_ratio):\n", "    data_list = [] \n", "    for label in labels: \n", "        path = os.path.join(data_dir, label)\n", "        class_num = labels.index(label)\n", "        for img in os.listdir(path):\n", "            for it in range(augment_ratio[class_num]):\n", "                try:\n", "                    img_arr = Image.open(os.path.join(path, img))\n", "                    img_arr= ImageOps.grayscale(img_arr)\n", "                    resized_arr = transform_aug(img_arr)\n", "                    # Reshaping images to preferred size and grayscale transform\n", "                    data_list.append([resized_arr,class_num])\n", "                except Exception as e:\n", "                    print(e)\n", "    return data_list\n", "\n", "def get_training_data(data_dir):\n", "    data_list = [] \n", "    for label in labels: \n", "        path = os.path.join(data_dir, label)\n", "        class_num = labels.index(label)\n", "        for img in os.listdir(path):\n", "            try:\n", "                img_arr = Image.open(os.path.join(path, img))\n", "                img_arr= ImageOps.grayscale(img_arr)\n", "                resized_arr = transform(img_arr)\n", "                # Reshaping images to preferred size and grayscale transform\n", "                data_list.append([resized_arr,class_num])\n", "            except Exception as e:\n", "                print(e)\n", "    return data_list\n", "\n", "train_list = get_training_data_aug(\"./data/chest_xray/train\",[6,18])\n", "test_list = get_training_data(\"./data/chest_xray/test\")\n", "random.shuffle(train_list)\n", "random.shuffle(test_list)\n", "no_batches=len(train_list)//batch_size\n", "test_no_batches=len(test_list)//batch_size\n", "train_data_len=no_batches*batch_size\n", "test_data_len=len(test_list)\n", "test_data_len=test_no_batches*batch_size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_to_image_list(data_list):\n", "    images_list=[]\n", "    labels_list=[]\n", "    no_batches=len(data_list)//batch_size\n", "\n", "    for i in range(no_batches):\n", "        images_list.append(torch.zeros(batch_size,1,img_size,img_size).detach().cuda())\n", "        labels= torch.zeros(batch_size)\n", "        labels = labels.type(torch.LongTensor).cuda().detach()\n", "        labels_list.append(labels)\n", "        for it in range(batch_size):\n", "            images_list[i][it,:,:,:]=data_list[i*batch_size+it][0]\n", "            labels_list[i][it]=data_list[i*batch_size+it][1]\n", "    return images_list,labels_list    \n", "\n", "images_list, labels_list = convert_to_image_list(train_list)\n", "del train_list\n", "test_images_list, test_labels_list = convert_to_image_list(test_list)\n", "del test_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filepath=\"xray_data.pickle\"\n", "# with open(filepath,\"wb\") as file:\n", "#     pickle.dump([images_list,labels_list,test_labels_list,test_images_list,no_batches,test_no_batches,batch_size,train_data_len,test_data_len],file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_bNfVLRUYqZA"}, "outputs": [], "source": ["\n", "# filepath=\"xray_data.pickle\"\n", "# with open(filepath,\"rb\") as file:    \n", "#     [images_list,labels_list,test_labels_list,test_images_list,no_batches,test_no_batches,batch_size,train_data_len,test_data_len]=pickle.load(file)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fL-Y<PERSON><PERSON><PERSON><PERSON>_"}, "outputs": [], "source": ["#@title Define model class\n", "import torch.nn.functional as F\n", "\n", "from typing import TypeVar, <PERSON><PERSON>\n", "\n", "Tensor = TypeVar('torch.tensor')\n", "\n", "class NeuralNet(torch.nn.Module):\n", "    \"\"\"\n", "    base class for all NN classifiers\n", "    \"\"\"\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def initialize_weights(self):\n", "        for m in self.modules():\n", "            if isinstance(m, torch.nn.Conv2d):\n", "                torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None:\n", "                    torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.BatchNorm2d):\n", "                torch.nn.init.constant_(m.weight, 1)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, torch.nn.Linear):\n", "                torch.nn.init.normal_(m.weight, 0, 0.01)\n", "                torch.nn.init.constant_(m.bias, 0)\n", "\n", "class chestxray_CNNnew(NeuralNet):\n", "\n", "    def __init__(self,\n", "                 flattened_size: int = 12544,\n", "                 low_rank: int = 32,\n", "                 batch_norm_mom: float = 1.0):\n", "        \"\"\"CNN Builder.\"\"\"\n", "        super(<PERSON><PERSON><PERSON>_<PERSON>, self).__init__()\n", "\n", "        self.conv_layer = nn.Sequential(\n", "            nn.Conv2d(in_channels=1, out_channels=32, kernel_size=3, stride=1, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2, padding=1),\n", "        # Second Convolutional Block\n", "            nn.BatchNorm2d(32,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=3, stride=1, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2, padding=1),        \n", "        # Third Convolutional Block\n", "            nn.BatchNorm2d(64,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=3, stride=1, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2, padding=1),\n", "        \n", "        # Fourth Convolutional Block\n", "            nn.BatchNorm2d(64,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, stride=1, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2, padding=1),      \n", "        # Fifth Convolutional Block\n", "            nn.BatchNorm2d(128,momentum=batch_norm_mom),\n", "            nn.Conv2d(in_channels=128, out_channels=256, kernel_size=3, stride=1, padding=1),\n", "            nn.Softplus(beta=1.0),\n", "            nn.MaxPool2d(kernel_size=2, stride=2, padding=1),\n", "        )\n", "\n", "        self.fc_layer = nn.Sequential(\n", "            nn.BatchNorm1d(flattened_size,momentum=batch_norm_mom),\n", "            nn.Linear(flattened_size, low_rank),\n", "            nn.BatchNorm1d(low_rank,momentum=batch_norm_mom),\n", "            nn.<PERSON><PERSON>(low_rank, 512),            \n", "            nn.Softplus(beta=1.0), \n", "            nn.BatchNorm1d(512,momentum=batch_norm_mom),\n", "        )\n", "\n", "        self.last_layer=nn.Sequential(\n", "            nn.<PERSON><PERSON>(512, 2)\n", "        )\n", "\n", "    def forward(self, x: Tensor) -> Tensor:\n", "        \"\"\"Perform forward.\"\"\"\n", "\n", "        # conv layers\n", "        x = self.conv_layer(x)\n", "\n", "        # flatten\n", "        x = x.view(x.size(0), -1)\n", "\n", "        # fc layer\n", "        x = self.fc_layer(x)\n", "\n", "        x=self.last_layer(x)\n", "\n", "        return x\n", "\n", "    def classify(self, x: Tensor) -> <PERSON><PERSON>[Tensor, Tensor, Tensor]:\n", "        net_out = self.forward(x)\n", "        acc = F.softmax(net_out, dim=1)\n", "        class_idx = torch.max(net_out, 1)[1]\n", "\n", "        return acc, acc[0, class_idx], class_idx\n", "    \n", "\n", "\n", "class Net(nn.Module):\n", "  def __init__(self, input_size, hidden_size, num_classes):\n", "    super(Net,self).__init__()\n", "    self.fc1 = nn.Linear(input_size, hidden_size)\n", "    self.relu = nn.ReLU()\n", "    self.fc2 = nn.Linear(hidden_size, num_classes)\n", "\n", "  def forward(self,x):\n", "    out = self.fc1(x)\n", "    out = self.relu(out)\n", "    out = self.fc2(out)\n", "    return out"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ePLIwvAFj2zH"}, "outputs": [], "source": ["#@title Define loss-function & optimizer\n", "loss_function = nn.CrossEntropyLoss()\n", "\n", "\n", "def images_regulariser(net): \n", "    li_reg_loss = 0\n", "    penalized     = [p for name,p in net.named_parameters() if 'bias' not in name]\n", "    not_penalized = [p for name,p in net.named_parameters() if 'bias' in name]\n", "    for p in penalized:\n", "        li_reg_loss += (p**2).sum()*0.5\n", "    #for p in net.parameters():\n", "#        li_reg_loss += (p**2).sum()*0.5\n", "    reg=li_reg_loss/(train_data_len)*l2regconst\n", "    return(reg)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def addnet(net,net2):\n", "    for param1, param2 in zip(net.parameters(), net2.parameters()):\n", "     param1.data += param2.data\n", "\n", "def multiplynet(net,a):\n", "   for param1 in net.parameters():\n", "     param1.data *=a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "@dataclass\n", "class hclass:\n", "    h: <PERSON><PERSON>\n", "    eta: Tensor\n", "    etam1g: Tensor\n", "    c11: Tensor\n", "    c21: Tensor\n", "    c22: Tensor\n", "\n", "def hper2const(h,gam):\n", "    gh=gam.double()*h.double()\n", "    s=torch.sqrt(4*torch.expm1(-gh/2)-torch.expm1(-gh)+gh)\n", "    eta=(torch.exp(-gh/2)).float()\n", "    etam1g=((-torch.expm1(-gh/2))/gam.double()).float()\n", "    c11=(s/gam).float()\n", "    c21=(torch.exp(-gh)*(torch.expm1(gh/2.0))**2/s).float()\n", "    c22=(torch.sqrt(8*torch.expm1(-gh/2)-4*torch.expm1(-gh)-gh*torch.expm1(-gh))/s).float()\n", "    hc=hclass(h=h,eta=eta,etam1g=etam1g,c11=c11,c21=c21,c22=c22)\n", "    return(hc)\n", "\n", "def U(x,v,hc):\n", "    xi1=torch.randn(x.size(),device=device)\n", "    xi2=torch.randn(x.size(),device=device)\n", "\n", "    xn=x+hc.etam1g*v+hc.c11*xi1\n", "    vn=v*hc.eta+hc.c21*xi1+hc.c22*xi2\n", "    return([xn, vn])\n", "\n", "def bounce(x,v,xstar,width):\n", "    vsign=(((x-xstar+width)/(2*width)).floor()% 2)*(-2)+1\n", "    vn=v*vsign\n", "    xn=((x-xstar-width)% (4*width)-2*width).abs()-width+xstar\n", "    # num_outside=((xn-xstar)>width).sum()+((xstar-xn)>width).sum()\n", "    # if(num_outside>0):\n", "    #     print(num_outside)    \n", "    return([xn, vn])\n", "\n", "def bouncenet(net):\n", "    for p,p_star in zip(net.parameters(),net_star.parameters()):\n", "        [p.data, p.v]=bounce(p.data, p.v, p_star.data, 6/torch.sqrt(l2regconst_extra))\n", "\n", "def UBU_step(net,hper2c,images,labels,batch_it):   \n", "    with torch.no_grad():\n", "        for p in list(net.parameters()):\n", "\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "        bouncenet(net)\n", "\n", "    outputsU = net(images)\n", "    loss_likelihood = loss_function(outputsU, labels)  \n", "\n", "\n", "    grads_reg=[torch.zeros_like(par) for par in net.parameters()]\n", "    net_pars=list(net.parameters())\n", "    with torch.no_grad():\n", "        for it in range(len_params):\n", "            if(list_no_bias[it]):\n", "                grads_reg[it]=net_pars[it].data*l2regconst\n", "\n", "    net.zero_grad()\n", "    #loss.backward()\n", "    loss_likelihood.backward()\n", "    with torch.no_grad():\n", "        grads_likelihood=[par.grad*batch_size for par in net.parameters()]\n", "    \n", "        #Normal, no variance reduction\n", "        # for p,p_star in zip(net.parameters(),net_star.parameters()):      \n", "        #     p.v-=hper2c.h*(p.grad*train_data_len+l2regconst_extra*(p.data-p_star.data))\n", "\n", "        for p,grad,grad_reg,p_star,grad_star,star_sum_grad in zip(list(net.parameters()),grads_likelihood,grads_reg,list(net_star.parameters()),net_star_grad_list[batch_it],net_star_full_grad):              \n", "            #Using variance reduction\n", "            p.v-=hper2c.h*(grad_reg+star_sum_grad+(grad-grad_star.cuda())*no_batches+l2regconst_extra*(p.data-p_star.data))\n", "\n", "        for p in list(net.parameters()):\n", "            [p.data,p.v]=U(p.data,p.v,hper2c)\n", "\n", "    #bouncenet()\n", "    return(loss_likelihood.data)\n", "\n", "def ind_create(batch_it):\n", "    modit=batch_it %(2*no_batches)\n", "    ind=(modit<=(no_batches-1))*modit+(modit>=no_batches)*(2*no_batches-modit-1)\n", "    return ind"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "u75Xa5VckuTH"}, "outputs": [], "source": ["#@title Output arrays\n", "par_runs=64\n", "num_classes=2\n", "num_epochs=60\n", "switch_to_sampling_epoch=20\n", "switch_to_swa_epoch=15\n", "\n", "\n", "num_swa_epochs=switch_to_sampling_epoch-switch_to_swa_epoch\n", "\n", "training_size=no_batches*batch_size\n", "test_size=test_data_len\n", "labels_arr=torch.zeros(training_size)\n", "test_labels_arr=torch.zeros(test_size)\n", "test_prob_arr=torch.zeros([test_size,num_classes,num_epochs,par_runs])\n", "\n", "lr = 1e-2\n", "lr_swa=1e-3\n", "h=5e-4\n", "l2regconst=torch.tensor(1).detach()\n", "l2regconst_extra=torch.tensor(50).detach()\n", "gam=torch.sqrt(l2regconst_extra)\n", "hper2c=hper2const(torch.tensor(h/2),gam)\n", "\n", "chestxray_CNNnew=torch.compile(chestxray_CNNnew)\n", "images_regulariser=torch.compile(images_regulariser)\n", "loss_function=torch.compile(loss_function)\n", "UBU_step=torch.compile(UBU_step)\n", "\n", "for par_it in range(par_runs):\n", "  print(\"par_it:\",par_it,\"\\n\")\n", "  #@title Build the model\n", "  net = chestxray_CNNnew().cuda()\n", "  net.train()\n", "  optimizer = torch.optim.Adam( net.parameters(), lr=lr)\n", "  \n", "  lr_scheduler = torch.optim.lr_scheduler.PolynomialLR(optimizer=optimizer, total_iters=switch_to_swa_epoch,power=1)\n", "\n", "\n", "  #@title Training the model\n", "\n", "  for epoch in range(num_epochs):\n", "    sum_loss=torch.zeros(1).cuda().detach()\n", "    #l2regconst=torch.min(torch.tensor(1+epoch),torch.tensor(switch_to_swa_epoch)).detach()\n", "    # if(epoch<switch_to_sampling_epoch):\n", "    net.train()\n", "    if(epoch==(switch_to_swa_epoch-1)):\n", "      net2=copy.deepcopy(net)\n", "      multiplynet(net2,0)\n", "      optimizer=torch.optim.Adam(net.parameters(),lr=lr_swa)\n", "\n", "    if(epoch>=switch_to_sampling_epoch and (epoch-switch_to_sampling_epoch)%2==0):\n", "        rperm=random.permutation(list(range(no_batches)))\n", "    \n", "    for i in range(no_batches): \n", "      if(epoch<switch_to_sampling_epoch):\n", "        b=torch.randint(high=no_batches,size=(1,1))\n", "      else:\n", "        it=(epoch-switch_to_sampling_epoch)*no_batches+i\n", "        b=rperm[ind_create(it)]\n", "        \n", "      images=images_list[b].detach()\n", "      labels=labels_list[b].detach()\n", "      \n", "\n", "      if(epoch<switch_to_sampling_epoch):\n", "        outputs = net(images)    \n", "        loss_likelihood = loss_function(outputs, labels)\n", "        sum_loss=sum_loss+loss_likelihood.detach()   \n", "        reg=images_regulariser(net)\n", "        loss=loss_likelihood+reg\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        if(epoch>=(switch_to_swa_epoch)):\n", "          addnet(net2,net)\n", "      else:\n", "        loss_likelihood=UBU_step(net,hper2c,images,labels,b)\n", "        sum_loss=sum_loss+loss_likelihood.detach()\n", "\n", "\n", "\n", "\n", "    #if (i+1) % (no_batches) == 0:\n", "    #print(\"Reg:\",reg)\n", "    print('Epoch [%d/%d], Step [%d/%d]' %(epoch+1, num_epochs, i+1, no_batches))\n", "    correct = 0\n", "    total = 0\n", "    \n", "\n", "\n", "\n", "\n", "\n", "    if epoch==(switch_to_sampling_epoch-1):\n", "      multiplynet(net2,1/(num_swa_epochs*no_batches))\n", "      multiplynet(net,0)\n", "      addnet(net,net2)\n", "      del net2\n", "      gc.collect()\n", "\n", "      len_params=len(list(net.parameters()))\n", "      net_star=copy.deepcopy(net)\n", "\n", "      #Variance reduction - saving gradients at each batch at x_star\n", "      net_star_grad_list=[]\n", "      net_star_full_grad=[torch.zeros_like(par, device=device) for par in list(net.parameters())]\n", "      for i in range(no_batches):\n", "          images=images_list[i]\n", "          labels=labels_list[i]\n", "          outputs=net(images)\n", "          loss_likelihood = loss_function(outputs, labels)\n", "          reg=images_regulariser(net)\n", "          net.zero_grad()\n", "          loss_likelihood.backward()\n", "          with torch.no_grad():\n", "            grads=[(par.grad*batch_size).cpu() for par in list(net.parameters())]\n", "            net_star_grad_list.append(grads)\n", "            for g, gi in zip(net_star_full_grad,grads):\n", "              g+=gi.cuda()\n", "                      \n", "      len_params=len(list(net.parameters()))\n", "      list_no_bias=torch.zeros(len_params)\n", "      pit=0\n", "      for name, p in net_star.named_parameters():\n", "          if 'bias' not in name:\n", "              list_no_bias[pit]=1.0\n", "          pit+=1\n", "\n", "      #Initialise velocities\n", "      for par in list(net.parameters()):\n", "        par.v = torch.randn_like(par,device=device).detach()      \n", "    with torch.no_grad():\n", "      net(torch.cat(images_list[0:10],dim=0).detach())\n", "      net.eval()\n", "      for testit in range(test_no_batches):\n", "        imagest=test_images_list[testit]\n", "        labelst=test_labels_list[testit]\n", "        actual_batch_size=len(imagest)\n", "        test_labels_arr[(testit*batch_size):(testit*batch_size+actual_batch_size)]=labelst.detach().cpu()\n", "        outputt = net(imagest).detach()#.reshape(actual_batch_size).detach()\n", "        _, predictedt = torch.max(outputt,1)\n", "\n", "        correct += (predictedt == labelst).sum()\n", "        total += labelst.size(0)\n", "\n", "        test_prob_arr[(testit*batch_size):(testit*batch_size+actual_batch_size),:,epoch,par_it]=torch.softmax(outputt,dim=1)\n", "    \n", "\n", "    print('Test accuracy of the model: %.3f %%' %((100*correct)/(total+1)))\n", "\n", "    if(epoch<=switch_to_swa_epoch):\n", "      lr_scheduler.step()\n", "    print('Epoch [%d], Average Loss: %0.4f' %(epoch+1, sum_loss/no_batches))\n", "  \n", "  \n", "  filepath=\"output_chest_xray_rand.pickle\"\n", "  with open(filepath,\"wb\") as file:\n", "    pickle.dump([labels_arr.numpy(),test_labels_arr.numpy(),test_prob_arr.numpy()],file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def rps_single(probs, true_label,num_classes):\n", "    outcome=torch.zeros(num_classes)\n", "    outcome[true_label.int()]=1.0\n", "    cum_probs = torch.cumsum(probs,0)\n", "    cum_outcomes = torch.cumsum(outcome,0)\n", "    \n", "    sum_rps = 0\n", "    for i in range(len(outcome)):         \n", "        sum_rps+= (cum_probs[i] - cum_outcomes[i])**2\n", "    \n", "    return sum_rps/(num_classes-1)\n", "\n", "def rps_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    rps_vec=torch.zeros(test_data_len)\n", "    for it in range(test_data_len):\n", "        rps_vec[it]=rps_single(test_probs[it,:].reshape(num_classes),true_labels[it],num_classes)\n", "    return rps_vec\n", "\n", "def nll_calc(test_probs, true_labels,test_data_len,num_classes):\n", "    res=0\n", "    for it in range(test_data_len):\n", "        res-=torch.max(torch.tensor([torch.log(test_probs[it,true_labels[it].int()]),-100]))\n", "    return res/test_data_len\n", "\n", "\n", "def adaptive_calibration_error(test_probs,true_labels, test_data_len, num_classes,num_bins=20):\n", "    o=torch.tensor(0.0).detach()\n", "    for k in range(num_classes):\n", "        ind=torch.argsort(test_probs[:,k],stable=True)        \n", "        testprobsk=test_probs[:,k]\n", "        sorted_probs=testprobsk[ind]\n", "        sorted_true_labels=true_labels[ind]\n", "\n", "        true_label_is_k = (sorted_true_labels==k).clone().detach().float()\n", "        bins=(torch.tensor(range(test_data_len))/torch.tensor(test_data_len/num_bins)).floor()\n", "\n", "        for b in range(num_bins):\n", "            mask = (bins == b)\n", "            if torch.any(mask):\n", "                o += (true_label_is_k[mask] - sorted_probs[mask]).mean().abs()\n", "\n", "    return o / (num_bins*num_classes)\n", "\n", "\n", "def compute_acc_ace_rps_no_bayes(es,par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_swa_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Non-Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "def compute_acc_ace_rps_swa(es,par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr):\n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,switch_to_sampling_epoch-1,it*es:(it+1)*es]).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"SWA, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n", "\n", "#Bayesian\n", "def compute_acc_ace_rps_bayes(es,par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr): \n", "    copies=int(par_runs/es)\n", "    ace_arr=torch.zeros(copies)\n", "    rps_arr=torch.zeros(copies)\n", "    nll_arr=torch.zeros(copies)\n", "    accuracy_arr=torch.zeros(copies)\n", "\n", "    for it in range(copies):\n", "        test_prob=torch.Tensor(test_prob_arr[:,:,(switch_to_sampling_epoch+burnin_epochs):num_epochs,it*es:(it+1)*es]).mean(-1).mean(-1).reshape(test_data_len,num_classes)\n", "        ace_arr[it]=adaptive_calibration_error(test_prob,test_labels_arr,test_data_len, num_classes)\n", "        rps_arr[it]=(rps_calc(test_prob, test_labels_arr,test_data_len,num_classes)).mean()\n", "        nll_arr[it]=nll_calc(test_prob, test_labels_arr,test_data_len,num_classes)\n", "        _, predictedt = torch.max(test_prob,1)\n", "        accuracy_arr[it]= (predictedt==test_labels_arr.reshape(1,test_data_len)).sum()/test_data_len\n", "    print(\"Bayesian, ensemble size:\", es)\n", "    print(\"mean accuracy:\",accuracy_arr.mean(),\"std:\",accuracy_arr.std())\n", "    print(\"mean ace:\",ace_arr.mean(),\"std:\",ace_arr.std())\n", "    print(\"mean nll:\",nll_arr.mean(),\"std:\",nll_arr.std())\n", "    print(\"mean rps:\",rps_arr.mean(),\"std:\",rps_arr.std())\n", "    return [accuracy_arr.mean(),accuracy_arr.std(),ace_arr.mean(),ace_arr.std(),rps_arr.mean(),rps_arr.std(),nll_arr.mean(),nll_arr.std()]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#no bayesian\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_no_bayes(pow(2,it),par_runs,switch_to_swa_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_xray_no_bayes_SMS_UBU.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "     \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#SWA\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_swa(pow(2,it),par_runs,switch_to_sampling_epoch,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n", "cal_swa=[acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std]\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_swa_SMS_UBU.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Bayesian\n", "burnin_epochs=10\n", "acc,acc_std,ace,ace_std,rps,rps_std,nll,nll_std=(torch.zeros(5),torch.zeros(5),\n", "torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5),torch.zeros(5))\n", "for it in range(5):\n", "     [acc[it],acc_std[it],ace[it],ace_std[it],rps[it],rps_std[0],nll[it],nll_std[it]]=\\\n", "     compute_acc_ace_rps_bayes(pow(2,it),par_runs,switch_to_sampling_epoch,burnin_epochs,num_epochs,test_data_len,num_classes,test_prob_arr,test_labels_arr)\n", "\n", "from scipy.io import savemat\n", "filepath=\"results_fashion_bayes_SMS_UBU.mat\"\n", "mdic={\"acc\":acc.cpu().numpy(),\"acc_std\":acc_std.cpu().numpy(),\"nll\": nll.cpu().numpy(),\"nll_std\":nll_std.cpu().numpy(),\\\n", "      \"ace\": ace.cpu().numpy(),\"ace_std\":ace_std.cpu().numpy(), \"rps\":rps.cpu().numpy(),\"rps_std\":rps_std.cpu().numpy()}\n", "savemat(filepath,mdic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import torchvision\n", "# from torchview import draw_graph\n", "# net2 = chestxray_CNNnew()\n", "\n", "# #model_graph = draw_graph(net2, input_size=(1,3,64,64), expand_nested=True)\n", "# model_graph = draw_graph(net2, input_size=(1,1,180,180), expand_nested=True)\n", "# model_graph.visual_graph\n"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Pytorch MNIST.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}