figure1: 
burn_in_noise   = 10000; % Start adapting sigma after this many steps
initial_sigma   = 4.0;   % Starting noise level
sigma_min       = 0.1;   % Min allowed noise (like a large batch)
sigma_max       = 4.0;   % Max allowed noise (like a small batch)
alpha_sigma     = 0.01;  % Memory for noise control (smaller is more memory)
threshold_sigma = 0.05;  % 5% acceptable temperature gap
steepness_sigma = 1.0;   % Steepness of the sigmoid transition


figure2:
burn_in_noise   = 10000; % Start adapting sigma after this many steps
initial_sigma   = 4.0;   % Starting noise level
sigma_min       = 0.1;   % Min allowed noise (like a large batch)
sigma_max       = 4.0;   % Max allowed noise (like a small batch)
alpha_sigma     = 0.01;  % Memory for noise control (smaller is more memory)
threshold_sigma = 0.05;  % 5% acceptable temperature gap
steepness_sigma = 0.1;   % Steepness of the sigmoid transition


figure3:
burn_in_noise   = 10000; % Start adapting sigma after this many steps
initial_sigma   = 4.0;   % Starting noise level
sigma_min       = 0.1;   % Min allowed noise (like a large batch)
sigma_max       = 4.0;   % Max allowed noise (like a small batch)
alpha_sigma     = 0.015;  % Memory for noise control (smaller is more memory)
threshold_sigma = 0.05;  % 5% acceptable temperature gap
steepness_sigma = 0.01;   % Steepness of the sigmoid transition


figure4:
burn_in_noise   = 10000; % Start adapting sigma after this many steps
initial_sigma   = 4.0;   % Starting noise level
sigma_min       = 0.1;   % Min allowed noise (like a large batch)
sigma_max       = 4.0;   % Max allowed noise (like a small batch)
alpha_sigma     = 0.015;  % Memory for noise control (smaller is more memory)
threshold_sigma = 0.0;  % 5% acceptable temperature gap
steepness_sigma = 0.01;   % Steepness of the sigmoid transition


figure5:
burn_in_noise   = 10000; % Start adapting sigma after this many steps
initial_sigma   = 4.0;   % Starting noise level
sigma_min       = 0.1;   % Min allowed noise (like a large batch)
sigma_max       = 4.0;   % Max allowed noise (like a small batch)
alpha_sigma     = 0.015;  % Memory for noise control (smaller is more memory)
threshold_sigma = 0.1;  % 5% acceptable temperature gap
steepness_sigma = 0.01;   % Steepness of the sigmoid transition


figure6:
sigma = 1.24;